import { ComponentConfig } from '@measured/puck';
import { ReactNode } from 'react';

// Theme Configuration Types
export interface ThemeConfig {
  id?: string;
  name: string;
  description?: string;
  version: string;
  
  // Brand Settings
  brand: {
    name: string;
    logo?: string;
    favicon?: string;
  };

  // Color System
  colors: {
    primary: {
      main: string;
      light: string;
      dark: string;
      contrast: string;
    };
    secondary: {
      main: string;
      light: string;
      dark: string;
      contrast: string;
    };
    accent: {
      main: string;
      light: string;
      dark: string;
      contrast: string;
    };
    background: {
      default: string;
      paper: string;
      surface: string;
    };
    text: {
      primary: string;
      secondary: string;
      disabled: string;
    };
    border: {
      default: string;
      light: string;
      dark: string;
    };
    status: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  };

  // Typography System
  typography: {
    fontFamily: {
      primary: string;
      secondary?: string;
      mono?: string;
    };
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
    };
    fontWeight: {
      light: number;
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };

  // Spacing System
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
  };

  // Border Radius
  borderRadius: {
    none: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
  };

  // Shadows
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };

  // Layout Settings
  layout: {
    maxWidth: string;
    containerPadding: string;
    header: {
      height: string;
      sticky: boolean;
      background: string;
    };
    footer: {
      background: string;
      columns: number;
    };
  };

  // Component Overrides
  components: {
    [componentName: string]: ComponentThemeConfig;
  };

  // Accessibility Settings
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    focusVisible: boolean;
  };

  // Custom CSS Variables
  customVariables?: Record<string, string>;
}

// Component Theme Configuration
export interface ComponentThemeConfig {
  defaultProps?: Record<string, any>;
  styleOverrides?: Record<string, any>;
  variants?: Record<string, any>;
}

// ZMP Component Definition
export interface ZMPComponent {
  type: string;
  label: string;
  icon?: ReactNode;
  category: 'layout' | 'content' | 'form' | 'navigation' | 'media' | 'custom';
  props: Record<string, any>;
  defaultProps?: Record<string, any>;
  render: (props: any) => ReactNode;
}

// Puck Configuration
export interface PuckConfig extends ComponentConfig {
  components: Record<string, ZMPComponent>;
  categories?: {
    [categoryName: string]: {
      components: string[];
      title?: string;
      visible?: boolean;
    };
  };
}

// Theme Builder Props
export interface HeaderAction {
  type: 'button' | 'custom';
  label: string;
  icon?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

export interface ThemeBuilderProps {
  config?: PuckConfig;
  data?: any;
  onChange?: (data: any) => void; // Auto-save callback
  onPreview?: (data: any) => void;
  onPublish?: (data: any) => void;
  showThemePanel?: boolean;
  showPreview?: boolean;
  viewports?: Array<{
    width: number;
    height?: number;
    label: string;
    icon?: string;
  }>;
  iframe?: {
    enabled?: boolean;
  };
  headerActions?: HeaderAction[] | React.ReactNode; // Flexible header actions
  theme?: ThemeConfig; // Theme config for Tailwind Play
  enableTailwindPlay?: boolean; // Enable/disable Tailwind Play CDN
}

// Theme Renderer Props
export interface ThemeRendererProps {
  data: any;
  config: PuckConfig;
  className?: string;
  style?: React.CSSProperties;
}

// Theme Context
export interface ThemeContextValue {
  theme: ThemeConfig;
  updateTheme: (updates: Partial<ThemeConfig>) => void;
  resetTheme: () => void;
  applyTheme: (theme: ThemeConfig) => void;
}

// Component Props for ZMP-UI Components
export interface ZMPButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'tertiary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

export interface ZMPTextProps {
  children: ReactNode;
  size?: 'xSmall' | 'small' | 'normal' | 'large' | 'xLarge' | 'xxLarge';
  bold?: boolean;
  className?: string;
}

export interface ZMPBoxProps {
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  p?: number;
  m?: number;
  flex?: boolean;
  direction?: 'row' | 'column';
  align?: 'start' | 'center' | 'end';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around';
}

// Export all types
export type {
  ComponentConfig,
  ReactNode,
};
