/**
 * Hook for Theme Builder API integration
 * Provides debounced auto-save and CSS management
 */

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { debounce } from 'lodash';
import { ThemeAPIService, SaveConfigParams } from '../services/theme-api-service';

export interface UseThemeBuilderAPIOptions {
  accountId: string;
  themeId?: string;
  editThemeId?: string;
  tempThemeId?: string;
  autoSaveDelay?: number;
  enableTailwindCSS?: boolean;
}

export interface UseThemeBuilderAPIResult {
  saveConfig: (config: any) => Promise<void>;
  debouncedSave: (config: any) => void;
  isLoading: boolean;
  error: string | null;
  lastSaved: Date | null;
  cssUrl: string | null;
  applyCSSUrl: (url: string) => void;
  removeCSSFromDocument: () => void;
}

export const useThemeBuilderAPI = (
  options: UseThemeBuilderAPIOptions
): UseThemeBuilderAPIResult => {
  const {
    accountId,
    themeId,
    editThemeId,
    tempThemeId,
    autoSaveDelay = 1000,
    enableTailwindCSS = true,
  } = options;

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [cssUrl, setCssUrl] = useState<string | null>(null);

  // Refs
  const apiServiceRef = useRef<ThemeAPIService>();
  const currentTempThemeIdRef = useRef<string | undefined>(tempThemeId);

  // Initialize API service
  useEffect(() => {
    if (!apiServiceRef.current) {
      apiServiceRef.current = new ThemeAPIService();
    }
  }, []);

  // Update current temp theme ID
  useEffect(() => {
    currentTempThemeIdRef.current = tempThemeId;
  }, [tempThemeId]);

  // Save config function
  const saveConfig = useCallback(
    async (config: any): Promise<void> => {
      if (!apiServiceRef.current || !enableTailwindCSS) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const params: SaveConfigParams = {
          config,
          accountId,
          themeId,
          editThemeId,
          tempThemeId: currentTempThemeIdRef.current,
        };

        const result = await apiServiceRef.current.saveConfig(params);
        
        // Update state
        setCssUrl(result.cssUrl);
        setLastSaved(new Date());
        
        // Apply CSS URL to document
        apiServiceRef.current.applyCSSUrl(result.cssUrl);

        console.log('Theme config saved successfully:', {
          configId: result.configId,
          cssUrl: result.cssUrl,
          hash: result.hash,
          size: result.size,
        });

      } catch (err: any) {
        const errorMessage = err.message || 'Failed to save theme config';
        setError(errorMessage);
        console.error('Save config error:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [accountId, themeId, editThemeId, enableTailwindCSS]
  );

  // Create debounced save function
  const debouncedSave = useCallback(
    debounce(async (config: any) => {
      try {
        await saveConfig(config);
      } catch (error) {
        // Error is already handled in saveConfig
        console.error('Debounced save failed:', error);
      }
    }, autoSaveDelay),
    [saveConfig, autoSaveDelay]
  );

  // Apply CSS URL function
  const applyCSSUrl = useCallback((url: string) => {
    if (apiServiceRef.current) {
      apiServiceRef.current.applyCSSUrl(url);
      setCssUrl(url);
    }
  }, []);

  // Remove CSS from document
  const removeCSSFromDocument = useCallback(() => {
    if (apiServiceRef.current) {
      apiServiceRef.current.removeCSSFromDocument();
      setCssUrl(null);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (apiServiceRef.current) {
        apiServiceRef.current.removeCSSFromDocument();
      }
      // Cancel any pending debounced calls
      debouncedSave.cancel();
    };
  }, [debouncedSave]);

  return {
    saveConfig,
    debouncedSave,
    isLoading,
    error,
    lastSaved,
    cssUrl,
    applyCSSUrl,
    removeCSSFromDocument,
  };
};

/**
 * Hook for loading theme config and CSS
 */
export interface UseThemeConfigLoaderOptions {
  configId: string;
  accountId?: string;
  autoApplyCSS?: boolean;
}

export interface UseThemeConfigLoaderResult {
  config: any | null;
  cssText: string | null;
  isLoading: boolean;
  error: string | null;
  hash: string | null;
  cached: boolean;
  reload: () => Promise<void>;
}

export const useThemeConfigLoader = (
  options: UseThemeConfigLoaderOptions
): UseThemeConfigLoaderResult => {
  const { configId, accountId, autoApplyCSS = true } = options;

  // State
  const [config, setConfig] = useState<any | null>(null);
  const [cssText, setCssText] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hash, setHash] = useState<string | null>(null);
  const [cached, setCached] = useState(false);

  // Refs
  const apiServiceRef = useRef<ThemeAPIService>();

  // Initialize API service
  useEffect(() => {
    if (!apiServiceRef.current) {
      apiServiceRef.current = new ThemeAPIService();
    }
  }, []);

  // Load config function
  const loadConfig = useCallback(async () => {
    if (!apiServiceRef.current || !configId) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await apiServiceRef.current.loadConfig({
        configId,
        accountId,
      });

      setConfig(result.config);
      setCssText(result.cssText);
      setHash(result.hash);
      setCached(result.cached);

      // Auto-apply CSS if enabled
      if (autoApplyCSS && result.cssText) {
        apiServiceRef.current.applyCSSToDocument(result.cssText, configId);
      }

      console.log('Theme config loaded successfully:', {
        configId,
        hash: result.hash,
        size: result.size,
        cached: result.cached,
      });

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load theme config';
      setError(errorMessage);
      console.error('Load config error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [configId, accountId, autoApplyCSS]);

  // Load config on mount and when dependencies change
  useEffect(() => {
    if (configId) {
      loadConfig();
    }
  }, [loadConfig]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (apiServiceRef.current && autoApplyCSS) {
        apiServiceRef.current.removeCSSFromDocument();
      }
    };
  }, [autoApplyCSS]);

  return {
    config,
    cssText,
    isLoading,
    error,
    hash,
    cached,
    reload: loadConfig,
  };
};
