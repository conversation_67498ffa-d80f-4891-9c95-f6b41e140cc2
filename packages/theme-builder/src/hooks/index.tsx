import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ThemeConfig, ThemeContextValue } from '../types';
import { DEFAULT_THEME, applyThemeConfig } from '../utils';

// Theme Context
const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

// Theme Provider Props
interface ThemeProviderProps {
  children: ReactNode;
  initialTheme?: ThemeConfig;
}

// Theme Provider Component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme = DEFAULT_THEME,
}) => {
  const [theme, setTheme] = useState<ThemeConfig>(initialTheme);

  const updateTheme = useCallback((updates: Partial<ThemeConfig>) => {
    setTheme(prevTheme => ({
      ...prevTheme,
      ...updates,
    }));
  }, []);

  const resetTheme = useCallback(() => {
    setTheme(DEFAULT_THEME);
  }, []);

  const applyTheme = useCallback((newTheme: ThemeConfig) => {
    setTheme(newTheme);
    applyThemeConfig(newTheme);
  }, []);

  // Apply theme to DOM when theme changes
  React.useEffect(() => {
    applyThemeConfig(theme);
  }, [theme]);

  const value: ThemeContextValue = {
    theme,
    updateTheme,
    resetTheme,
    applyTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// useTheme Hook
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// useThemeBuilder Hook
export const useThemeBuilder = () => {
  const { theme, updateTheme, applyTheme } = useTheme();
  const [isEditing, setIsEditing] = useState(false);
  const [history, setHistory] = useState<ThemeConfig[]>([theme]);
  const [historyIndex, setHistoryIndex] = useState(0);

  const startEditing = useCallback(() => {
    setIsEditing(true);
  }, []);

  const stopEditing = useCallback(() => {
    setIsEditing(false);
  }, []);

  const saveToHistory = useCallback((newTheme: ThemeConfig) => {
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newTheme);
      return newHistory;
    });
    setHistoryIndex(prev => prev + 1);
  }, [historyIndex]);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      applyTheme(history[newIndex]);
    }
  }, [historyIndex, history, applyTheme]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      applyTheme(history[newIndex]);
    }
  }, [historyIndex, history, applyTheme]);

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  const updateThemeWithHistory = useCallback((updates: Partial<ThemeConfig>) => {
    const newTheme = { ...theme, ...updates };
    updateTheme(updates);
    saveToHistory(newTheme);
  }, [theme, updateTheme, saveToHistory]);

  return {
    theme,
    isEditing,
    startEditing,
    stopEditing,
    updateTheme: updateThemeWithHistory,
    applyTheme,
    undo,
    redo,
    canUndo,
    canRedo,
    history,
    historyIndex,
  };
};

// useLocalStorage Hook for theme persistence
export const useThemeStorage = (key: string = 'theme-builder-theme') => {
  const { theme, applyTheme } = useTheme();

  const saveTheme = useCallback(() => {
    try {
      localStorage.setItem(key, JSON.stringify(theme));
    } catch (error) {
      console.error('Failed to save theme to localStorage:', error);
    }
  }, [theme, key]);

  const loadTheme = useCallback(() => {
    try {
      const savedTheme = localStorage.getItem(key);
      if (savedTheme) {
        const parsedTheme = JSON.parse(savedTheme);
        applyTheme(parsedTheme);
        return parsedTheme;
      }
    } catch (error) {
      console.error('Failed to load theme from localStorage:', error);
    }
    return null;
  }, [key, applyTheme]);

  const clearTheme = useCallback(() => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to clear theme from localStorage:', error);
    }
  }, [key]);

  return {
    saveTheme,
    loadTheme,
    clearTheme,
  };
};

// Export API hooks
export * from './use-theme-builder-api';

// Re-export specific hooks
export {
  useThemeBuilderAPI,
  useThemeConfigLoader,
} from './use-theme-builder-api';
