/**
 * Global Styles Tab for ThemeBuilder
 * Provides Elementor-like global styling interface as a tab
 */

import React, { useState, useCallback } from 'react';
import { createUsePuck } from '@measured/puck';
import { GlobalStylePanel, STYLE_CATEGORIES, StyleCategory } from '../components/global-style-system';

// Create Puck hook (you'll need to import your config type)
const usePuck = createUsePuck<any>();

interface GlobalStylesTabProps {
  style?: React.CSSProperties;
}

export const GlobalStylesTab: React.FC<GlobalStylesTabProps> = ({ style }) => {
  const [activeCategory, setActiveCategory] = useState<StyleCategory>('layout');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Get current selected item from Puck
  const selectedItem = usePuck((s) => s.appState.ui.itemSelector);
  const data = usePuck((s) => s.appState.data);
  const dispatch = usePuck((s) => s.dispatch);

  // Get current item data
  const currentItem = selectedItem ? data.content.find((item: any) => item.props?.id === selectedItem.id) : null;
  const currentGlobalStyles = currentItem?.props?.globalStyles || '';

  // Handle global styles change
  const handleGlobalStylesChange = useCallback((newStyles: string) => {
    if (!selectedItem) return;

    dispatch({
      type: 'setData',
      data: {
        ...data,
        content: data.content.map((item: any) => 
          item.props?.id === selectedItem.id 
            ? {
                ...item,
                props: {
                  ...item.props,
                  globalStyles: newStyles,
                }
              }
            : item
        ),
      },
    });
  }, [selectedItem, data, dispatch]);

  // Filter categories based on search
  const filteredCategories = Object.keys(STYLE_CATEGORIES).filter(categoryKey =>
    categoryKey.toLowerCase().includes(searchTerm.toLowerCase()) ||
    STYLE_CATEGORIES[categoryKey as StyleCategory].title.toLowerCase().includes(searchTerm.toLowerCase())
  ) as StyleCategory[];

  return (
    <div style={{ padding: 24, ...style }}>
      {/* Header */}
      <div style={{ marginBottom: 20 }}>
        <h3 style={{ 
          margin: 0, 
          marginBottom: 8, 
          fontSize: 16, 
          fontWeight: 600,
          color: '#333'
        }}>
          Global Styles
        </h3>
        <p style={{ 
          margin: 0, 
          fontSize: 14, 
          color: '#666',
          lineHeight: 1.4
        }}>
          Apply TailwindCSS classes visually to the selected component
        </p>
      </div>

      {/* Selected Item Info */}
      {selectedItem ? (
        <div style={{
          background: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: 8,
          padding: 12,
          marginBottom: 16,
        }}>
          <div style={{ fontSize: 14, fontWeight: 500, color: '#374151', marginBottom: 4 }}>
            Selected: {selectedItem.label || currentItem?.type || 'Component'}
          </div>
          {currentGlobalStyles && (
            <div style={{ fontSize: 12, color: '#6b7280' }}>
              Current styles: {currentGlobalStyles.split(' ').length} classes
            </div>
          )}
        </div>
      ) : (
        <div style={{
          background: '#fef3c7',
          border: '1px solid #fbbf24',
          borderRadius: 8,
          padding: 12,
          marginBottom: 16,
        }}>
          <div style={{ fontSize: 14, color: '#92400e' }}>
            Select a component to apply global styles
          </div>
        </div>
      )}

      {/* Search */}
      <div style={{ marginBottom: 16 }}>
        <input
          type="text"
          placeholder="Search style categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #d1d5db',
            borderRadius: 6,
            fontSize: 14,
            outline: 'none',
            transition: 'border-color 0.2s',
          }}
          onFocus={(e) => {
            e.target.style.borderColor = '#3b82f6';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = '#d1d5db';
          }}
        />
      </div>

      {/* Category Tabs */}
      <div style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: 4,
        marginBottom: 16,
        borderBottom: '1px solid #e5e7eb',
        paddingBottom: 8,
      }}>
        {filteredCategories.map((categoryKey) => {
          const category = STYLE_CATEGORIES[categoryKey];
          const isActive = activeCategory === categoryKey;
          
          return (
            <button
              key={categoryKey}
              onClick={() => setActiveCategory(categoryKey)}
              style={{
                padding: '6px 12px',
                fontSize: 12,
                fontWeight: 500,
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                transition: 'all 0.2s',
                background: isActive ? '#3b82f6' : '#f3f4f6',
                color: isActive ? 'white' : '#374151',
              }}
              onMouseOver={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = '#e5e7eb';
                }
              }}
              onMouseOut={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = '#f3f4f6';
                }
              }}
            >
              {category.icon} {category.title}
            </button>
          );
        })}
      </div>

      {/* Global Style Panel */}
      {selectedItem ? (
        <GlobalStylePanel
          value={currentGlobalStyles}
          onChange={handleGlobalStylesChange}
          componentType={currentItem?.type || 'All'}
          categories={[activeCategory]}
        />
      ) : (
        <div style={{
          textAlign: 'center',
          padding: 40,
          color: '#9ca3af',
          fontSize: 14,
        }}>
          <div style={{ fontSize: 48, marginBottom: 16 }}>🎨</div>
          <div>Select a component from the preview to start styling</div>
        </div>
      )}

      {/* Quick Actions */}
      {selectedItem && (
        <div style={{
          marginTop: 20,
          paddingTop: 16,
          borderTop: '1px solid #e5e7eb',
        }}>
          <div style={{ fontSize: 14, fontWeight: 500, marginBottom: 8, color: '#374151' }}>
            Quick Actions
          </div>
          <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
            <button
              onClick={() => handleGlobalStylesChange('')}
              style={{
                padding: '6px 12px',
                fontSize: 12,
                border: '1px solid #d1d5db',
                borderRadius: 4,
                background: 'white',
                color: '#374151',
                cursor: 'pointer',
                transition: 'all 0.2s',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.background = '#f9fafb';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.background = 'white';
              }}
            >
              Clear All Styles
            </button>
            
            <button
              onClick={() => {
                navigator.clipboard.writeText(currentGlobalStyles);
                // You could add a toast notification here
              }}
              style={{
                padding: '6px 12px',
                fontSize: 12,
                border: '1px solid #d1d5db',
                borderRadius: 4,
                background: 'white',
                color: '#374151',
                cursor: 'pointer',
                transition: 'all 0.2s',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.background = '#f9fafb';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.background = 'white';
              }}
            >
              Copy Classes
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlobalStylesTab;
