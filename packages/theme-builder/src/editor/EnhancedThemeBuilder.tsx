/**
 * Enhanced ThemeBuilder with Global Styles Tab
 * Based on the Puck custom UI example with added global styling capabilities
 */

"use client";

import React, { ReactNode, useEffect, useRef, useState, useCallback } from "react";
import { ActionBar, Button, Data, Puck, Render, createUsePuck } from "@measured/puck";
import { IconButton } from "@measured/puck";
import { ChevronUp, ChevronDown, Globe, Palette, Settings } from "lucide-react";
import { GlobalStylesTab } from "./GlobalStylesTab";
import { TailwindPlayProvider } from "../components/tailwind-play-provider";

// You'll need to import your actual config type
const usePuck = createUsePuck<any>();

interface EnhancedThemeBuilderProps {
  config: any;
  data: Data;
  onChange?: (data: Data) => void;
  onPublish?: (data: Data) => void;
  enableTailwindPlay?: boolean;
  theme?: any;
  headerActions?: Array<{
    type: 'button';
    label: string;
    icon?: string;
    variant?: 'primary' | 'secondary';
    onClick: () => void;
    disabled?: boolean;
  }>;
  viewports?: Array<{
    width: number;
    height: number;
    label: string;
    icon?: string;
  }>;
}

const CustomHeader = ({ 
  onPublish, 
  headerActions = [] 
}: { 
  onPublish: (data: Data) => void;
  headerActions?: EnhancedThemeBuilderProps['headerActions'];
}) => {
  const get = usePuck((s) => s.get);
  const dispatch = usePuck((s) => s.dispatch);
  const previewMode = usePuck((s) => s.appState.ui.previewMode);

  const toggleMode = () => {
    dispatch({
      type: "setUi",
      ui: {
        previewMode: previewMode === "edit" ? "interactive" : "edit",
      },
    });
  };

  return (
    <header
      style={{
        display: "flex",
        flexWrap: "wrap",
        gap: 16,
        padding: "16px 24px",
        background: "white",
        color: "black",
        alignItems: "center",
        borderBottom: "1px solid #ddd",
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
      }}
      onClick={() => dispatch({ type: "setUi", ui: { itemSelector: null } })}
    >
      <span style={{ fontWeight: 600, fontSize: 16 }}>
        🎨 Theme Builder
      </span>
      
      <div style={{ marginLeft: "auto", display: "flex", gap: 8, alignItems: "center" }}>
        {/* Custom Header Actions */}
        {headerActions.map((action, index) => (
          <Button
            key={index}
            onClick={action.onClick}
            variant={action.variant || "secondary"}
            disabled={action.disabled}
            style={{ fontSize: 14 }}
          >
            {action.icon && <span style={{ marginRight: 4 }}>{action.icon}</span>}
            {action.label}
          </Button>
        ))}
        
        {/* Default Actions */}
        <div style={{ gap: 8, display: "flex" }}>
          <Button onClick={toggleMode} variant="secondary">
            <Settings size="14" style={{ marginRight: 4 }} />
            {previewMode === "edit" ? "Preview" : "Edit"}
          </Button>
          <Button
            onClick={() => onPublish(get().appState.data)}
            icon={<Globe size="14" />}
            variant="primary"
          >
            Publish
          </Button>
        </div>
      </div>
    </header>
  );
};

const Tabs = ({
  tabs,
  onTabCollapse,
  scrollTop,
}: {
  tabs: { label: string; body: ReactNode; icon?: ReactNode }[];
  onTabCollapse: () => void;
  scrollTop: number;
}) => {
  const [currentTab, setCurrentTab] = useState(-1);
  const itemSelector = usePuck((s) => s.appState.ui.itemSelector);
  const isDragging = usePuck((s) => s.appState.ui.isDragging);

  const currentTabRef = useRef(currentTab);

  useEffect(() => {
    if (currentTabRef.current !== -1 && itemSelector) {
      setCurrentTab(1); // Switch to Fields tab when item is selected
    }
  }, [itemSelector]);

  useEffect(() => {
    currentTabRef.current = currentTab;
  }, [currentTab]);

  useEffect(() => {
    if (isDragging && currentTab === 1) {
      setCurrentTab(-1);
    }
  }, [currentTab, isDragging]);

  useEffect(() => {
    if (scrollTop === 0) {
      setCurrentTab(-1);
      onTabCollapse();
    }
  }, [scrollTop]);

  return (
    <div
      onClick={(e) => e.stopPropagation()}
      style={{
        background: "#ffffff",
        pointerEvents: "all",
        borderTop: "1px solid #ddd",
        boxShadow: "rgba(140, 152, 164, 0.25) 0px 0px 6px 0px",
      }}
    >
      <div
        style={{
          display: "flex",
          paddingLeft: 16,
          paddingRight: 16,
          borderBottom: "1px solid #ddd",
          overflowX: "auto",
        }}
      >
        {tabs.map((tab, idx) => {
          const isCurrentTab = currentTab === idx;
          return (
            <button
              key={idx}
              type="button"
              onClick={() => {
                if (currentTab === idx) {
                  setCurrentTab(-1);
                } else {
                  setCurrentTab(idx);
                  if (scrollTop < 20) {
                    setTimeout(() => {
                      document
                        .querySelector("#action-bar")
                        ?.scroll({ top: 128, behavior: "smooth" });
                    }, 25);
                  }
                }
              }}
              style={{
                fontFamily: "inherit",
                fontSize: 14,
                padding: "16px 16px",
                paddingTop: 19,
                color: isCurrentTab ? "var(--puck-color-azure-04)" : "black",
                border: "none",
                borderBottom: isCurrentTab
                  ? "3px solid var(--puck-color-azure-04)"
                  : "3px solid transparent",
                background: "white",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                gap: 6,
                transition: "all 0.2s",
              }}
              onMouseOver={(e) => {
                if (!isCurrentTab) {
                  e.currentTarget.style.background = "#f8fafc";
                }
              }}
              onMouseOut={(e) => {
                if (!isCurrentTab) {
                  e.currentTarget.style.background = "white";
                }
              }}
            >
              {tab.icon}
              {tab.label}
            </button>
          );
        })}
        <div
          style={{
            marginLeft: "auto",
            display: "flex",
            alignItems: "center",
            gap: 8,
          }}
        >
          <div>
            <IconButton
              onClick={() => {
                setCurrentTab(currentTab === -1 ? 0 : -1);

                if (currentTab !== -1) {
                  onTabCollapse();
                } else {
                  setTimeout(() => {
                    document
                      .querySelector("#action-bar")
                      ?.scroll({ top: 128, behavior: "smooth" });
                  }, 25);
                }
              }}
              title={currentTab !== -1 ? "Collapse Tabs" : "Expand Tabs"}
            >
              {currentTab === -1 ? <ChevronUp /> : <ChevronDown />}
            </IconButton>
          </div>
        </div>
      </div>
      <div style={{ overflowX: "auto" }}>
        {tabs.map((tab, idx) => {
          const isCurrentTab = currentTab === idx;
          return (
            <div
              key={idx}
              style={{
                display: isCurrentTab ? "block" : "none",
              }}
            >
              {tab.body}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const CustomPuck = ({ 
  config, 
  data, 
  onChange, 
  onPublish, 
  headerActions 
}: {
  config: any;
  data: Data;
  onChange?: (data: Data) => void;
  onPublish: (data: Data) => void;
  headerActions?: EnhancedThemeBuilderProps['headerActions'];
}) => {
  const [hoveringTabs, setHoveringTabs] = useState(false);
  const [actionBarScroll, setActionBarScroll] = useState(0);

  return (
    <Puck 
      config={config} 
      data={data} 
      onPublish={onPublish}
      onChange={onChange}
    >
      <div style={{ position: "relative" }}>
        <div style={{ position: "sticky", top: 0, zIndex: 2 }}>
          <CustomHeader onPublish={onPublish} headerActions={headerActions} />
        </div>
        <div
          style={{
            position: "relative",
            overflowY: hoveringTabs ? "hidden" : "auto",
            zIndex: 0,
          }}
        >
          <Puck.Preview />
        </div>
        <div
          id="action-bar"
          style={{
            position: "fixed",
            bottom: 0,
            overflowY: "auto",
            overflowX: "hidden",
            maxHeight: "100vh",
            width: "100%",
            boxSizing: "border-box",
            paddingTop: "calc(100vh - 58px)",
            pointerEvents: hoveringTabs ? undefined : "none",
            zIndex: 1,
            overscrollBehavior: "none",
          }}
          onTouchStart={() => setHoveringTabs(false)}
          onScrollCapture={(e) => {
            setActionBarScroll(e.currentTarget.scrollTop);
          }}
        >
          <div
            style={{
              background: "white",
              position: "relative",
              pointerEvents: "none",
              zIndex: 0,
            }}
            onMouseOver={(e) => {
              e.stopPropagation();
              setHoveringTabs(true);
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              setHoveringTabs(true);
            }}
            onMouseOut={() => {
              setHoveringTabs(false);
            }}
          >
            {/* Force react to render when hoveringTabs changes */}
            {hoveringTabs && <span />}
            <Tabs
              onTabCollapse={() => {
                setTimeout(() => setHoveringTabs(false), 50);
              }}
              scrollTop={actionBarScroll}
              tabs={[
                { 
                  label: "Components", 
                  body: <Puck.Components />,
                  icon: <span>🧩</span>
                },
                { 
                  label: "Fields", 
                  body: <Puck.Fields />,
                  icon: <span>⚙️</span>
                },
                { 
                  label: "Global Styles", 
                  body: <GlobalStylesTab />,
                  icon: <Palette size={16} />
                },
                { 
                  label: "Outline", 
                  body: <Puck.Outline />,
                  icon: <span>📋</span>
                },
              ]}
            />
          </div>
        </div>
      </div>
    </Puck>
  );
};

export const EnhancedThemeBuilder: React.FC<EnhancedThemeBuilderProps> = ({
  config,
  data,
  onChange,
  onPublish,
  enableTailwindPlay = true,
  theme,
  headerActions,
  viewports,
}) => {
  const handlePublish = useCallback((publishData: Data) => {
    console.log('Publishing theme with global styles:', publishData);
    onPublish?.(publishData);
  }, [onPublish]);

  const handleChange = useCallback((changeData: Data) => {
    onChange?.(changeData);
  }, [onChange]);

  const content = (
    <CustomPuck
      config={config}
      data={data}
      onChange={handleChange}
      onPublish={handlePublish}
      headerActions={headerActions}
    />
  );

  if (enableTailwindPlay) {
    return (
      <TailwindPlayProvider enabled={true} theme={theme}>
        {content}
      </TailwindPlayProvider>
    );
  }

  return content;
};

export default EnhancedThemeBuilder;
