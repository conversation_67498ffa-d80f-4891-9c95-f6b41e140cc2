import React from 'react';

import { But<PERSON>, Puck } from '@measured/puck';
import '@measured/puck/puck.css';

import { ZMP_PUCK_CONFIG } from '../components/zmp-config';
import { TailwindPlayProvider } from '../components/tailwind-play-provider';
import { HeaderAction, ThemeBuilderProps } from '../types';

// Helper function to render header actions
const renderHeaderActions = (
  headerActions: HeaderAction[] | React.ReactNode,
) => {
  // If it's already a React node, return as is
  if (
    React.isValidElement(headerActions) ||
    typeof headerActions !== 'object' ||
    !Array.isArray(headerActions)
  ) {
    return headerActions;
  }

  // If it's an array of HeaderAction configs, render Puck buttons
  return (headerActions as HeaderAction[]).map((action, index) => {
    if (action.type === 'button') {
      return (
        <Button
          key={index}
          onClick={action.onClick}
          disabled={action.disabled}
          variant={action.variant === 'primary' ? 'primary' : 'secondary'}
        >
          {action.icon && (
            <span style={{ marginRight: '4px' }}>{action.icon}</span>
          )}
          {action.label}
        </Button>
      );
    }
    return null;
  });
};

export const ThemeBuilder: React.FC<ThemeBuilderProps> = ({
  config = ZMP_PUCK_CONFIG,
  data,
  onChange,
  onPreview,
  onPublish,
  showThemePanel = false,
  showPreview = true,
  viewports,
  iframe,
  headerActions,
}) => {
  return (
    <div className="theme-builder-container flex h-screen">
      {/* Main Editor - Full Width */}
      <div className="flex-1">
        <Puck
          config={config}
          data={data}
          onPublish={onPublish}
          onChange={onChange}
          viewports={viewports}
          iframe={iframe}
          overrides={{
            headerActions: ({ children }) => (
              <>
                {/* Custom header actions from parent */}
                {renderHeaderActions(headerActions)}
                {children}
              </>
            ),
          }}
        />
      </div>
    </div>
  );
};
