/**
 * Client-safe Performance Utilities
 * Browser-compatible performance monitoring without Node.js dependencies
 */

export interface PerformanceMetrics {
  operation: string;
  duration: number;
  size?: number;
  cached?: boolean;
  timestamp: number;
}

export class ClientPerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private maxMetrics = 100; // Keep last 100 metrics

  /**
   * Record performance metric
   */
  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);
    
    // Keep only last maxMetrics entries
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * Start timing an operation
   */
  startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return (additionalData?: Partial<PerformanceMetrics>) => {
      const duration = performance.now() - startTime;
      this.recordMetric({
        operation,
        duration,
        timestamp: Date.now(),
        ...additionalData,
      });
    };
  }

  /**
   * Get performance statistics
   */
  getStats(): {
    totalOperations: number;
    averageDuration: number;
    recentMetrics: PerformanceMetrics[];
  } {
    const totalOperations = this.metrics.length;
    const averageDuration = totalOperations > 0 
      ? this.metrics.reduce((sum, m) => sum + m.duration, 0) / totalOperations 
      : 0;

    const recentMetrics = this.metrics.slice(-10); // Last 10 operations

    return {
      totalOperations,
      averageDuration,
      recentMetrics,
    };
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
  }

  /**
   * Get metrics by operation type
   */
  getMetricsByOperation(operation: string): PerformanceMetrics[] {
    return this.metrics.filter(m => m.operation === operation);
  }
}

/**
 * Simple hash function for client-side use
 */
export function simpleHash(str: string): string {
  let hash = 0;
  if (str.length === 0) return hash.toString();
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return Math.abs(hash).toString(36);
}

/**
 * Client-safe cache implementation
 */
export class ClientCache<T> {
  private cache = new Map<string, { data: T; timestamp: number }>();
  private maxAge: number;
  private maxSize: number;

  constructor(maxAge: number = 5 * 60 * 1000, maxSize: number = 50) {
    this.maxAge = maxAge;
    this.maxSize = maxSize;
  }

  set(key: string, data: T): void {
    // Clean up expired entries
    this.cleanup();
    
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > this.maxAge) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.maxAge) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  getStats(): {
    size: number;
    maxSize: number;
    oldestEntry: number | null;
  } {
    let oldestEntry: number | null = null;
    
    if (this.cache.size > 0) {
      const timestamps = Array.from(this.cache.values()).map(v => v.timestamp);
      oldestEntry = Math.min(...timestamps);
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      oldestEntry,
    };
  }
}

// Export singleton instances
export const clientPerformanceMonitor = new ClientPerformanceMonitor();
export const clientCache = new ClientCache();

/**
 * Measure async operation performance
 */
export async function measureAsync<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  const endTiming = clientPerformanceMonitor.startTiming(operation);
  
  try {
    const result = await fn();
    endTiming({ cached: false });
    return result;
  } catch (error) {
    endTiming({ cached: false });
    throw error;
  }
}

/**
 * Measure sync operation performance
 */
export function measureSync<T>(
  operation: string,
  fn: () => T
): T {
  const endTiming = clientPerformanceMonitor.startTiming(operation);
  
  try {
    const result = fn();
    endTiming({ cached: false });
    return result;
  } catch (error) {
    endTiming({ cached: false });
    throw error;
  }
}

export default {
  ClientPerformanceMonitor,
  ClientCache,
  clientPerformanceMonitor,
  clientCache,
  simpleHash,
  measureAsync,
  measureSync,
};
