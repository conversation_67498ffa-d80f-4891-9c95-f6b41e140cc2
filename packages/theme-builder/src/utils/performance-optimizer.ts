/**
 * Performance Optimizer for Theme Builder
 * Implements compression, caching, and performance monitoring
 */

import { createHash } from 'crypto';
import { gzip, gunzip } from 'zlib';
import { promisify } from 'util';

const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

export interface PerformanceMetrics {
  operation: string;
  duration: number;
  size?: number;
  compressed?: boolean;
  cached?: boolean;
  timestamp: number;
}

export interface CompressionResult {
  compressed: Buffer;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}

export class PerformanceOptimizer {
  private metrics: PerformanceMetrics[] = [];
  private cache = new Map<string, { data: any; timestamp: number; compressed: boolean }>();
  private maxCacheSize = 100; // Maximum number of cached items
  private maxCacheAge = 30 * 60 * 1000; // 30 minutes

  /**
   * Compress CSS text using gzip
   */
  async compressCSS(cssText: string): Promise<CompressionResult> {
    const startTime = Date.now();
    
    try {
      const originalBuffer = Buffer.from(cssText, 'utf8');
      const compressed = await gzipAsync(originalBuffer);
      
      const result: CompressionResult = {
        compressed,
        originalSize: originalBuffer.length,
        compressedSize: compressed.length,
        compressionRatio: compressed.length / originalBuffer.length,
      };

      this.recordMetric({
        operation: 'compress_css',
        duration: Date.now() - startTime,
        size: result.compressedSize,
        compressed: true,
        cached: false,
        timestamp: Date.now(),
      });

      return result;
    } catch (error) {
      console.error('CSS compression failed:', error);
      throw error;
    }
  }

  /**
   * Decompress CSS text from gzip
   */
  async decompressCSS(compressedData: Buffer): Promise<string> {
    const startTime = Date.now();
    
    try {
      const decompressed = await gunzipAsync(compressedData);
      const cssText = decompressed.toString('utf8');

      this.recordMetric({
        operation: 'decompress_css',
        duration: Date.now() - startTime,
        size: decompressed.length,
        compressed: false,
        cached: false,
        timestamp: Date.now(),
      });

      return cssText;
    } catch (error) {
      console.error('CSS decompression failed:', error);
      throw error;
    }
  }

  /**
   * Cache data with automatic cleanup
   */
  cacheData(key: string, data: any, compressed: boolean = false): void {
    // Clean up old entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      this.cleanupCache();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      compressed,
    });
  }

  /**
   * Retrieve cached data
   */
  getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    
    if (!cached) {
      return null;
    }

    // Check if cache entry is expired
    if (Date.now() - cached.timestamp > this.maxCacheAge) {
      this.cache.delete(key);
      return null;
    }

    this.recordMetric({
      operation: 'cache_hit',
      duration: 0,
      cached: true,
      compressed: cached.compressed,
      timestamp: Date.now(),
    });

    return cached.data;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.maxCacheAge) {
        expiredKeys.push(key);
      }
    }

    // Remove expired entries
    expiredKeys.forEach(key => this.cache.delete(key));

    // If still too many entries, remove oldest ones
    if (this.cache.size >= this.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, Math.floor(this.maxCacheSize * 0.2)); // Remove 20%
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }

  /**
   * Generate optimized hash for caching
   */
  generateOptimizedHash(data: any): string {
    const normalized = this.normalizeData(data);
    return createHash('md5').update(JSON.stringify(normalized)).digest('hex');
  }

  /**
   * Normalize data for consistent hashing
   */
  private normalizeData(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => this.normalizeData(item));
    }
    
    if (data && typeof data === 'object') {
      const normalized: any = {};
      const keys = Object.keys(data).sort(); // Sort keys for consistency
      
      for (const key of keys) {
        normalized[key] = this.normalizeData(data[key]);
      }
      
      return normalized;
    }
    
    return data;
  }

  /**
   * Record performance metric
   */
  private recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    totalOperations: number;
    averageDuration: number;
    cacheHitRate: number;
    compressionStats: {
      operations: number;
      averageRatio: number;
    };
    recentMetrics: PerformanceMetrics[];
  } {
    const totalOperations = this.metrics.length;
    const averageDuration = totalOperations > 0 
      ? this.metrics.reduce((sum, m) => sum + m.duration, 0) / totalOperations 
      : 0;

    const cacheHits = this.metrics.filter(m => m.cached).length;
    const cacheHitRate = totalOperations > 0 ? cacheHits / totalOperations : 0;

    const compressionMetrics = this.metrics.filter(m => m.operation.includes('compress'));
    const compressionStats = {
      operations: compressionMetrics.length,
      averageRatio: compressionMetrics.length > 0
        ? compressionMetrics.reduce((sum, m) => sum + (m.size || 0), 0) / compressionMetrics.length
        : 0,
    };

    const recentMetrics = this.metrics.slice(-10); // Last 10 operations

    return {
      totalOperations,
      averageDuration,
      cacheHitRate,
      compressionStats,
      recentMetrics,
    };
  }

  /**
   * Clear all metrics and cache
   */
  reset(): void {
    this.metrics = [];
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    oldestEntry: number | null;
  } {
    const cacheHits = this.metrics.filter(m => m.cached).length;
    const totalRequests = this.metrics.filter(m => 
      m.operation.includes('get') || m.operation.includes('load')
    ).length;
    
    const hitRate = totalRequests > 0 ? cacheHits / totalRequests : 0;
    
    let oldestEntry: number | null = null;
    if (this.cache.size > 0) {
      const timestamps = Array.from(this.cache.values()).map(v => v.timestamp);
      oldestEntry = Math.min(...timestamps);
    }

    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate,
      oldestEntry,
    };
  }

  /**
   * Optimize CSS text for better performance
   */
  optimizeCSS(cssText: string): string {
    // Remove comments
    let optimized = cssText.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // Remove unnecessary whitespace
    optimized = optimized.replace(/\s+/g, ' ');
    
    // Remove whitespace around specific characters
    optimized = optimized.replace(/\s*([{}:;,>+~])\s*/g, '$1');
    
    // Remove trailing semicolons before closing braces
    optimized = optimized.replace(/;}/g, '}');
    
    // Remove empty rules
    optimized = optimized.replace(/[^{}]+\{\s*\}/g, '');
    
    return optimized.trim();
  }
}

// Export singleton instance
export const performanceOptimizer = new PerformanceOptimizer();
