/**
 * TailwindCSS CLI Service
 * Extracts classes from Puck config and generates CSS using TailwindCSS CLI
 */

import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface TailwindCSSServiceOptions {
  tempDir?: string;
  configPath?: string;
  maxCacheAge?: number; // in milliseconds
}

export interface GenerateCSSResult {
  css: string;
  hash: string;
  classes: string[];
  size: number;
}

export class TailwindCSSService {
  private tempDir: string;
  private configPath: string;
  private cache = new Map<string, { css: string; timestamp: number }>();
  private maxCacheAge: number;

  constructor(options: TailwindCSSServiceOptions = {}) {
    this.tempDir = options.tempDir || join(process.cwd(), 'tmp', 'tailwind-css-service');
    this.configPath = options.configPath || join(process.cwd(), 'packages', 'theme-builder', 'tailwind.cli.config.js');
    this.maxCacheAge = options.maxCacheAge || 5 * 60 * 1000; // 5 minutes

    this.ensureTempDir();
  }

  /**
   * Extract TailwindCSS classes from Puck data
   */
  extractClassesFromPuckData(puckData: any): string[] {
    const classes = new Set<string>();

    const extractFromObject = (obj: any) => {
      if (!obj || typeof obj !== 'object') return;

      // Extract from className properties
      if (typeof obj.className === 'string') {
        this.parseClassString(obj.className).forEach(cls => classes.add(cls));
      }

      // Extract from customClasses properties (Tailwind-specific)
      if (typeof obj.customClasses === 'string') {
        this.parseClassString(obj.customClasses).forEach(cls => classes.add(cls));
      }

      // Extract from tailwind-specific properties
      if (obj.tailwindColor) {
        this.getTailwindColorClasses(obj.tailwindColor).forEach(cls => classes.add(cls));
      }

      if (obj.tailwindSize) {
        this.getTailwindSizeClasses(obj.tailwindSize).forEach(cls => classes.add(cls));
      }

      if (obj.tailwindColumns) {
        classes.add(`grid-cols-${obj.tailwindColumns}`);
      }

      if (obj.tailwindGap) {
        const gapClass = this.mapGapToClass(obj.tailwindGap);
        if (gapClass) classes.add(gapClass);
      }

      // Recursively extract from nested objects and arrays
      Object.values(obj).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(item => extractFromObject(item));
        } else if (typeof value === 'object') {
          extractFromObject(value);
        }
      });
    };

    extractFromObject(puckData);
    return Array.from(classes).filter(cls => cls.trim().length > 0);
  }

  /**
   * Parse class string and extract individual classes
   */
  private parseClassString(classString: string): string[] {
    return classString
      .split(/\s+/)
      .map(cls => cls.trim())
      .filter(cls => cls.length > 0)
      .filter(cls => this.isValidTailwindClass(cls));
  }

  /**
   * Check if a class is a valid Tailwind class
   */
  private isValidTailwindClass(className: string): boolean {
    // Basic validation for Tailwind classes
    const tailwindPatterns = [
      /^(bg|text|border|ring|shadow|rounded|p|m|w|h|flex|grid|gap|space)-/, // Common prefixes
      /^(hover|focus|active|disabled|sm|md|lg|xl|2xl):/, // Variants
      /^(top|right|bottom|left|inset)-/, // Position
      /^(justify|items|content|self|place)-/, // Flexbox/Grid
      /^(font|leading|tracking|text)-/, // Typography
      /^(opacity|scale|rotate|translate|skew|transform)-/, // Transforms
      /^(transition|duration|ease|delay)-/, // Transitions
      /^(cursor|select|pointer|resize)-/, // Interactivity
      /^\w+\[.+\]$/, // Arbitrary values like w-[123px]
    ];

    return tailwindPatterns.some(pattern => pattern.test(className));
  }

  /**
   * Get Tailwind color classes from color variant
   */
  private getTailwindColorClasses(colorVariant: string): string[] {
    const colorMap: Record<string, string[]> = {
      primary: ['bg-blue-600', 'text-blue-600', 'border-blue-600'],
      secondary: ['bg-gray-600', 'text-gray-600', 'border-gray-600'],
      accent: ['bg-amber-500', 'text-amber-500', 'border-amber-500'],
      success: ['bg-green-600', 'text-green-600', 'border-green-600'],
      warning: ['bg-yellow-500', 'text-yellow-500', 'border-yellow-500'],
      error: ['bg-red-600', 'text-red-600', 'border-red-600'],
    };

    return colorMap[colorVariant] || [];
  }

  /**
   * Get Tailwind size classes from size variant
   */
  private getTailwindSizeClasses(sizeVariant: string): string[] {
    const sizeMap: Record<string, string[]> = {
      xs: ['text-xs', 'p-1', 'h-6'],
      sm: ['text-sm', 'p-2', 'h-8'],
      md: ['text-base', 'p-3', 'h-10'],
      lg: ['text-lg', 'p-4', 'h-12'],
      xl: ['text-xl', 'p-5', 'h-14'],
    };

    return sizeMap[sizeVariant] || [];
  }

  /**
   * Map gap variant to Tailwind class
   */
  private mapGapToClass(gapVariant: string): string | null {
    const gapMap: Record<string, string> = {
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    };

    return gapMap[gapVariant] || null;
  }

  /**
   * Generate CSS from classes using TailwindCSS CLI
   */
  async generateCSS(classes: string[]): Promise<GenerateCSSResult> {
    const hash = this.generateHash(classes);
    
    // Check cache first
    const cached = this.cache.get(hash);
    if (cached && Date.now() - cached.timestamp < this.maxCacheAge) {
      return {
        css: cached.css,
        hash,
        classes,
        size: Buffer.byteLength(cached.css, 'utf8'),
      };
    }

    try {
      // Create temporary files
      const tempInputFile = join(this.tempDir, `input-${hash}.css`);
      const tempOutputFile = join(this.tempDir, `output-${hash}.css`);
      const tempClassFile = join(this.tempDir, `classes-${hash}.txt`);

      // Write classes to temporary file
      await fs.writeFile(tempClassFile, classes.join('\n'));

      // Create input CSS with Tailwind directives
      const inputCSS = `
@tailwind base;
@tailwind components;
@tailwind utilities;
`;
      await fs.writeFile(tempInputFile, inputCSS);

      // Run TailwindCSS CLI
      const command = `npx tailwindcss -i ${tempInputFile} -o ${tempOutputFile} --content ${tempClassFile}`;
      
      const startTime = Date.now();
      await execAsync(command, { cwd: process.cwd() });
      const duration = Date.now() - startTime;

      console.log(`TailwindCSS CLI completed in ${duration}ms`);

      // Read generated CSS
      const css = await fs.readFile(tempOutputFile, 'utf8');

      // Clean up temporary files
      await Promise.all([
        fs.unlink(tempInputFile).catch(() => {}),
        fs.unlink(tempOutputFile).catch(() => {}),
        fs.unlink(tempClassFile).catch(() => {}),
      ]);

      // Cache result
      this.cache.set(hash, { css, timestamp: Date.now() });

      return {
        css,
        hash,
        classes,
        size: Buffer.byteLength(css, 'utf8'),
      };
    } catch (error) {
      console.error('TailwindCSS generation failed:', error);
      throw new Error(`Failed to generate CSS: ${error.message}`);
    }
  }

  /**
   * Generate CSS from Puck data
   */
  async generateCSSFromPuckData(puckData: any): Promise<GenerateCSSResult> {
    const classes = this.extractClassesFromPuckData(puckData);
    return this.generateCSS(classes);
  }

  /**
   * Generate MD5 hash from classes array
   */
  private generateHash(classes: string[]): string {
    const sortedClasses = [...classes].sort();
    return createHash('md5').update(JSON.stringify(sortedClasses)).digest('hex');
  }

  /**
   * Ensure temp directory exists
   */
  private async ensureTempDir(): Promise<void> {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create temp directory:', error);
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache stats
   */
  getCacheStats(): { size: number; entries: number } {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.values()).length,
    };
  }
}

// Export singleton instance
export const tailwindCSSService = new TailwindCSSService();
