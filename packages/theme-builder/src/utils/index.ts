import { ThemeConfig } from '../types';

// Default theme configuration
export const DEFAULT_THEME: ThemeConfig = {
  name: 'Default Theme',
  description: 'Default theme for Zalo Mini App',
  version: '1.0.0',
  
  brand: {
    name: 'My Mini App',
  },

  colors: {
    primary: {
      main: '#1877F2',
      light: '#42A5F5',
      dark: '#1565C0',
      contrast: '#FFFFFF',
    },
    secondary: {
      main: '#9C27B0',
      light: '#BA68C8',
      dark: '#7B1FA2',
      contrast: '#FFFFFF',
    },
    accent: {
      main: '#FF9800',
      light: '#FFB74D',
      dark: '#F57C00',
      contrast: '#000000',
    },
    background: {
      default: '#FFFFFF',
      paper: '#F5F5F5',
      surface: '#FAFAFA',
    },
    text: {
      primary: '#212121',
      secondary: '#757575',
      disabled: '#BDBDBD',
    },
    border: {
      default: '#E0E0E0',
      light: '#F5F5F5',
      dark: '#BDBDBD',
    },
    status: {
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
    },
  },

  typography: {
    fontFamily: {
      primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },

  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },

  borderRadius: {
    none: '0',
    sm: '0.25rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
    full: '9999px',
  },

  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
  },

  layout: {
    maxWidth: '1200px',
    containerPadding: '1rem',
    header: {
      height: '64px',
      sticky: true,
      background: '#FFFFFF',
    },
    footer: {
      background: '#F5F5F5',
      columns: 3,
    },
  },

  components: {},

  accessibility: {
    highContrast: false,
    reducedMotion: false,
    focusVisible: true,
  },
};

/**
 * Create a new theme configuration with defaults
 */
export function createThemeConfig(overrides: Partial<ThemeConfig> = {}): ThemeConfig {
  return {
    ...DEFAULT_THEME,
    ...overrides,
    colors: {
      ...DEFAULT_THEME.colors,
      ...overrides.colors,
    },
    typography: {
      ...DEFAULT_THEME.typography,
      ...overrides.typography,
    },
    spacing: {
      ...DEFAULT_THEME.spacing,
      ...overrides.spacing,
    },
    borderRadius: {
      ...DEFAULT_THEME.borderRadius,
      ...overrides.borderRadius,
    },
    shadows: {
      ...DEFAULT_THEME.shadows,
      ...overrides.shadows,
    },
    layout: {
      ...DEFAULT_THEME.layout,
      ...overrides.layout,
    },
    components: {
      ...DEFAULT_THEME.components,
      ...overrides.components,
    },
    accessibility: {
      ...DEFAULT_THEME.accessibility,
      ...overrides.accessibility,
    },
  };
}

/**
 * Apply theme configuration to CSS variables
 */
export function applyThemeConfig(theme: ThemeConfig, element: HTMLElement = document.documentElement): void {
  // Guard against undefined/null theme
  if (!theme || !theme.colors) {
    console.warn('applyThemeConfig: theme or theme.colors is undefined/null');
    return;
  }

  const root = element;

  // Apply color variables
  Object.entries(theme.colors).forEach(([category, colors]) => {
    if (typeof colors === 'object') {
      Object.entries(colors).forEach(([key, value]) => {
        root.style.setProperty(`--color-${category}-${key}`, value);
      });
    }
  });

  // Apply typography variables with safe access
  if (theme.typography?.fontSize) {
    Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, value);
    });
  } else {
    // Default font sizes if not provided
    root.style.setProperty('--font-size-base', '1rem');
    root.style.setProperty('--font-size-sm', '0.875rem');
    root.style.setProperty('--font-size-lg', '1.125rem');
  }

  if (theme.typography?.fontWeight) {
    Object.entries(theme.typography.fontWeight).forEach(([key, value]) => {
      root.style.setProperty(`--font-weight-${key}`, value.toString());
    });
  } else {
    // Default font weights
    root.style.setProperty('--font-weight-normal', '400');
    root.style.setProperty('--font-weight-bold', '700');
  }

  // Handle both app config structure and theme-builder structure
  const fontFamily = theme.typography?.fontFamily?.primary || theme.typography?.fontFamily || 'Inter, system-ui, sans-serif';
  root.style.setProperty('--font-family-primary', fontFamily);

  if (theme.typography?.fontFamily?.secondary) {
    root.style.setProperty('--font-family-secondary', theme.typography.fontFamily.secondary);
  }

  // Apply spacing variables with safe access
  if (theme.spacing) {
    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });
  } else {
    // Default spacing values
    root.style.setProperty('--spacing-sm', '0.5rem');
    root.style.setProperty('--spacing-md', '1rem');
    root.style.setProperty('--spacing-lg', '1.5rem');
  }

  // Apply border radius variables with safe access
  if (theme.borderRadius) {
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--border-radius-${key}`, value);
    });
  } else {
    // Default border radius values
    root.style.setProperty('--border-radius-sm', '0.25rem');
    root.style.setProperty('--border-radius-md', '0.5rem');
    root.style.setProperty('--border-radius-lg', '0.75rem');
  }

  // Apply shadow variables with safe access
  if (theme.shadows) {
    Object.entries(theme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });
  } else {
    // Default shadow values
    root.style.setProperty('--shadow-sm', '0 1px 2px 0 rgba(0, 0, 0, 0.05)');
    root.style.setProperty('--shadow-md', '0 4px 6px -1px rgba(0, 0, 0, 0.1)');
    root.style.setProperty('--shadow-lg', '0 10px 15px -3px rgba(0, 0, 0, 0.1)');
  }

  // Apply layout variables with safe access
  if (theme.layout) {
    root.style.setProperty('--layout-max-width', theme.layout.maxWidth || '1200px');
    root.style.setProperty('--layout-container-padding', theme.layout.containerPadding || '1rem');

    if (theme.layout.header) {
      root.style.setProperty('--layout-header-height', theme.layout.header.height || '64px');
      root.style.setProperty('--layout-header-background', theme.layout.header.background || '#FFFFFF');
    } else {
      root.style.setProperty('--layout-header-height', '64px');
      root.style.setProperty('--layout-header-background', '#FFFFFF');
    }

    if (theme.layout.footer) {
      root.style.setProperty('--layout-footer-background', theme.layout.footer.background || '#F5F5F5');
    } else {
      root.style.setProperty('--layout-footer-background', '#F5F5F5');
    }
  } else {
    // Default layout values
    root.style.setProperty('--layout-max-width', '1200px');
    root.style.setProperty('--layout-container-padding', '1rem');
    root.style.setProperty('--layout-header-height', '64px');
    root.style.setProperty('--layout-header-background', '#FFFFFF');
    root.style.setProperty('--layout-footer-background', '#F5F5F5');
  }

  // Apply custom variables
  if (theme.customVariables) {
    Object.entries(theme.customVariables).forEach(([key, value]) => {
      root.style.setProperty(`--${key}`, value);
    });
  }
}

/**
 * Validate theme configuration
 */
export function validateThemeConfig(theme: Partial<ThemeConfig>): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!theme.name || theme.name.trim() === '') {
    errors.push('Theme name is required');
  }

  if (!theme.version || theme.version.trim() === '') {
    errors.push('Theme version is required');
  }

  // Validate colors
  if (theme.colors) {
    const requiredColorCategories = ['primary', 'secondary', 'background', 'text'];
    requiredColorCategories.forEach(category => {
      if (!theme.colors![category as keyof typeof theme.colors]) {
        errors.push(`Color category '${category}' is required`);
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Generate CSS from theme configuration
 */
export function generateThemeCSS(theme: ThemeConfig): string {
  let css = ':root {\n';

  // Colors
  Object.entries(theme.colors).forEach(([category, colors]) => {
    if (typeof colors === 'object') {
      Object.entries(colors).forEach(([key, value]) => {
        css += `  --color-${category}-${key}: ${value};\n`;
      });
    }
  });

  // Typography with safe access
  const fontFamily = theme.typography?.fontFamily?.primary || theme.typography?.fontFamily || 'Inter, system-ui, sans-serif';
  css += `  --font-family-primary: ${fontFamily};\n`;

  if (theme.typography?.fontFamily?.secondary) {
    css += `  --font-family-secondary: ${theme.typography.fontFamily.secondary};\n`;
  }

  if (theme.typography?.fontSize) {
    Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
      css += `  --font-size-${key}: ${value};\n`;
    });
  }

  if (theme.typography?.fontWeight) {
    Object.entries(theme.typography.fontWeight).forEach(([key, value]) => {
      css += `  --font-weight-${key}: ${value};\n`;
    });
  }

  // Spacing with safe access
  if (theme.spacing) {
    Object.entries(theme.spacing).forEach(([key, value]) => {
      css += `  --spacing-${key}: ${value};\n`;
    });
  }

  // Border radius with safe access
  if (theme.borderRadius) {
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      css += `  --border-radius-${key}: ${value};\n`;
    });
  }

  // Shadows with safe access
  if (theme.shadows) {
    Object.entries(theme.shadows).forEach(([key, value]) => {
      css += `  --shadow-${key}: ${value};\n`;
    });
  }

  // Layout with safe access
  if (theme.layout) {
    css += `  --layout-max-width: ${theme.layout.maxWidth || '1200px'};\n`;
    css += `  --layout-container-padding: ${theme.layout.containerPadding || '1rem'};\n`;

    if (theme.layout.header) {
      css += `  --layout-header-height: ${theme.layout.header.height || '64px'};\n`;
      css += `  --layout-header-background: ${theme.layout.header.background || '#FFFFFF'};\n`;
    }

    if (theme.layout.footer) {
      css += `  --layout-footer-background: ${theme.layout.footer.background || '#F5F5F5'};\n`;
    }
  }

  // Custom variables
  if (theme.customVariables) {
    Object.entries(theme.customVariables).forEach(([key, value]) => {
      css += `  --${key}: ${value};\n`;
    });
  }

  css += '}\n';

  return css;
}

/**
 * Convert hex color to RGB
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Generate lighter/darker variants of a color
 */
export function generateColorVariants(baseColor: string): { light: string; dark: string } {
  // This is a simplified implementation
  // In a real implementation, you might want to use a color manipulation library
  return {
    light: baseColor, // Placeholder
    dark: baseColor,  // Placeholder
  };
}

// Export Tailwind utilities
export * from './tailwind-utils';

// Export TailwindCSS service
export * from './tailwind-css-service';
export { tailwindCSSService } from './tailwind-css-service';

// Export performance optimizer
export * from './performance-optimizer';
export { performanceOptimizer } from './performance-optimizer';

// Export debounce utility
export * from './debounce';
export { debounce, throttle } from './debounce';


