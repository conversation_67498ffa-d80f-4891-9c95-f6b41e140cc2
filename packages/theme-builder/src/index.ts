// Main exports for the theme builder package

// Import CSS styles for Tailwind integration
import './styles/globals.css';

export * from './components';
export * from './editor';
export * from './renderer';
export * from './types';
export * from './utils';
export * from './hooks';
export * from './services';

// Re-export commonly used types and utilities
export type {
  ThemeConfig,
  ComponentConfig,
  ZMPComponent,
  PuckConfig,
  ThemeBuilderProps,
  HeaderAction,
} from './types';

export {
  createThemeConfig,
  applyThemeConfig,
  validateThemeConfig,
} from './utils';

export {
  isValidPuckData,
  createEmptyPuckData,
} from './utils/data-validation';

export {
  migrate,
  transformProps,
} from '@measured/puck';

export {
  ThemeBuilder,
  EnhancedThemeBuilder,
  GlobalStylesTab,
} from './editor';

export {
  ThemeRenderer,
} from './renderer';

export {
  useTheme,
  useThemeBuilder,
} from './hooks';


