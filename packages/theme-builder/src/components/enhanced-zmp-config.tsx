/**
 * Enhanced <PERSON>MP Puck Config with Global Styling
 * Provides Elementor-like styling capabilities for all components
 */

import { Config } from '@measured/puck';
import {
  EnhancedZ<PERSON>Button,
  EnhancedZMPText,
  EnhancedZMPCard,
  EnhancedZMPGrid,
  EnhancedZMPContainer,
} from './enhanced-zmp-components';
import { createGlobalStyleField } from './global-style-system';

// Enhanced ZMP Root with Global Styling
const EnhancedZMPRoot = {
  fields: {
    title: {
      type: 'text' as const,
      label: 'Page Title',
    },
    description: {
      type: 'textarea' as const,
      label: 'Page Description',
    },
    backgroundColor: {
      type: 'select' as const,
      label: 'Page Background',
      options: [
        { label: 'White', value: 'bg-white' },
        { label: 'Gray 50', value: 'bg-gray-50' },
        { label: 'Gray 100', value: 'bg-gray-100' },
        { label: 'Blue 50', value: 'bg-blue-50' },
        { label: 'Green 50', value: 'bg-green-50' },
        { label: 'Custom', value: '' },
      ],
    },
    globalStyles: createGlobalStyleField('Root', ['colors', 'spacing', 'layout']),
    customClasses: {
      type: 'text' as const,
      label: 'Custom Page Classes',
      placeholder: 'min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100',
    },
  },
  render: ({ title, description, backgroundColor, globalStyles, customClasses, children }: any) => {
    const pageClasses = [
      'min-h-screen',
      backgroundColor || 'bg-white',
      globalStyles,
      customClasses,
    ].filter(Boolean).join(' ');

    return (
      <div className={pageClasses}>
        {title && (
          <head>
            <title>{title}</title>
            {description && <meta name="description" content={description} />}
          </head>
        )}
        <div className="relative">
          {children}
        </div>
      </div>
    );
  },
};

// Enhanced ZMP Puck Configuration
export const ENHANCED_ZMP_PUCK_CONFIG: Config = {
  components: {
    // Layout Components
    Container: EnhancedZMPContainer,
    Grid: EnhancedZMPGrid,
    
    // Display Components
    Card: EnhancedZMPCard,
    
    // Typography Components
    Text: EnhancedZMPText,
    
    // Form Components
    Button: EnhancedZMPButton,
    
    // Add more enhanced components as needed...
  },
  
  root: EnhancedZMPRoot,
  
  // Categories for better organization
  categories: {
    layout: {
      title: 'Layout',
      components: ['Container', 'Grid'],
    },
    display: {
      title: 'Display',
      components: ['Card'],
    },
    typography: {
      title: 'Typography',
      components: ['Text'],
    },
    form: {
      title: 'Form',
      components: ['Button'],
    },
  },
};

/**
 * Create Enhanced Component with Global Styling
 * Helper function to add global styling to any component
 */
export const createEnhancedComponent = (
  baseComponent: any,
  componentType: string,
  allowedCategories?: string[]
) => {
  return {
    ...baseComponent,
    fields: {
      ...baseComponent.fields,
      globalStyles: createGlobalStyleField(
        componentType,
        allowedCategories as any
      ),
      customClasses: {
        type: 'text' as const,
        label: 'Custom Classes',
        placeholder: 'Enter custom Tailwind classes...',
      },
    },
    render: (props: any) => {
      // Extract styling props
      const { globalStyles, customClasses, ...otherProps } = props;
      
      // If the base component has a custom render that handles className
      if (baseComponent.render) {
        return baseComponent.render({
          ...otherProps,
          className: [
            otherProps.className,
            globalStyles,
            customClasses,
          ].filter(Boolean).join(' '),
        });
      }
      
      // Default render
      return (
        <div 
          className={[globalStyles, customClasses].filter(Boolean).join(' ')}
        >
          {baseComponent.render ? baseComponent.render(otherProps) : 'Component'}
        </div>
      );
    },
  };
};

/**
 * Global Style Presets
 * Common style combinations for quick application
 */
export const STYLE_PRESETS = {
  buttons: {
    primary: 'bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors',
    secondary: 'bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors',
    outline: 'border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-600 hover:text-white transition-all',
    ghost: 'text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50 transition-colors',
  },
  cards: {
    simple: 'bg-white rounded-lg shadow-md p-6',
    elevated: 'bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow',
    bordered: 'bg-white border-2 border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors',
    gradient: 'bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-6 shadow-md',
  },
  containers: {
    centered: 'max-w-4xl mx-auto px-4 py-8',
    fullWidth: 'w-full px-6 py-12',
    narrow: 'max-w-2xl mx-auto px-4 py-6',
    wide: 'max-w-6xl mx-auto px-8 py-12',
  },
  text: {
    heading: 'text-3xl font-bold text-gray-900 mb-4',
    subheading: 'text-xl font-semibold text-gray-700 mb-3',
    body: 'text-base text-gray-600 leading-relaxed',
    caption: 'text-sm text-gray-500',
  },
};

/**
 * Add Style Presets Field
 */
export const createStylePresetsField = (category: keyof typeof STYLE_PRESETS) => ({
  type: 'select' as const,
  label: 'Style Presets',
  options: [
    { label: 'None', value: '' },
    ...Object.entries(STYLE_PRESETS[category]).map(([key, value]) => ({
      label: key.charAt(0).toUpperCase() + key.slice(1),
      value,
    })),
  ],
});

export default ENHANCED_ZMP_PUCK_CONFIG;
