/**
 * Global Style System for Puck Editor
 * Provides Elementor-like styling interface with TailwindCSS
 */

import React, { useState, useCallback } from 'react';
import { FieldLabel } from '@measured/puck';

// Style categories configuration
export const STYLE_CATEGORIES = {
  layout: {
    title: 'Layout',
    icon: '📐',
    components: ['Grid', 'Flex', 'Container', 'Section'],
    styles: {
      display: {
        label: 'Display',
        options: [
          { label: 'Block', value: 'block' },
          { label: 'Flex', value: 'flex' },
          { label: 'Grid', value: 'grid' },
          { label: 'Inline', value: 'inline' },
          { label: 'Hidden', value: 'hidden' },
        ],
      },
      flexDirection: {
        label: 'Flex Direction',
        options: [
          { label: 'Row', value: 'flex-row' },
          { label: 'Column', value: 'flex-col' },
          { label: 'Row Reverse', value: 'flex-row-reverse' },
          { label: 'Column Reverse', value: 'flex-col-reverse' },
        ],
      },
      justifyContent: {
        label: 'Justify Content',
        options: [
          { label: 'Start', value: 'justify-start' },
          { label: 'Center', value: 'justify-center' },
          { label: 'End', value: 'justify-end' },
          { label: 'Between', value: 'justify-between' },
          { label: 'Around', value: 'justify-around' },
          { label: 'Evenly', value: 'justify-evenly' },
        ],
      },
      alignItems: {
        label: 'Align Items',
        options: [
          { label: 'Start', value: 'items-start' },
          { label: 'Center', value: 'items-center' },
          { label: 'End', value: 'items-end' },
          { label: 'Stretch', value: 'items-stretch' },
          { label: 'Baseline', value: 'items-baseline' },
        ],
      },
      gap: {
        label: 'Gap',
        options: [
          { label: 'None', value: 'gap-0' },
          { label: 'XS', value: 'gap-1' },
          { label: 'SM', value: 'gap-2' },
          { label: 'MD', value: 'gap-4' },
          { label: 'LG', value: 'gap-6' },
          { label: 'XL', value: 'gap-8' },
        ],
      },
    },
  },
  spacing: {
    title: 'Spacing',
    icon: '📏',
    components: ['All'],
    styles: {
      padding: {
        label: 'Padding',
        options: [
          { label: 'None', value: 'p-0' },
          { label: 'XS', value: 'p-1' },
          { label: 'SM', value: 'p-2' },
          { label: 'MD', value: 'p-4' },
          { label: 'LG', value: 'p-6' },
          { label: 'XL', value: 'p-8' },
          { label: '2XL', value: 'p-12' },
        ],
      },
      margin: {
        label: 'Margin',
        options: [
          { label: 'None', value: 'm-0' },
          { label: 'XS', value: 'm-1' },
          { label: 'SM', value: 'm-2' },
          { label: 'MD', value: 'm-4' },
          { label: 'LG', value: 'm-6' },
          { label: 'XL', value: 'm-8' },
          { label: 'Auto', value: 'm-auto' },
        ],
      },
    },
  },
  typography: {
    title: 'Typography',
    icon: '🔤',
    components: ['Text', 'Heading', 'Button'],
    styles: {
      fontSize: {
        label: 'Font Size',
        options: [
          { label: 'XS', value: 'text-xs' },
          { label: 'SM', value: 'text-sm' },
          { label: 'Base', value: 'text-base' },
          { label: 'LG', value: 'text-lg' },
          { label: 'XL', value: 'text-xl' },
          { label: '2XL', value: 'text-2xl' },
          { label: '3XL', value: 'text-3xl' },
          { label: '4XL', value: 'text-4xl' },
        ],
      },
      fontWeight: {
        label: 'Font Weight',
        options: [
          { label: 'Light', value: 'font-light' },
          { label: 'Normal', value: 'font-normal' },
          { label: 'Medium', value: 'font-medium' },
          { label: 'Semibold', value: 'font-semibold' },
          { label: 'Bold', value: 'font-bold' },
          { label: 'Black', value: 'font-black' },
        ],
      },
      textAlign: {
        label: 'Text Align',
        options: [
          { label: 'Left', value: 'text-left' },
          { label: 'Center', value: 'text-center' },
          { label: 'Right', value: 'text-right' },
          { label: 'Justify', value: 'text-justify' },
        ],
      },
      lineHeight: {
        label: 'Line Height',
        options: [
          { label: 'Tight', value: 'leading-tight' },
          { label: 'Normal', value: 'leading-normal' },
          { label: 'Relaxed', value: 'leading-relaxed' },
          { label: 'Loose', value: 'leading-loose' },
        ],
      },
    },
  },
  colors: {
    title: 'Colors',
    icon: '🎨',
    components: ['All'],
    styles: {
      backgroundColor: {
        label: 'Background Color',
        options: [
          { label: 'Transparent', value: 'bg-transparent' },
          { label: 'White', value: 'bg-white' },
          { label: 'Gray 50', value: 'bg-gray-50' },
          { label: 'Gray 100', value: 'bg-gray-100' },
          { label: 'Gray 500', value: 'bg-gray-500' },
          { label: 'Gray 900', value: 'bg-gray-900' },
          { label: 'Blue 500', value: 'bg-blue-500' },
          { label: 'Blue 600', value: 'bg-blue-600' },
          { label: 'Green 500', value: 'bg-green-500' },
          { label: 'Red 500', value: 'bg-red-500' },
          { label: 'Yellow 500', value: 'bg-yellow-500' },
          { label: 'Purple 500', value: 'bg-purple-500' },
        ],
      },
      textColor: {
        label: 'Text Color',
        options: [
          { label: 'Black', value: 'text-black' },
          { label: 'White', value: 'text-white' },
          { label: 'Gray 500', value: 'text-gray-500' },
          { label: 'Gray 700', value: 'text-gray-700' },
          { label: 'Gray 900', value: 'text-gray-900' },
          { label: 'Blue 500', value: 'text-blue-500' },
          { label: 'Blue 600', value: 'text-blue-600' },
          { label: 'Green 500', value: 'text-green-500' },
          { label: 'Red 500', value: 'text-red-500' },
        ],
      },
    },
  },
  borders: {
    title: 'Borders',
    icon: '🔲',
    components: ['All'],
    styles: {
      borderWidth: {
        label: 'Border Width',
        options: [
          { label: 'None', value: 'border-0' },
          { label: 'Thin', value: 'border' },
          { label: 'Thick', value: 'border-2' },
          { label: 'Extra Thick', value: 'border-4' },
        ],
      },
      borderRadius: {
        label: 'Border Radius',
        options: [
          { label: 'None', value: 'rounded-none' },
          { label: 'SM', value: 'rounded-sm' },
          { label: 'MD', value: 'rounded-md' },
          { label: 'LG', value: 'rounded-lg' },
          { label: 'XL', value: 'rounded-xl' },
          { label: 'Full', value: 'rounded-full' },
        ],
      },
      borderColor: {
        label: 'Border Color',
        options: [
          { label: 'Gray 200', value: 'border-gray-200' },
          { label: 'Gray 300', value: 'border-gray-300' },
          { label: 'Gray 500', value: 'border-gray-500' },
          { label: 'Blue 500', value: 'border-blue-500' },
          { label: 'Green 500', value: 'border-green-500' },
          { label: 'Red 500', value: 'border-red-500' },
        ],
      },
    },
  },
  effects: {
    title: 'Effects',
    icon: '✨',
    components: ['All'],
    styles: {
      shadow: {
        label: 'Shadow',
        options: [
          { label: 'None', value: 'shadow-none' },
          { label: 'SM', value: 'shadow-sm' },
          { label: 'MD', value: 'shadow-md' },
          { label: 'LG', value: 'shadow-lg' },
          { label: 'XL', value: 'shadow-xl' },
          { label: '2XL', value: 'shadow-2xl' },
        ],
      },
      opacity: {
        label: 'Opacity',
        options: [
          { label: '0%', value: 'opacity-0' },
          { label: '25%', value: 'opacity-25' },
          { label: '50%', value: 'opacity-50' },
          { label: '75%', value: 'opacity-75' },
          { label: '100%', value: 'opacity-100' },
        ],
      },
      transform: {
        label: 'Transform',
        options: [
          { label: 'None', value: '' },
          { label: 'Scale 105%', value: 'scale-105' },
          { label: 'Scale 110%', value: 'scale-110' },
          { label: 'Rotate 45°', value: 'rotate-45' },
          { label: 'Rotate 90°', value: 'rotate-90' },
        ],
      },
    },
  },
  responsive: {
    title: 'Responsive',
    icon: '📱',
    components: ['All'],
    styles: {
      breakpoint: {
        label: 'Breakpoint',
        options: [
          { label: 'Mobile (default)', value: '' },
          { label: 'SM (640px+)', value: 'sm:' },
          { label: 'MD (768px+)', value: 'md:' },
          { label: 'LG (1024px+)', value: 'lg:' },
          { label: 'XL (1280px+)', value: 'xl:' },
        ],
      },
    },
  },
} as const;

export type StyleCategory = keyof typeof STYLE_CATEGORIES;
export type StyleProperty = string;
export type StyleValue = string;

/**
 * Global Style Panel Component
 * Provides Elementor-like styling interface
 */
interface GlobalStylePanelProps {
  value: string;
  onChange: (value: string) => void;
  componentType?: string;
  categories?: StyleCategory[];
}

export const GlobalStylePanel: React.FC<GlobalStylePanelProps> = ({
  value = '',
  onChange,
  componentType = 'All',
  categories = Object.keys(STYLE_CATEGORIES) as StyleCategory[],
}) => {
  const [activeCategory, setActiveCategory] = useState<StyleCategory>('layout');
  const [appliedClasses, setAppliedClasses] = useState<string[]>(
    value.split(' ').filter(Boolean)
  );

  // Update applied classes when value changes
  React.useEffect(() => {
    setAppliedClasses(value.split(' ').filter(Boolean));
  }, [value]);

  const handleClassToggle = useCallback((className: string) => {
    const newClasses = appliedClasses.includes(className)
      ? appliedClasses.filter(cls => cls !== className)
      : [...appliedClasses, className];

    setAppliedClasses(newClasses);
    onChange(newClasses.join(' '));
  }, [appliedClasses, onChange]);

  const handleClassRemove = useCallback((className: string) => {
    const newClasses = appliedClasses.filter(cls => cls !== className);
    setAppliedClasses(newClasses);
    onChange(newClasses.join(' '));
  }, [appliedClasses, onChange]);

  const isClassApplied = useCallback((className: string) => {
    return appliedClasses.includes(className);
  }, [appliedClasses]);

  // Filter categories based on component type
  const availableCategories = categories.filter(categoryKey => {
    const category = STYLE_CATEGORIES[categoryKey];
    return category.components.includes('All') ||
           category.components.includes(componentType);
  });

  return (
    <div className="global-style-panel">
      {/* Applied Classes Display */}
      {appliedClasses.length > 0 && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium mb-2">Applied Classes:</div>
          <div className="flex flex-wrap gap-1">
            {appliedClasses.map((className, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
              >
                {className}
                <button
                  onClick={() => handleClassRemove(className)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-1 mb-4 border-b">
        {availableCategories.map((categoryKey) => {
          const category = STYLE_CATEGORIES[categoryKey];
          return (
            <button
              key={categoryKey}
              onClick={() => setActiveCategory(categoryKey)}
              className={`px-3 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                activeCategory === categoryKey
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.icon} {category.title}
            </button>
          );
        })}
      </div>

      {/* Style Options */}
      <div className="space-y-4">
        {Object.entries(STYLE_CATEGORIES[activeCategory].styles).map(([styleKey, styleConfig]) => (
          <div key={styleKey} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {styleConfig.label}
            </label>
            <div className="grid grid-cols-2 gap-2">
              {styleConfig.options.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleClassToggle(option.value)}
                  className={`px-3 py-2 text-sm rounded border transition-colors ${
                    isClassApplied(option.value)
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Custom Class Input */}
      <div className="mt-6 pt-4 border-t">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Custom Classes
        </label>
        <input
          type="text"
          placeholder="Enter custom Tailwind classes..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              const customClass = e.currentTarget.value.trim();
              if (customClass) {
                handleClassToggle(customClass);
                e.currentTarget.value = '';
              }
            }
          }}
        />
        <div className="text-xs text-gray-500 mt-1">
          Press Enter to add custom classes
        </div>
      </div>
    </div>
  );
};

/**
 * Custom Field for Global Styling in Sidebar
 * To be used in Puck component configurations
 */
export const createGlobalStyleField = (
  componentType: string = 'All',
  categories?: StyleCategory[]
) => ({
  type: 'custom' as const,
  render: ({ name, onChange, value }: any) => {
    // Import here to avoid circular dependency
    const { GlobalStylesSidebar } = require('./global-styles-sidebar');

    return (
      <GlobalStylesSidebar
        value={value || ''}
        onChange={onChange}
        componentType={componentType}
        categories={categories}
      />
    );
  },
});

/**
 * Quick Style Selector Component
 * For simple style selection without full panel
 */
interface QuickStyleSelectorProps {
  value: string;
  onChange: (value: string) => void;
  category: StyleCategory;
  property: string;
  label?: string;
}

export const QuickStyleSelector: React.FC<QuickStyleSelectorProps> = ({
  value,
  onChange,
  category,
  property,
  label,
}) => {
  const styleConfig = STYLE_CATEGORIES[category]?.styles[property];

  if (!styleConfig) {
    return <div>Style configuration not found</div>;
  }

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
      >
        <option value="">Select {styleConfig.label}</option>
        {styleConfig.options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

/**
 * Create Quick Style Field
 */
export const createQuickStyleField = (
  category: StyleCategory,
  property: string,
  label?: string
) => ({
  type: 'custom' as const,
  render: ({ name, onChange, value }: any) => (
    <QuickStyleSelector
      value={value || ''}
      onChange={onChange}
      category={category}
      property={property}
      label={label}
    />
  ),
});
