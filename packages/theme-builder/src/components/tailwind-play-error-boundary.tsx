/**
 * Error Boundary for TailwindPlayProvider
 * Catches and handles errors in Tailwind Play CDN loading
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class TailwindPlayErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('TailwindPlayProvider error:', error, errorInfo);
    
    // You can also log the error to an error reporting service here
    if (typeof window !== 'undefined' && window.console) {
      console.group('TailwindPlay Error Details');
      console.error('Error:', error.message);
      console.error('Stack:', error.stack);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return this.props.fallback || (
        <div className="tailwind-play-error-fallback">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  TailwindCSS Preview Unavailable
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>
                    The real-time TailwindCSS preview is temporarily unavailable. 
                    Your styling will still work, but you may not see live updates.
                  </p>
                  {this.state.error && (
                    <details className="mt-2">
                      <summary className="cursor-pointer font-medium">
                        Technical Details
                      </summary>
                      <pre className="mt-1 text-xs bg-yellow-100 p-2 rounded overflow-auto">
                        {this.state.error.message}
                      </pre>
                    </details>
                  )}
                </div>
                <div className="mt-3">
                  <button
                    onClick={() => {
                      this.setState({ hasError: false, error: undefined });
                      // Optionally reload the page or retry loading
                      window.location.reload();
                    }}
                    className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded text-sm font-medium transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
          {this.props.children}
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * HOC to wrap components with TailwindPlay error boundary
 */
export function withTailwindPlayErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <TailwindPlayErrorBoundary fallback={fallback}>
      <Component {...props} />
    </TailwindPlayErrorBoundary>
  );

  WrappedComponent.displayName = `withTailwindPlayErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

export default TailwindPlayErrorBoundary;
