/**
 * TailwindPlayProvider - Provides real-time TailwindCSS styling via Play CDN
 * Implements <200ms preview performance for dynamic class styling
 */

import React, { useEffect, useRef, useCallback } from 'react';
import { TailwindPlayErrorBoundary } from './tailwind-play-error-boundary';

interface TailwindPlayProviderProps {
  children: React.ReactNode;
  theme?: {
    colors?: Record<string, any>;
    fontFamily?: Record<string, string[]>;
    spacing?: Record<string, string>;
    borderRadius?: Record<string, string>;
  };
  enabled?: boolean;
}

// Default theme config synchronized with TailwindCSS CLI
const DEFAULT_TAILWIND_CONFIG = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe', 
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0', 
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        accent: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        heading: ['Inter', 'system-ui', 'sans-serif'],
        body: ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
    },
  },
  corePlugins: {
    preflight: false, // Disable preflight to avoid conflicts
  },
};

export const TailwindPlayProvider: React.FC<TailwindPlayProviderProps> = ({
  children,
  theme,
  enabled = true,
}) => {
  const scriptRef = useRef<HTMLScriptElement | null>(null);
  const configRef = useRef<HTMLScriptElement | null>(null);
  const isLoadedRef = useRef(false);
  const isMountedRef = useRef(true);

  // Safe element removal function
  const safeRemoveElement = useCallback((element: HTMLElement | null, description: string) => {
    if (!element) return;

    try {
      // Check if element is still in the DOM
      if (element.parentNode && document.contains(element)) {
        element.parentNode.removeChild(element);
        console.log(`Successfully removed ${description}`);
      } else {
        console.log(`${description} was already removed from DOM`);
      }
    } catch (error) {
      console.warn(`Failed to remove ${description}:`, error);
    }
  }, []);

  useEffect(() => {
    if (!enabled || isLoadedRef.current) return;

    // Load Tailwind Play CDN
    const loadTailwindPlay = () => {
      // Remove existing scripts safely
      safeRemoveElement(scriptRef.current, 'Tailwind Play script');
      safeRemoveElement(configRef.current, 'Tailwind config script');

      // Add Tailwind Play CDN script
      const script = document.createElement('script');
      script.src = 'https://cdn.tailwindcss.com';
      script.async = true;
      script.onload = () => {
        console.log('Tailwind Play CDN loaded');
        
        // Configure Tailwind after it loads
        const configScript = document.createElement('script');
        configScript.innerHTML = `
          if (typeof tailwind !== 'undefined') {
            tailwind.config = ${JSON.stringify(mergeThemeConfig(theme))};
            console.log('Tailwind Play configured with theme:', tailwind.config);
          }
        `;
        document.head.appendChild(configScript);
        configRef.current = configScript;
        
        isLoadedRef.current = true;
      };
      script.onerror = () => {
        console.error('Failed to load Tailwind Play CDN');
      };
      
      document.head.appendChild(script);
      scriptRef.current = script;
    };

    loadTailwindPlay();

    // Cleanup on unmount
    return () => {
      isMountedRef.current = false;
      safeRemoveElement(scriptRef.current, 'Tailwind Play script (cleanup)');
      safeRemoveElement(configRef.current, 'Tailwind config script (cleanup)');
      isLoadedRef.current = false;
      scriptRef.current = null;
      configRef.current = null;
    };
  }, [enabled]);

  // Update config when theme changes
  useEffect(() => {
    if (!enabled || !isLoadedRef.current) return;

    const updateConfig = () => {
      if (typeof window !== 'undefined' && (window as any).tailwind) {
        (window as any).tailwind.config = mergeThemeConfig(theme);
        console.log('Tailwind Play config updated');
      }
    };

    // Small delay to ensure DOM updates are processed
    const timeoutId = setTimeout(updateConfig, 50);
    return () => clearTimeout(timeoutId);
  }, [theme, enabled]);

  return (
    <TailwindPlayErrorBoundary>
      {children}
    </TailwindPlayErrorBoundary>
  );
};

/**
 * Merge custom theme with default Tailwind config
 */
function mergeThemeConfig(customTheme?: TailwindPlayProviderProps['theme']) {
  if (!customTheme) return DEFAULT_TAILWIND_CONFIG;

  const merged = { ...DEFAULT_TAILWIND_CONFIG };
  
  if (customTheme.colors) {
    merged.theme.extend.colors = {
      ...merged.theme.extend.colors,
      ...customTheme.colors,
    };
  }

  if (customTheme.fontFamily) {
    merged.theme.extend.fontFamily = {
      ...merged.theme.extend.fontFamily,
      ...customTheme.fontFamily,
    };
  }

  if (customTheme.spacing) {
    merged.theme.extend.spacing = {
      ...merged.theme.extend.spacing,
      ...customTheme.spacing,
    };
  }

  if (customTheme.borderRadius) {
    merged.theme.extend.borderRadius = {
      ...merged.theme.extend.borderRadius,
      ...customTheme.borderRadius,
    };
  }

  return merged;
}

/**
 * Hook to check if Tailwind Play is loaded and ready
 */
export const useTailwindPlay = () => {
  const [isReady, setIsReady] = React.useState(false);

  useEffect(() => {
    const checkReady = () => {
      if (typeof window !== 'undefined' && (window as any).tailwind) {
        setIsReady(true);
      } else {
        setTimeout(checkReady, 100);
      }
    };

    checkReady();
  }, []);

  return { isReady };
};

export default TailwindPlayProvider;
