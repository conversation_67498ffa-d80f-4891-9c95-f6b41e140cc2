import React from 'react';

import { DefaultRootProps, RootConfig } from '@measured/puck';

import {
  BORDER_RADIUS_OPTIONS,
  DEFAULT_COLORS,
  DEFAULT_THEME_NAME,
  DEFAULT_TYPOGRAPHY,
  FONT_FAMILY_OPTIONS,
  SPACING_OPTIONS,
} from '../constants/theme-defaults';
import { cn, generateCSSVariables, TAILWIND_OPTIONS } from '../utils/tailwind-utils';

export type ZMPRootProps = DefaultRootProps & {
  title?: string;
  // Colors - Primary
  primaryColor?: string;
  primaryLightColor?: string;
  primaryDarkColor?: string;
  // Colors - Secondary
  secondaryColor?: string;
  secondaryLightColor?: string;
  secondaryDarkColor?: string;
  // Colors - Accent
  accentColor?: string;
  accentLightColor?: string;
  accentDarkColor?: string;
  // Colors - Background
  backgroundColor?: string;
  paperBackgroundColor?: string;
  // Colors - Text
  textColor?: string;
  secondaryTextColor?: string;
  // Typography
  fontFamily?: string;
  headingsFontFamily?: string;
  headingsFontWeight?: string;
  bodyFontFamily?: string;
  bodyFontWeight?: string;
  // Layout
  borderRadius?: string;
  spacing?: string;
  // Tailwind Dynamic Styling
  tailwindTheme?: keyof typeof TAILWIND_OPTIONS.colors;
  containerClasses?: string;
  enableTailwindStyling?: boolean;
};

export const ZMPRoot: RootConfig<ZMPRootProps> = {
  defaultProps: {
    title: DEFAULT_THEME_NAME,
    // Colors - Primary
    primaryColor: DEFAULT_COLORS.primary.main,
    primaryLightColor: DEFAULT_COLORS.primary.light,
    primaryDarkColor: DEFAULT_COLORS.primary.dark,
    // Colors - Secondary
    secondaryColor: DEFAULT_COLORS.secondary.main,
    secondaryLightColor: DEFAULT_COLORS.secondary.light,
    secondaryDarkColor: DEFAULT_COLORS.secondary.dark,
    // Colors - Accent
    accentColor: DEFAULT_COLORS.accent.main,
    accentLightColor: DEFAULT_COLORS.accent.light,
    accentDarkColor: DEFAULT_COLORS.accent.dark,
    // Colors - Background
    backgroundColor: DEFAULT_COLORS.background.default,
    paperBackgroundColor: DEFAULT_COLORS.background.paper,
    // Colors - Text
    textColor: DEFAULT_COLORS.text.primary,
    secondaryTextColor: DEFAULT_COLORS.text.secondary,
    // Typography
    fontFamily: DEFAULT_TYPOGRAPHY.fontFamily,
    headingsFontFamily: DEFAULT_TYPOGRAPHY.headings.fontFamily,
    headingsFontWeight: DEFAULT_TYPOGRAPHY.headings.fontWeight,
    bodyFontFamily: DEFAULT_TYPOGRAPHY.body.fontFamily,
    bodyFontWeight: DEFAULT_TYPOGRAPHY.body.fontWeight,
    // Layout
    borderRadius: '8px',
    spacing: '16px',
  },
  fields: {
    title: {
      type: 'text',
      label: 'App Title',
    },
    // Primary Colors
    primaryColor: {
      type: 'text',
      label: 'Primary Color',
    },
    primaryLightColor: {
      type: 'text',
      label: 'Primary Light Color',
    },
    primaryDarkColor: {
      type: 'text',
      label: 'Primary Dark Color',
    },
    // Secondary Colors
    secondaryColor: {
      type: 'text',
      label: 'Secondary Color',
    },
    secondaryLightColor: {
      type: 'text',
      label: 'Secondary Light Color',
    },
    secondaryDarkColor: {
      type: 'text',
      label: 'Secondary Dark Color',
    },
    // Accent Colors
    accentColor: {
      type: 'text',
      label: 'Accent Color',
    },
    accentLightColor: {
      type: 'text',
      label: 'Accent Light Color',
    },
    accentDarkColor: {
      type: 'text',
      label: 'Accent Dark Color',
    },
    // Background Colors
    backgroundColor: {
      type: 'text',
      label: 'Background Color',
    },
    paperBackgroundColor: {
      type: 'text',
      label: 'Paper Background Color',
    },
    // Text Colors
    textColor: {
      type: 'text',
      label: 'Primary Text Color',
    },
    secondaryTextColor: {
      type: 'text',
      label: 'Secondary Text Color',
    },
    // Typography
    fontFamily: {
      type: 'select',
      label: 'Font Family',
      options: FONT_FAMILY_OPTIONS,
    },
    headingsFontFamily: {
      type: 'select',
      label: 'Headings Font Family',
      options: FONT_FAMILY_OPTIONS,
    },
    headingsFontWeight: {
      type: 'select',
      label: 'Headings Font Weight',
      options: [
        { label: 'Normal', value: '400' },
        { label: 'Medium', value: '500' },
        { label: 'Semi Bold', value: '600' },
        { label: 'Bold', value: '700' },
      ],
    },
    bodyFontFamily: {
      type: 'select',
      label: 'Body Font Family',
      options: FONT_FAMILY_OPTIONS,
    },
    bodyFontWeight: {
      type: 'select',
      label: 'Body Font Weight',
      options: [
        { label: 'Light', value: '300' },
        { label: 'Normal', value: '400' },
        { label: 'Medium', value: '500' },
      ],
    },
    // Layout
    borderRadius: {
      type: 'select',
      label: 'Border Radius',
      options: BORDER_RADIUS_OPTIONS,
    },
    spacing: {
      type: 'select',
      label: 'Base Spacing',
      options: SPACING_OPTIONS,
    },
    // Tailwind Dynamic Styling
    tailwindTheme: {
      type: 'select',
      label: 'Tailwind Theme',
      options: Object.entries(TAILWIND_OPTIONS.colors).map(([key, value]) => ({
        label: value.label,
        value: key,
      })),
    },
    containerClasses: {
      type: 'text',
      label: 'Container CSS Classes',
      placeholder: 'e.g., max-w-4xl mx-auto',
    },
    enableTailwindStyling: {
      type: 'radio',
      label: 'Enable Tailwind Styling',
      options: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
    },
  },
  render: ({
    puck: { isEditing },
    title,
    primaryColor,
    primaryLightColor,
    primaryDarkColor,
    secondaryColor,
    secondaryLightColor,
    secondaryDarkColor,
    accentColor,
    accentLightColor,
    accentDarkColor,
    backgroundColor,
    paperBackgroundColor,
    textColor,
    secondaryTextColor,
    fontFamily,
    headingsFontFamily,
    headingsFontWeight,
    bodyFontFamily,
    bodyFontWeight,
    borderRadius,
    spacing,
    tailwindTheme,
    containerClasses,
    enableTailwindStyling,
    children,
  }) => {
    // Generate CSS variables from theme config
    const themeConfig = {
      colors: {
        primary: { main: primaryColor!, light: primaryLightColor!, dark: primaryDarkColor! },
        secondary: { main: secondaryColor!, light: secondaryLightColor!, dark: secondaryDarkColor! },
        accent: { main: accentColor!, light: accentLightColor!, dark: accentDarkColor! },
        background: { default: backgroundColor!, paper: paperBackgroundColor! },
        text: { primary: textColor!, secondary: secondaryTextColor! },
      },
      typography: {
        fontFamily: fontFamily!,
        headings: { fontFamily: headingsFontFamily!, fontWeight: headingsFontWeight! },
        body: { fontFamily: bodyFontFamily!, fontWeight: bodyFontWeight! },
      },
    };

    const cssVariables = generateCSSVariables(themeConfig);

    // Generate dynamic Tailwind classes
    const containerClassName = cn(
      'zmp-root',
      enableTailwindStyling && 'min-h-screen',
      enableTailwindStyling && tailwindTheme && TAILWIND_OPTIONS.colors[tailwindTheme]?.bg.replace('bg-', 'bg-').replace('-600', '-50'),
      containerClasses,
      isEditing && 'zmp-editing-mode'
    );

    return (
      <div
        className={containerClassName}
        style={
          {
            backgroundColor: enableTailwindStyling ? undefined : backgroundColor,
            color: enableTailwindStyling ? undefined : textColor,
            fontFamily: enableTailwindStyling ? undefined : fontFamily,
            // CSS Custom Properties for ZMP components - Full color palette
            '--zmp-primary-color': primaryColor,
            '--zmp-primary-light-color': primaryLightColor,
            '--zmp-primary-dark-color': primaryDarkColor,
            '--zmp-secondary-color': secondaryColor,
            '--zmp-secondary-light-color': secondaryLightColor,
            '--zmp-secondary-dark-color': secondaryDarkColor,
            '--zmp-accent-color': accentColor,
            '--zmp-accent-light-color': accentLightColor,
            '--zmp-accent-dark-color': accentDarkColor,
            '--zmp-background-color': backgroundColor,
            '--zmp-paper-background-color': paperBackgroundColor,
            '--zmp-text-color': textColor,
            '--zmp-secondary-text-color': secondaryTextColor,
            '--zmp-font-family': fontFamily,
            '--zmp-headings-font-family': headingsFontFamily,
            '--zmp-headings-font-weight': headingsFontWeight,
            '--zmp-body-font-family': bodyFontFamily,
            '--zmp-body-font-weight': bodyFontWeight,
            '--zmp-border-radius': borderRadius,
            '--zmp-spacing': spacing,
            // Add generated CSS variables
            ...cssVariables,
          } as React.CSSProperties
        }
      >
        {/* Mobile App Header */}
        {/*        {isEditing && (
          <div
            style={{
              padding: spacing,
              backgroundColor: primaryColor,
              color: 'white',
              textAlign: 'center',
              fontSize: '18px',
              fontWeight: 'bold',
            }}
          >
            {title} - Editing Mode
          </div>
        )}*/}

        {/* Main Content Area */}
        <div
          style={{
            flex: 1,
            padding: isEditing ? spacing : '0',
            minHeight: isEditing ? '400px' : 'auto',
          }}
        >
          {children}
        </div>

        {/* Mobile App Footer */}
        {/*     {isEditing && (
          <div
            style={{
              padding: spacing,
              backgroundColor: secondaryColor,
              color: textColor,
              textAlign: 'center',
              fontSize: '12px',
              borderTop: `1px solid ${primaryColor}`,
            }}
          >
            Zalo Mini App Footer
          </div>
        )}*/}

        {/* Global Styles */}
        <style jsx global>{`
          .zmp-component {
            --primary: ${primaryColor};
            --primary-light: ${primaryLightColor};
            --primary-dark: ${primaryDarkColor};
            --secondary: ${secondaryColor};
            --secondary-light: ${secondaryLightColor};
            --secondary-dark: ${secondaryDarkColor};
            --accent: ${accentColor};
            --accent-light: ${accentLightColor};
            --accent-dark: ${accentDarkColor};
            --background: ${backgroundColor};
            --paper-background: ${paperBackgroundColor};
            --text: ${textColor};
            --secondary-text: ${secondaryTextColor};
            --font-family: ${fontFamily};
            --headings-font-family: ${headingsFontFamily};
            --headings-font-weight: ${headingsFontWeight};
            --body-font-family: ${bodyFontFamily};
            --body-font-weight: ${bodyFontWeight};
            --border-radius: ${borderRadius};
            --spacing: ${spacing};
            font-family: var(--font-family);
          }

          .zmp-button {
            border-radius: var(--border-radius);
            padding: calc(var(--spacing) * 0.5) var(--spacing);
            font-family: var(--font-family);
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .zmp-button-primary {
            background-color: var(--primary);
            color: white;
          }

          .zmp-button-primary:hover {
            background-color: var(--primary-dark);
          }

          .zmp-button-secondary {
            background-color: var(--secondary);
            color: var(--text);
            border: 1px solid var(--primary);
          }

          .zmp-button-secondary:hover {
            background-color: var(--secondary-light);
          }

          .zmp-button-accent {
            background-color: var(--accent);
            color: white;
          }

          .zmp-button-accent:hover {
            background-color: var(--accent-dark);
          }

          .zmp-text {
            color: var(--text);
            font-family: var(--body-font-family);
            font-weight: var(--body-font-weight);
            line-height: 1.5;
          }

          .zmp-text-secondary {
            color: var(--secondary-text);
          }

          .zmp-heading {
            font-family: var(--headings-font-family);
            font-weight: var(--headings-font-weight);
            color: var(--text);
          }

          .zmp-card {
            background-color: var(--paper-background);
            border-radius: var(--border-radius);
            padding: var(--spacing);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--secondary);
          }
        `}</style>
      </div>
    );
  },
};

export default ZMPRoot;
