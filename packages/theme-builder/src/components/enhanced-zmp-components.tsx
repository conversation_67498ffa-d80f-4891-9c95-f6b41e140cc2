/**
 * Enhanced ZMP Components with Global Styling
 * Integrates global style system with existing ZMP components
 */

import React from 'react';
import { ComponentConfig } from '@measured/puck';
import { 
  createGlobalStyleField, 
  createQuickStyleField,
  STYLE_CATEGORIES 
} from './global-style-system';

/**
 * Enhanced ZMP Button with Global Styling
 */
export const EnhancedZMPButton: ComponentConfig = {
  fields: {
    text: {
      type: 'text',
      label: 'Button Text',
    },
    variant: {
      type: 'select',
      label: 'Variant',
      options: [
        { label: 'Primary', value: 'primary' },
        { label: 'Secondary', value: 'secondary' },
        { label: 'Outline', value: 'outline' },
        { label: 'Ghost', value: 'ghost' },
      ],
    },
    size: createQuickStyleField('typography', 'fontSize', 'Size'),
    backgroundColor: createQuickStyleField('colors', 'backgroundColor', 'Background'),
    textColor: createQuickStyleField('colors', 'textColor', 'Text Color'),
    borderRadius: createQuickStyleField('borders', 'borderRadius', 'Border Radius'),
    padding: createQuickStyleField('spacing', 'padding', 'Padding'),
    globalStyles: createGlobalStyleField('Button', ['typography', 'colors', 'borders', 'spacing', 'effects']),
    customClasses: {
      type: 'text',
      label: 'Custom Classes',
      placeholder: 'hover:scale-105 active:scale-95',
    },
  },
  render: ({ text, variant, size, backgroundColor, textColor, borderRadius, padding, globalStyles, customClasses }) => {
    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200';
    
    const variantClasses = {
      primary: 'bg-blue-600 text-white hover:bg-blue-700',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700',
      outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white',
      ghost: 'text-blue-600 hover:bg-blue-50',
    };

    const allClasses = [
      baseClasses,
      variantClasses[variant] || variantClasses.primary,
      size,
      backgroundColor,
      textColor,
      borderRadius,
      padding,
      globalStyles,
      customClasses,
    ].filter(Boolean).join(' ');

    return (
      <button className={allClasses}>
        {text || 'Button'}
      </button>
    );
  },
};

/**
 * Enhanced ZMP Text with Global Styling
 */
export const EnhancedZMPText: ComponentConfig = {
  fields: {
    content: {
      type: 'textarea',
      label: 'Text Content',
    },
    tag: {
      type: 'select',
      label: 'HTML Tag',
      options: [
        { label: 'Paragraph', value: 'p' },
        { label: 'Heading 1', value: 'h1' },
        { label: 'Heading 2', value: 'h2' },
        { label: 'Heading 3', value: 'h3' },
        { label: 'Heading 4', value: 'h4' },
        { label: 'Span', value: 'span' },
        { label: 'Div', value: 'div' },
      ],
    },
    fontSize: createQuickStyleField('typography', 'fontSize', 'Font Size'),
    fontWeight: createQuickStyleField('typography', 'fontWeight', 'Font Weight'),
    textAlign: createQuickStyleField('typography', 'textAlign', 'Text Align'),
    textColor: createQuickStyleField('colors', 'textColor', 'Text Color'),
    margin: createQuickStyleField('spacing', 'margin', 'Margin'),
    globalStyles: createGlobalStyleField('Text', ['typography', 'colors', 'spacing']),
    customClasses: {
      type: 'text',
      label: 'Custom Classes',
      placeholder: 'hover:text-blue-600 transition-colors',
    },
  },
  render: ({ content, tag, fontSize, fontWeight, textAlign, textColor, margin, globalStyles, customClasses }) => {
    const Tag = tag || 'p';
    
    const allClasses = [
      fontSize,
      fontWeight,
      textAlign,
      textColor,
      margin,
      globalStyles,
      customClasses,
    ].filter(Boolean).join(' ');

    return (
      <Tag className={allClasses}>
        {content || 'Your text here...'}
      </Tag>
    );
  },
};

/**
 * Enhanced ZMP Card with Global Styling
 */
export const EnhancedZMPCard: ComponentConfig = {
  fields: {
    title: {
      type: 'text',
      label: 'Card Title',
    },
    content: {
      type: 'textarea',
      label: 'Card Content',
    },
    imageUrl: {
      type: 'text',
      label: 'Image URL',
    },
    backgroundColor: createQuickStyleField('colors', 'backgroundColor', 'Background'),
    borderRadius: createQuickStyleField('borders', 'borderRadius', 'Border Radius'),
    shadow: createQuickStyleField('effects', 'shadow', 'Shadow'),
    padding: createQuickStyleField('spacing', 'padding', 'Padding'),
    globalStyles: createGlobalStyleField('Card', ['colors', 'borders', 'spacing', 'effects']),
    customClasses: {
      type: 'text',
      label: 'Custom Classes',
      placeholder: 'hover:shadow-lg transition-shadow',
    },
  },
  render: ({ title, content, imageUrl, backgroundColor, borderRadius, shadow, padding, globalStyles, customClasses }) => {
    const allClasses = [
      'overflow-hidden',
      backgroundColor || 'bg-white',
      borderRadius || 'rounded-lg',
      shadow || 'shadow-md',
      padding || 'p-6',
      globalStyles,
      customClasses,
    ].filter(Boolean).join(' ');

    return (
      <div className={allClasses}>
        {imageUrl && (
          <img 
            src={imageUrl} 
            alt={title || 'Card image'} 
            className="w-full h-48 object-cover mb-4 rounded"
          />
        )}
        {title && (
          <h3 className="text-lg font-semibold mb-2">
            {title}
          </h3>
        )}
        {content && (
          <p className="text-gray-600">
            {content}
          </p>
        )}
      </div>
    );
  },
};

/**
 * Enhanced ZMP Grid with Global Styling
 */
export const EnhancedZMPGrid: ComponentConfig = {
  fields: {
    columns: {
      type: 'select',
      label: 'Columns',
      options: [
        { label: '1 Column', value: 'grid-cols-1' },
        { label: '2 Columns', value: 'grid-cols-2' },
        { label: '3 Columns', value: 'grid-cols-3' },
        { label: '4 Columns', value: 'grid-cols-4' },
        { label: '6 Columns', value: 'grid-cols-6' },
        { label: '12 Columns', value: 'grid-cols-12' },
      ],
    },
    gap: createQuickStyleField('layout', 'gap', 'Gap'),
    padding: createQuickStyleField('spacing', 'padding', 'Padding'),
    globalStyles: createGlobalStyleField('Grid', ['layout', 'spacing']),
    customClasses: {
      type: 'text',
      label: 'Custom Classes',
      placeholder: 'lg:grid-cols-4 md:grid-cols-2',
    },
  },
  render: ({ columns, gap, padding, globalStyles, customClasses }) => {
    const allClasses = [
      'grid',
      columns || 'grid-cols-1',
      gap || 'gap-4',
      padding,
      globalStyles,
      customClasses,
    ].filter(Boolean).join(' ');

    return (
      <div className={allClasses}>
        <div className="bg-gray-100 p-4 rounded text-center">Grid Item 1</div>
        <div className="bg-gray-100 p-4 rounded text-center">Grid Item 2</div>
        <div className="bg-gray-100 p-4 rounded text-center">Grid Item 3</div>
      </div>
    );
  },
};

/**
 * Enhanced ZMP Container with Global Styling
 */
export const EnhancedZMPContainer: ComponentConfig = {
  fields: {
    maxWidth: {
      type: 'select',
      label: 'Max Width',
      options: [
        { label: 'None', value: 'max-w-none' },
        { label: 'SM', value: 'max-w-sm' },
        { label: 'MD', value: 'max-w-md' },
        { label: 'LG', value: 'max-w-lg' },
        { label: 'XL', value: 'max-w-xl' },
        { label: '2XL', value: 'max-w-2xl' },
        { label: '4XL', value: 'max-w-4xl' },
        { label: '6XL', value: 'max-w-6xl' },
        { label: 'Full', value: 'max-w-full' },
      ],
    },
    backgroundColor: createQuickStyleField('colors', 'backgroundColor', 'Background'),
    padding: createQuickStyleField('spacing', 'padding', 'Padding'),
    margin: createQuickStyleField('spacing', 'margin', 'Margin'),
    globalStyles: createGlobalStyleField('Container', ['layout', 'colors', 'spacing']),
    customClasses: {
      type: 'text',
      label: 'Custom Classes',
      placeholder: 'mx-auto min-h-screen',
    },
  },
  render: ({ maxWidth, backgroundColor, padding, margin, globalStyles, customClasses }) => {
    const allClasses = [
      maxWidth || 'max-w-4xl',
      backgroundColor,
      padding || 'p-4',
      margin || 'mx-auto',
      globalStyles,
      customClasses,
    ].filter(Boolean).join(' ');

    return (
      <div className={allClasses}>
        <div className="border-2 border-dashed border-gray-300 p-8 text-center text-gray-500">
          Container Content Area
          <br />
          <small>Drop components here</small>
        </div>
      </div>
    );
  },
};
