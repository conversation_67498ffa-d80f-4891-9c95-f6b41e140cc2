/**
 * Global Styles Sidebar Component
 * Integrates with <PERSON><PERSON>'s SidebarSection for component-specific styling
 */

import React, { useState, useCallback } from 'react';
import { SidebarSection } from '@measured/puck';
import { GlobalStylePanel, STYLE_CATEGORIES, StyleCategory } from './global-style-system';
import { Palette, ChevronDown, ChevronRight } from 'lucide-react';

interface GlobalStylesSidebarProps {
  value: string;
  onChange: (value: string) => void;
  componentType?: string;
  label?: string;
  categories?: StyleCategory[];
}

export const GlobalStylesSidebar: React.FC<GlobalStylesSidebarProps> = ({
  value = '',
  onChange,
  componentType = 'All',
  label = 'Global Styles',
  categories,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeCategory, setActiveCategory] = useState<StyleCategory>('layout');

  // Get available categories for this component type
  const availableCategories = categories || (Object.keys(STYLE_CATEGORIES).filter(categoryKey => {
    const category = STYLE_CATEGORIES[categoryKey as StyleCategory];
    return category.components.includes('All') || 
           category.components.includes(componentType);
  }) as StyleCategory[]);

  const appliedClasses = value.split(' ').filter(Boolean);

  return (
    <SidebarSection title={label} noPadding>
      <div className="p-4">
        {/* Header with expand/collapse */}
        <div 
          className="flex items-center justify-between cursor-pointer mb-3 p-2 rounded hover:bg-gray-50"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center gap-2">
            <Palette size={16} className="text-blue-600" />
            <span className="font-medium text-sm">Style Categories</span>
            {appliedClasses.length > 0 && (
              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                {appliedClasses.length}
              </span>
            )}
          </div>
          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
        </div>

        {/* Applied Classes Preview */}
        {appliedClasses.length > 0 && (
          <div className="mb-3 p-2 bg-gray-50 rounded text-xs">
            <div className="font-medium mb-1">Applied Classes:</div>
            <div className="flex flex-wrap gap-1">
              {appliedClasses.slice(0, 5).map((cls, index) => (
                <span key={index} className="bg-blue-100 text-blue-700 px-1 py-0.5 rounded">
                  {cls}
                </span>
              ))}
              {appliedClasses.length > 5 && (
                <span className="text-gray-500">+{appliedClasses.length - 5} more</span>
              )}
            </div>
          </div>
        )}

        {/* Expanded Style Interface */}
        {isExpanded && (
          <div className="space-y-3">
            {/* Category Tabs */}
            <div className="flex flex-wrap gap-1">
              {availableCategories.map((categoryKey) => {
                const category = STYLE_CATEGORIES[categoryKey];
                const isActive = activeCategory === categoryKey;
                
                return (
                  <button
                    key={categoryKey}
                    onClick={() => setActiveCategory(categoryKey)}
                    className={`px-2 py-1 text-xs rounded transition-colors ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category.icon} {category.title}
                  </button>
                );
              })}
            </div>

            {/* Style Options for Active Category */}
            <div className="space-y-2">
              {Object.entries(STYLE_CATEGORIES[activeCategory].styles).map(([styleKey, styleConfig]) => (
                <div key={styleKey}>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    {styleConfig.label}
                  </label>
                  <div className="grid grid-cols-2 gap-1">
                    {styleConfig.options.map((option) => {
                      const isApplied = appliedClasses.includes(option.value);
                      return (
                        <button
                          key={option.value}
                          onClick={() => {
                            const newClasses = isApplied
                              ? appliedClasses.filter(cls => cls !== option.value)
                              : [...appliedClasses, option.value];
                            onChange(newClasses.join(' '));
                          }}
                          className={`px-2 py-1 text-xs rounded border transition-colors ${
                            isApplied
                              ? 'bg-blue-500 text-white border-blue-500'
                              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {option.label}
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="pt-2 border-t border-gray-200">
              <div className="flex gap-2">
                <button
                  onClick={() => onChange('')}
                  className="flex-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors"
                >
                  Clear All
                </button>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(value);
                    // Could add toast notification here
                  }}
                  className="flex-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors"
                >
                  Copy Classes
                </button>
              </div>
            </div>

            {/* Custom Classes Input */}
            <div className="pt-2 border-t border-gray-200">
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Custom Classes
              </label>
              <input
                type="text"
                placeholder="Enter custom classes..."
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    const customClass = e.currentTarget.value.trim();
                    if (customClass) {
                      const newClasses = [...appliedClasses, customClass];
                      onChange(newClasses.join(' '));
                      e.currentTarget.value = '';
                    }
                  }
                }}
              />
              <div className="text-xs text-gray-500 mt-1">
                Press Enter to add
              </div>
            </div>
          </div>
        )}

        {/* Compact Mode when collapsed */}
        {!isExpanded && (
          <div className="space-y-2">
            {/* Quick Style Buttons */}
            <div className="grid grid-cols-3 gap-1">
              <button
                onClick={() => {
                  const newClasses = appliedClasses.includes('p-4') 
                    ? appliedClasses.filter(cls => cls !== 'p-4')
                    : [...appliedClasses, 'p-4'];
                  onChange(newClasses.join(' '));
                }}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  appliedClasses.includes('p-4')
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Padding
              </button>
              <button
                onClick={() => {
                  const newClasses = appliedClasses.includes('rounded-lg') 
                    ? appliedClasses.filter(cls => cls !== 'rounded-lg')
                    : [...appliedClasses, 'rounded-lg'];
                  onChange(newClasses.join(' '));
                }}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  appliedClasses.includes('rounded-lg')
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Rounded
              </button>
              <button
                onClick={() => {
                  const newClasses = appliedClasses.includes('shadow-lg') 
                    ? appliedClasses.filter(cls => cls !== 'shadow-lg')
                    : [...appliedClasses, 'shadow-lg'];
                  onChange(newClasses.join(' '));
                }}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  appliedClasses.includes('shadow-lg')
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Shadow
              </button>
            </div>

            {/* Expand Button */}
            <button
              onClick={() => setIsExpanded(true)}
              className="w-full px-2 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 rounded transition-colors"
            >
              More Styling Options
            </button>
          </div>
        )}
      </div>
    </SidebarSection>
  );
};

export default GlobalStylesSidebar;
