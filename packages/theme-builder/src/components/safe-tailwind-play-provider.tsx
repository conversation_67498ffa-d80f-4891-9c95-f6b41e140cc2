/**
 * Safe TailwindPlay Provider
 * Alternative implementation with better error handling and script management
 */

import React, { useEffect, useRef, useCallback, useState } from 'react';
import { TailwindPlayErrorBoundary } from './tailwind-play-error-boundary';

export interface SafeTailwindPlayProviderProps {
  children: React.ReactNode;
  theme?: {
    colors?: Record<string, any>;
    fontFamily?: Record<string, string | string[]>;
    spacing?: Record<string, string>;
    borderRadius?: Record<string, string>;
  };
  enabled?: boolean;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

const TAILWIND_PLAY_CDN = 'https://cdn.tailwindcss.com/3.4.0';

export const SafeTailwindPlayProvider: React.FC<SafeTailwindPlayProviderProps> = ({
  children,
  theme,
  enabled = true,
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const scriptRef = useRef<HTMLScriptElement | null>(null);
  const isMountedRef = useRef(true);
  const loadAttemptRef = useRef(0);
  const maxRetries = 3;

  // Safe script removal
  const safeRemoveScript = useCallback(() => {
    if (scriptRef.current) {
      try {
        // Check if script is still in DOM
        if (document.contains(scriptRef.current)) {
          scriptRef.current.remove();
          console.log('TailwindCSS script removed safely');
        }
      } catch (err) {
        console.warn('Failed to remove TailwindCSS script:', err);
      } finally {
        scriptRef.current = null;
      }
    }
  }, []);

  // Load TailwindCSS with retry logic
  const loadTailwindCSS = useCallback(async () => {
    if (!enabled || !isMountedRef.current) return;

    loadAttemptRef.current++;
    
    try {
      // Remove existing script
      safeRemoveScript();

      // Check if TailwindCSS is already loaded globally
      if (typeof window !== 'undefined' && (window as any).tailwind) {
        console.log('TailwindCSS already loaded globally');
        setIsLoaded(true);
        setError(null);
        onLoad?.();
        return;
      }

      // Create new script element
      const script = document.createElement('script');
      script.src = TAILWIND_PLAY_CDN;
      script.async = true;
      script.crossOrigin = 'anonymous';

      // Set up event handlers
      const handleLoad = () => {
        if (!isMountedRef.current) return;
        
        console.log('TailwindCSS loaded successfully');
        setIsLoaded(true);
        setError(null);
        loadAttemptRef.current = 0;
        onLoad?.();

        // Apply custom theme if provided
        if (theme && typeof window !== 'undefined' && (window as any).tailwind) {
          try {
            (window as any).tailwind.config = {
              theme: {
                extend: theme,
              },
            };
            console.log('Custom theme applied to TailwindCSS');
          } catch (themeError) {
            console.warn('Failed to apply custom theme:', themeError);
          }
        }
      };

      const handleError = (event: ErrorEvent | Event) => {
        if (!isMountedRef.current) return;

        const error = new Error(`Failed to load TailwindCSS from ${TAILWIND_PLAY_CDN}`);
        console.error('TailwindCSS load error:', error);
        
        setError(error);
        onError?.(error);

        // Retry if we haven't exceeded max attempts
        if (loadAttemptRef.current < maxRetries) {
          console.log(`Retrying TailwindCSS load (attempt ${loadAttemptRef.current + 1}/${maxRetries})`);
          setTimeout(() => {
            if (isMountedRef.current) {
              loadTailwindCSS();
            }
          }, 1000 * loadAttemptRef.current); // Exponential backoff
        } else {
          console.error(`Failed to load TailwindCSS after ${maxRetries} attempts`);
        }
      };

      script.addEventListener('load', handleLoad);
      script.addEventListener('error', handleError);

      // Store reference and append to head
      scriptRef.current = script;
      document.head.appendChild(script);

      console.log(`Loading TailwindCSS (attempt ${loadAttemptRef.current}/${maxRetries})`);

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error loading TailwindCSS');
      console.error('TailwindCSS setup error:', error);
      setError(error);
      onError?.(error);
    }
  }, [enabled, theme, onLoad, onError, safeRemoveScript]);

  // Load TailwindCSS on mount and when enabled changes
  useEffect(() => {
    if (enabled) {
      loadTailwindCSS();
    } else {
      safeRemoveScript();
      setIsLoaded(false);
      setError(null);
    }
  }, [enabled, loadTailwindCSS, safeRemoveScript]);

  // Update theme when it changes
  useEffect(() => {
    if (!enabled || !isLoaded || !theme) return;

    try {
      if (typeof window !== 'undefined' && (window as any).tailwind) {
        (window as any).tailwind.config = {
          theme: {
            extend: theme,
          },
        };
        console.log('TailwindCSS theme updated');
      }
    } catch (err) {
      console.warn('Failed to update TailwindCSS theme:', err);
    }
  }, [theme, enabled, isLoaded]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      safeRemoveScript();
    };
  }, [safeRemoveScript]);

  // Provide context value
  const contextValue = {
    isLoaded,
    error,
    retry: loadTailwindCSS,
  };

  return (
    <TailwindPlayErrorBoundary
      fallback={
        <div className="tailwind-play-fallback">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 m-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  TailwindCSS Loading
                </h3>
                <div className="mt-1 text-sm text-blue-700">
                  <p>
                    {error 
                      ? `Failed to load TailwindCSS: ${error.message}`
                      : 'Loading TailwindCSS for real-time preview...'
                    }
                  </p>
                </div>
                {error && (
                  <div className="mt-2">
                    <button
                      onClick={loadTailwindCSS}
                      className="bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded text-sm font-medium transition-colors"
                    >
                      Retry
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
          {children}
        </div>
      }
    >
      <SafeTailwindPlayContext.Provider value={contextValue}>
        {children}
      </SafeTailwindPlayContext.Provider>
    </TailwindPlayErrorBoundary>
  );
};

// Context for safe provider
const SafeTailwindPlayContext = React.createContext<{
  isLoaded: boolean;
  error: Error | null;
  retry: () => void;
}>({
  isLoaded: false,
  error: null,
  retry: () => {},
});

// Hook to use safe TailwindPlay context
export const useSafeTailwindPlay = () => {
  const context = React.useContext(SafeTailwindPlayContext);
  if (!context) {
    throw new Error('useSafeTailwindPlay must be used within SafeTailwindPlayProvider');
  }
  return context;
};

export default SafeTailwindPlayProvider;
