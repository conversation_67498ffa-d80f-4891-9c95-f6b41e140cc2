/**
 * Theme API Service
 * Handles communication with theme-builder APIs
 * Implements debounced auto-save and CSS loading
 */

import { debounce } from '../utils/debounce';

export interface SaveConfigParams {
  config: any;
  accountId: string;
  themeId?: string;
  editThemeId?: string;
  tempThemeId?: string;
}

export interface SaveConfigResult {
  configId: string;
  cssUrl: string;
  hash: string;
  size: number;
}

export interface LoadConfigParams {
  configId: string;
  accountId?: string;
}

export interface LoadConfigResult {
  config: any;
  cssText: string;
  hash: string;
  size: number;
  cached: boolean;
}

export class ThemeAPIService {
  private baseUrl: string;
  private cssLinkElement: HTMLLinkElement | null = null;
  private currentCSSUrl: string | null = null;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  /**
   * Save theme config and generate CSS
   */
  async saveConfig(params: SaveConfigParams): Promise<SaveConfigResult> {
    const response = await fetch(`${this.baseUrl}/api/theme-builder/save-config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to save config');
    }

    return data.data;
  }

  /**
   * Load theme config and CSS
   */
  async loadConfig(params: LoadConfigParams): Promise<LoadConfigResult> {
    const url = new URL(`${this.baseUrl}/api/theme-builder/get-config`);
    url.searchParams.set('configId', params.configId);
    if (params.accountId) {
      url.searchParams.set('accountId', params.accountId);
    }

    const response = await fetch(url.toString());

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to load config');
    }

    return data.data;
  }

  /**
   * Apply CSS to the document
   */
  applyCSSToDocument(cssText: string, configId?: string): void {
    // Remove existing CSS link if any
    if (this.cssLinkElement) {
      document.head.removeChild(this.cssLinkElement);
      this.cssLinkElement = null;
    }

    // Create style element for inline CSS
    const styleElement = document.createElement('style');
    styleElement.id = `theme-builder-css-${configId || 'inline'}`;
    styleElement.textContent = cssText;
    
    // Add to document head
    document.head.appendChild(styleElement);

    console.log(`Applied CSS (${Buffer.byteLength(cssText, 'utf8')} bytes) to document`);
  }

  /**
   * Apply CSS URL to the document
   */
  applyCSSUrl(cssUrl: string): void {
    // Don't reload if same URL
    if (this.currentCSSUrl === cssUrl) {
      return;
    }

    // Remove existing CSS link if any
    if (this.cssLinkElement) {
      document.head.removeChild(this.cssLinkElement);
    }

    // Create new link element
    this.cssLinkElement = document.createElement('link');
    this.cssLinkElement.rel = 'stylesheet';
    this.cssLinkElement.href = cssUrl;
    this.cssLinkElement.id = 'theme-builder-css-link';

    // Add to document head
    document.head.appendChild(this.cssLinkElement);
    this.currentCSSUrl = cssUrl;

    console.log(`Applied CSS URL: ${cssUrl}`);
  }

  /**
   * Remove applied CSS from document
   */
  removeCSSFromDocument(): void {
    if (this.cssLinkElement) {
      document.head.removeChild(this.cssLinkElement);
      this.cssLinkElement = null;
      this.currentCSSUrl = null;
    }

    // Also remove any inline style elements
    const styleElements = document.querySelectorAll('style[id^="theme-builder-css-"]');
    styleElements.forEach(element => element.remove());

    console.log('Removed theme CSS from document');
  }

  /**
   * Create debounced save function
   */
  createDebouncedSave(
    params: Omit<SaveConfigParams, 'config'>,
    delay: number = 1000
  ): (config: any) => Promise<void> {
    return debounce(async (config: any) => {
      try {
        const result = await this.saveConfig({ ...params, config });
        console.log('Auto-save successful:', result);
        
        // Apply CSS URL after successful save
        this.applyCSSUrl(result.cssUrl);
      } catch (error) {
        console.error('Auto-save failed:', error);
        throw error;
      }
    }, delay);
  }

  /**
   * Preload CSS for faster switching
   */
  preloadCSS(cssUrl: string): void {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = cssUrl;
    document.head.appendChild(link);
  }

  /**
   * Get current CSS URL
   */
  getCurrentCSSUrl(): string | null {
    return this.currentCSSUrl;
  }

  /**
   * Check if CSS is currently applied
   */
  isCSSApplied(): boolean {
    return this.cssLinkElement !== null || this.currentCSSUrl !== null;
  }
}

/**
 * Hook for using Theme API Service in React components
 */
export const useThemeAPIService = (baseUrl?: string) => {
  // This will be implemented in the hooks file
  // For now, return a new instance
  return new ThemeAPIService(baseUrl);
};

// Export singleton instance
export const themeAPIService = new ThemeAPIService();
