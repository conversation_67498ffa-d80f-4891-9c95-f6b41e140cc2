# Global Styles Tab

Enhanced ThemeBuilder with Elementor-like global styling tab for visual TailwindCSS class application.

## 🎯 Overview

The Global Styles Tab provides a dedicated interface within the ThemeBuilder for applying TailwindCSS classes to components visually, similar to Elementor's styling panel but integrated as a tab alongside Components, Fields, and Outline.

### Features

- **Dedicated Tab**: Global Styles tab integrated with existing Puck Editor tabs
- **Component Selection**: Automatically shows styling options for the selected component
- **Category-based Interface**: Organized styling options by Layout, Typography, Colors, etc.
- **Real-time Preview**: Instant visual feedback with TailwindCSS integration
- **Component-Specific Options**: Different styling categories based on component type
- **Quick Actions**: Clear styles, copy classes, and other utilities

## 🏗️ Architecture

```
Enhanced ThemeBuilder
├── Custom Header (with actions)
├── Preview Area
└── Bottom Tabs
    ├── Components Tab
    ├── Fields Tab
    ├── Global Styles Tab ← NEW
    └── Outline Tab
```

## 🚀 Quick Start

### 1. Basic Usage

```tsx
import { EnhancedThemeBuilder, ENHANCED_ZMP_PUCK_CONFIG } from '@kit/theme-builder';

<EnhancedThemeBuilder
  config={ENHANCED_ZMP_PUCK_CONFIG}
  data={puckData}
  onChange={handleChange}
  onPublish={handlePublish}
  enableTailwindPlay={true}
/>
```

### 2. With Custom Header Actions

```tsx
<EnhancedThemeBuilder
  config={ENHANCED_ZMP_PUCK_CONFIG}
  data={puckData}
  onChange={handleChange}
  onPublish={handlePublish}
  enableTailwindPlay={true}
  headerActions={[
    {
      type: 'button',
      label: 'Save Draft',
      icon: '💾',
      variant: 'secondary',
      onClick: () => saveDraft(),
    },
    {
      type: 'button',
      label: 'Export',
      icon: '📤',
      variant: 'secondary',
      onClick: () => exportTheme(),
    },
  ]}
/>
```

### 3. With Custom Theme

```tsx
<EnhancedThemeBuilder
  config={ENHANCED_ZMP_PUCK_CONFIG}
  data={puckData}
  onChange={handleChange}
  onPublish={handlePublish}
  enableTailwindPlay={true}
  theme={{
    colors: {
      primary: { main: '#3b82f6' },
      secondary: { main: '#64748b' },
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
    },
  }}
/>
```

## 📦 Components

### EnhancedThemeBuilder

Main component with Global Styles tab integration.

**Props:**
- `config`: Puck configuration object
- `data`: Current theme data
- `onChange`: Callback when data changes
- `onPublish`: Callback when publish is triggered
- `enableTailwindPlay`: Enable TailwindCSS Play CDN (default: true)
- `theme`: Custom theme configuration for TailwindCSS
- `headerActions`: Array of custom header actions
- `viewports`: Array of viewport configurations

### GlobalStylesTab

The Global Styles tab component.

**Features:**
- Component selection indicator
- Search functionality for style categories
- Category-based styling interface
- Quick actions (clear styles, copy classes)
- Real-time style application

## 🎨 Global Styles Interface

### Component Selection

When a component is selected in the preview:
- Shows component name and type
- Displays current applied styles count
- Enables styling interface

When no component is selected:
- Shows selection prompt
- Disables styling interface
- Provides helpful guidance

### Style Categories

The interface organizes styles into categories:

- **📐 Layout**: Display, Flex, Grid, Gap
- **📏 Spacing**: Padding, Margin
- **🔤 Typography**: Font Size, Weight, Align
- **🎨 Colors**: Background, Text colors
- **🔲 Borders**: Width, Radius, Colors
- **✨ Effects**: Shadow, Opacity, Transform
- **📱 Responsive**: Breakpoint controls

### Search & Filter

- Search across all style categories
- Filter categories by name
- Quick category switching

### Quick Actions

- **Clear All Styles**: Remove all applied global styles
- **Copy Classes**: Copy current classes to clipboard
- **Component Info**: Show selected component details

## 🔧 Integration with Existing Components

### Enhanced Components

Components that support global styling need these props:

```tsx
const MyComponent = {
  fields: {
    // ... other fields
    globalStyles: {
      type: 'text',
      label: 'Global Styles',
    },
  },
  render: ({ globalStyles, ...props }) => {
    return (
      <div className={globalStyles}>
        {/* component content */}
      </div>
    );
  },
};
```

### Automatic Integration

The Global Styles tab automatically:
- Detects selected component
- Applies styles to `globalStyles` prop
- Updates component in real-time
- Maintains style persistence

## 🎯 User Experience

### Workflow

1. **Select Component**: Click on any component in the preview
2. **Open Global Styles**: Click on the "Global Styles" tab
3. **Choose Category**: Select styling category (Layout, Colors, etc.)
4. **Apply Styles**: Click on style options to apply
5. **See Results**: View changes instantly in preview
6. **Fine-tune**: Use search and quick actions as needed

### Visual Feedback

- **Selected Component**: Highlighted with border and info panel
- **Applied Styles**: Visual indicators for active styles
- **Real-time Preview**: Instant updates in preview area
- **Style Count**: Shows number of applied classes

### Responsive Design

- **Mobile-friendly**: Touch-optimized interface
- **Tablet Support**: Optimized for tablet usage
- **Desktop**: Full feature set with hover states

## 📱 Mobile Experience

The Global Styles tab is optimized for mobile usage:

- **Touch Targets**: Large, touch-friendly buttons
- **Scrollable Interface**: Smooth scrolling through categories
- **Collapsible Tabs**: Space-efficient tab system
- **Gesture Support**: Swipe and tap interactions

## 🔍 Advanced Features

### Component-Specific Styling

Different components show different styling options:

```tsx
// Button component shows:
- Colors (background, text)
- Spacing (padding)
- Typography (font size, weight)
- Effects (shadow, hover states)

// Text component shows:
- Typography (all options)
- Colors (text color)
- Spacing (margin)

// Container component shows:
- Layout (display, flex, grid)
- Spacing (padding, margin)
- Colors (background)
```

### Style Inheritance

- **Global Styles**: Applied via `globalStyles` prop
- **Component Styles**: Merged with existing className
- **Custom Classes**: Additional `customClasses` prop
- **Priority Order**: customClasses > globalStyles > default classes

### Performance Optimization

- **Debounced Updates**: Prevents excessive re-renders
- **Selective Re-rendering**: Only updates changed components
- **Efficient Diffing**: Smart comparison of style changes
- **Memory Management**: Cleanup of unused styles

## 🧪 Testing

### Manual Testing

1. **Component Selection**: Test selecting different components
2. **Style Application**: Apply various styles and verify results
3. **Category Switching**: Test all style categories
4. **Search Functionality**: Search for specific styles
5. **Quick Actions**: Test clear and copy functions
6. **Mobile Usage**: Test on mobile devices

### Automated Testing

```tsx
import { render, fireEvent } from '@testing-library/react';
import { EnhancedThemeBuilder } from '@kit/theme-builder';

test('Global Styles tab applies styles correctly', () => {
  const { getByText, getByRole } = render(
    <EnhancedThemeBuilder
      config={testConfig}
      data={testData}
      onChange={mockOnChange}
      onPublish={mockOnPublish}
    />
  );

  // Select component
  fireEvent.click(getByText('Test Component'));
  
  // Open Global Styles tab
  fireEvent.click(getByText('Global Styles'));
  
  // Apply style
  fireEvent.click(getByText('Blue Background'));
  
  // Verify style applied
  expect(mockOnChange).toHaveBeenCalledWith(
    expect.objectContaining({
      content: expect.arrayContaining([
        expect.objectContaining({
          props: expect.objectContaining({
            globalStyles: expect.stringContaining('bg-blue-500')
          })
        })
      ])
    })
  );
});
```

## 🚀 Best Practices

1. **Component Design**: Ensure components support `globalStyles` prop
2. **Style Organization**: Use consistent style categories
3. **Performance**: Implement debouncing for style updates
4. **User Guidance**: Provide clear selection indicators
5. **Mobile Optimization**: Test on various screen sizes
6. **Accessibility**: Ensure keyboard navigation works
7. **Error Handling**: Handle edge cases gracefully

## 🔧 Customization

### Custom Style Categories

```tsx
const customCategories = {
  animations: {
    title: 'Animations',
    icon: '🎬',
    styles: {
      transition: {
        label: 'Transition',
        options: [
          { label: 'None', value: '' },
          { label: 'All', value: 'transition-all' },
          { label: 'Colors', value: 'transition-colors' },
        ],
      },
    },
  },
};
```

### Custom Header Actions

```tsx
const customActions = [
  {
    type: 'button',
    label: 'Custom Action',
    icon: '⚡',
    variant: 'primary',
    onClick: () => customFunction(),
  },
];
```

This Global Styles tab provides a powerful, intuitive interface for applying TailwindCSS classes visually, making theme building accessible to users of all skill levels while maintaining the flexibility and power of the underlying CSS framework.
