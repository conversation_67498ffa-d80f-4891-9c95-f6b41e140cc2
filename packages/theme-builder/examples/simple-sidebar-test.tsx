/**
 * Simple Sidebar Test
 * Minimal example to test Global Styles in sidebar
 */

import React, { useState } from 'react';
import { ThemeBuilder, TailwindPlayProvider } from '@kit/theme-builder';
import { createGlobalStyleField } from '../src/components/global-style-system';

// Simple test component
const TestComponent = {
  fields: {
    text: {
      type: 'text' as const,
      label: 'Text',
    },
    // This will show Global Styles in sidebar when component is selected
    globalStyles: createGlobalStyleField('TestComponent'),
  },
  render: ({ text, globalStyles }: any) => (
    <div className={`p-4 border rounded ${globalStyles}`}>
      {text || 'Test Component - Click me to see Global Styles in sidebar!'}
    </div>
  ),
};

const SIMPLE_CONFIG = {
  components: {
    TestComponent,
  },
};

const SIMPLE_DATA = {
  content: [
    {
      type: 'TestComponent',
      props: {
        text: 'Click me to see Global Styles in the sidebar →',
        globalStyles: 'bg-blue-50 border-blue-200 text-blue-800',
      },
    },
  ],
  root: {
    props: {
      title: 'Simple Sidebar Test',
    },
  },
};

export const SimpleSidebarTest: React.FC = () => {
  const [data, setData] = useState(SIMPLE_DATA);

  return (
    <div className="h-screen">
      <TailwindPlayProvider enabled={true}>
        <ThemeBuilder
          config={SIMPLE_CONFIG}
          data={data}
          onChange={setData}
          onPublish={(data) => console.log('Published:', data)}
        />
      </TailwindPlayProvider>
    </div>
  );
};

export default SimpleSidebarTest;
