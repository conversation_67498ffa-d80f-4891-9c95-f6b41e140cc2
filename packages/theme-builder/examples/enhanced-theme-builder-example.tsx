/**
 * Enhanced Theme Builder Example
 * Shows how to use the new Global Styles tab functionality
 */

import React, { useState, useCallback } from 'react';
import { EnhancedThemeBuilder } from '../src/editor/EnhancedThemeBuilder';
import { ENHANCED_ZMP_PUCK_CONFIG } from '../src/components/enhanced-zmp-config';

// Example data with components that support global styling
const EXAMPLE_DATA = {
  content: [
    {
      type: 'Container',
      props: {
        id: 'container-1',
        maxWidth: 'max-w-6xl',
        globalStyles: 'mx-auto py-12 px-4',
        customClasses: 'bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen',
      },
      content: [
        {
          type: 'Text',
          props: {
            id: 'heading-1',
            content: 'Welcome to Enhanced Theme Builder',
            tag: 'h1',
            fontSize: 'text-4xl',
            fontWeight: 'font-bold',
            textAlign: 'text-center',
            globalStyles: 'mb-8 text-gray-900',
            customClasses: 'hover:text-blue-600 transition-colors duration-300',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'grid-1',
            columns: 'grid-cols-1',
            gap: 'gap-8',
            globalStyles: 'md:grid-cols-2 lg:grid-cols-3 mb-12',
          },
          content: [
            {
              type: 'Card',
              props: {
                id: 'card-1',
                title: 'Global Styling',
                content: 'Use the Global Styles tab to apply TailwindCSS classes visually.',
                backgroundColor: 'bg-white',
                borderRadius: 'rounded-xl',
                shadow: 'shadow-lg',
                padding: 'p-6',
                globalStyles: '',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
            {
              type: 'Card',
              props: {
                id: 'card-2',
                title: 'Real-time Preview',
                content: 'See your changes instantly with TailwindCSS Play CDN integration.',
                backgroundColor: 'bg-white',
                borderRadius: 'rounded-xl',
                shadow: 'shadow-lg',
                padding: 'p-6',
                globalStyles: '',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
            {
              type: 'Card',
              props: {
                id: 'card-3',
                title: 'Component-Specific',
                content: 'Different styling options based on the selected component type.',
                backgroundColor: 'bg-white',
                borderRadius: 'rounded-xl',
                shadow: 'shadow-lg',
                padding: 'p-6',
                globalStyles: '',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
          ],
        },
        {
          type: 'Button',
          props: {
            id: 'button-1',
            text: 'Try Global Styling',
            variant: 'primary',
            fontSize: 'text-lg',
            backgroundColor: 'bg-blue-600',
            textColor: 'text-white',
            borderRadius: 'rounded-lg',
            padding: 'px-8 py-4',
            globalStyles: 'font-semibold mx-auto block',
            customClasses: 'hover:bg-blue-700 hover:scale-105 active:scale-95 transition-all',
          },
        },
      ],
    },
  ],
  root: {
    props: {
      title: 'Enhanced Theme Builder Demo',
      description: 'Global styling system with TailwindCSS integration',
      backgroundColor: 'bg-gray-50',
      globalStyles: '',
      customClasses: '',
    },
  },
};

// Theme configuration for TailwindPlay
const THEME_CONFIG = {
  colors: {
    primary: {
      main: '#3b82f6',
      light: '#60a5fa',
      dark: '#1d4ed8',
    },
    secondary: {
      main: '#64748b',
      light: '#94a3b8',
      dark: '#334155',
    },
    accent: {
      main: '#f59e0b',
      light: '#fbbf24',
      dark: '#d97706',
    },
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    headings: {
      fontFamily: 'Inter, system-ui, sans-serif',
      fontWeight: '700',
    },
    body: {
      fontFamily: 'Inter, system-ui, sans-serif',
      fontWeight: '400',
    },
  },
};

/**
 * Basic Enhanced Theme Builder Example
 */
export const BasicEnhancedExample: React.FC = () => {
  const [data, setData] = useState(EXAMPLE_DATA);

  const handleChange = useCallback((newData: any) => {
    setData(newData);
    console.log('Theme data changed:', newData);
  }, []);

  const handlePublish = useCallback((publishData: any) => {
    console.log('Publishing theme:', publishData);
    alert('Theme published! Check console for data.');
  }, []);

  return (
    <div className="h-screen">
      <EnhancedThemeBuilder
        config={ENHANCED_ZMP_PUCK_CONFIG}
        data={data}
        onChange={handleChange}
        onPublish={handlePublish}
        enableTailwindPlay={true}
        theme={THEME_CONFIG}
        headerActions={[
          {
            type: 'button',
            label: 'Save Draft',
            icon: '💾',
            variant: 'secondary',
            onClick: () => {
              localStorage.setItem('theme-draft', JSON.stringify(data));
              alert('Draft saved to localStorage!');
            },
          },
          {
            type: 'button',
            label: 'Load Draft',
            icon: '📂',
            variant: 'secondary',
            onClick: () => {
              const draft = localStorage.getItem('theme-draft');
              if (draft) {
                setData(JSON.parse(draft));
                alert('Draft loaded from localStorage!');
              } else {
                alert('No draft found in localStorage!');
              }
            },
          },
        ]}
      />
    </div>
  );
};

/**
 * Advanced Example with Custom Actions
 */
export const AdvancedEnhancedExample: React.FC = () => {
  const [data, setData] = useState(EXAMPLE_DATA);
  const [showStyleInspector, setShowStyleInspector] = useState(false);

  const extractAllClasses = useCallback((themeData: any): string[] => {
    const classes: string[] = [];
    
    const extract = (obj: any) => {
      if (!obj || typeof obj !== 'object') return;
      
      ['className', 'globalStyles', 'customClasses', 'fontSize', 'fontWeight', 
       'textAlign', 'textColor', 'backgroundColor', 'borderRadius', 'padding', 
       'margin', 'shadow'].forEach(prop => {
        if (typeof obj[prop] === 'string' && obj[prop]) {
          obj[prop].split(' ').forEach((cls: string) => {
            if (cls.trim()) classes.push(cls.trim());
          });
        }
      });
      
      Object.values(obj).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(extract);
        } else if (typeof value === 'object') {
          extract(value);
        }
      });
    };
    
    extract(themeData);
    return [...new Set(classes)]; // Remove duplicates
  }, []);

  const handleChange = useCallback((newData: any) => {
    setData(newData);
  }, []);

  const handlePublish = useCallback((publishData: any) => {
    const allClasses = extractAllClasses(publishData);
    console.log('Publishing theme with classes:', allClasses);
    
    // Simulate API call
    setTimeout(() => {
      alert(`Theme published successfully!\n\nTotal classes: ${allClasses.length}\nUnique classes: ${allClasses.length}`);
    }, 1000);
  }, [extractAllClasses]);

  return (
    <div className="h-screen flex">
      {/* Main Builder */}
      <div className="flex-1">
        <EnhancedThemeBuilder
          config={ENHANCED_ZMP_PUCK_CONFIG}
          data={data}
          onChange={handleChange}
          onPublish={handlePublish}
          enableTailwindPlay={true}
          theme={THEME_CONFIG}
          headerActions={[
            {
              type: 'button',
              label: 'Style Inspector',
              icon: '🔍',
              variant: 'secondary',
              onClick: () => setShowStyleInspector(!showStyleInspector),
            },
            {
              type: 'button',
              label: 'Export Classes',
              icon: '📋',
              variant: 'secondary',
              onClick: () => {
                const classes = extractAllClasses(data);
                navigator.clipboard.writeText(classes.join(' '));
                alert(`Copied ${classes.length} classes to clipboard!`);
              },
            },
            {
              type: 'button',
              label: 'Reset Theme',
              icon: '🔄',
              variant: 'secondary',
              onClick: () => {
                if (confirm('Are you sure you want to reset the theme?')) {
                  setData(EXAMPLE_DATA);
                }
              },
            },
          ]}
        />
      </div>

      {/* Style Inspector Panel */}
      {showStyleInspector && (
        <div className="w-80 bg-white border-l shadow-lg p-4 overflow-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-bold text-lg">Style Inspector</h3>
            <button
              onClick={() => setShowStyleInspector(false)}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ×
            </button>
          </div>
          
          <StyleInspectorPanel data={data} extractAllClasses={extractAllClasses} />
        </div>
      )}
    </div>
  );
};

/**
 * Style Inspector Panel Component
 */
const StyleInspectorPanel: React.FC<{
  data: any;
  extractAllClasses: (data: any) => string[];
}> = ({ data, extractAllClasses }) => {
  const allClasses = extractAllClasses(data);
  const classByCategory = {
    layout: allClasses.filter(cls => /^(flex|grid|block|inline|hidden|relative|absolute|fixed)/.test(cls)),
    spacing: allClasses.filter(cls => /^(p|m|gap|space)-/.test(cls)),
    colors: allClasses.filter(cls => /^(bg|text|border)-/.test(cls)),
    typography: allClasses.filter(cls => /^(text|font|leading|tracking)-/.test(cls)),
    effects: allClasses.filter(cls => /^(shadow|opacity|scale|rotate|transition)/.test(cls)),
    responsive: allClasses.filter(cls => /^(sm|md|lg|xl|2xl):/.test(cls)),
  };

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-3 rounded">
        <div className="text-sm font-medium mb-2">Summary</div>
        <div className="text-xs space-y-1">
          <div>Total Classes: {allClasses.length}</div>
          <div>Unique Classes: {allClasses.length}</div>
        </div>
      </div>

      {Object.entries(classByCategory).map(([category, classes]) => (
        <div key={category} className="border rounded p-3">
          <div className="font-medium text-sm mb-2 capitalize">
            {category} ({classes.length})
          </div>
          <div className="flex flex-wrap gap-1">
            {classes.slice(0, 10).map((cls, index) => (
              <span
                key={index}
                className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
              >
                {cls}
              </span>
            ))}
            {classes.length > 10 && (
              <span className="text-xs text-gray-500">
                +{classes.length - 10} more
              </span>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default {
  BasicEnhancedExample,
  AdvancedEnhancedExample,
};
