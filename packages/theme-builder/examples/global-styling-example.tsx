/**
 * Global Styling Example
 * Shows how to use the Elementor-like global styling system
 */

import React, { useState } from 'react';
import { 
  Theme<PERSON><PERSON>er, 
  TailwindPlayProvider 
} from '@kit/theme-builder';
import { ENHANCED_ZMP_PUCK_CONFIG } from '../src/components/enhanced-zmp-config';

// Example data with global styling
const GLOBAL_STYLING_EXAMPLE_DATA = {
  content: [
    {
      type: 'Container',
      props: {
        maxWidth: 'max-w-6xl',
        globalStyles: 'mx-auto py-12 px-4',
        customClasses: 'bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen',
      },
      content: [
        {
          type: 'Text',
          props: {
            content: 'Welcome to Global Styling',
            tag: 'h1',
            globalStyles: 'text-4xl font-bold text-center mb-8 text-gray-900',
            customClasses: 'hover:text-blue-600 transition-colors duration-300',
          },
        },
        {
          type: 'Grid',
          props: {
            columns: 'grid-cols-1',
            globalStyles: 'md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12',
          },
          content: [
            {
              type: 'Card',
              props: {
                title: 'Easy Styling',
                content: 'Apply styles visually with our Elementor-like interface. No need to remember class names!',
                globalStyles: 'bg-white shadow-lg rounded-xl p-6',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
            {
              type: 'Card',
              props: {
                title: 'TailwindCSS Power',
                content: 'Full TailwindCSS support with real-time preview and intelligent suggestions.',
                globalStyles: 'bg-white shadow-lg rounded-xl p-6',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
            {
              type: 'Card',
              props: {
                title: 'Responsive Design',
                content: 'Built-in responsive controls for mobile, tablet, and desktop breakpoints.',
                globalStyles: 'bg-white shadow-lg rounded-xl p-6',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
          ],
        },
        {
          type: 'Text',
          props: {
            content: 'Try the styling panel on the right to customize any element!',
            tag: 'p',
            globalStyles: 'text-center text-lg text-gray-600 mb-8',
          },
        },
        {
          type: 'Grid',
          props: {
            columns: 'grid-cols-1',
            globalStyles: 'md:grid-cols-2 gap-4 justify-center',
          },
          content: [
            {
              type: 'Button',
              props: {
                text: 'Primary Button',
                variant: 'primary',
                globalStyles: 'px-8 py-4 text-lg font-semibold rounded-lg',
                customClasses: 'hover:scale-105 active:scale-95 transition-transform',
              },
            },
            {
              type: 'Button',
              props: {
                text: 'Secondary Button',
                variant: 'outline',
                globalStyles: 'px-8 py-4 text-lg font-semibold rounded-lg',
                customClasses: 'hover:scale-105 active:scale-95 transition-transform',
              },
            },
          ],
        },
      ],
    },
  ],
  root: {
    props: {
      title: 'Global Styling Demo',
      description: 'Elementor-like styling system for Puck Editor',
      globalStyles: 'bg-gray-50',
    },
  },
};

/**
 * Global Styling Demo Component
 */
export const GlobalStylingDemo: React.FC = () => {
  const [puckData, setPuckData] = useState(GLOBAL_STYLING_EXAMPLE_DATA);

  const handleChange = (data: any) => {
    setPuckData(data);
    console.log('Updated data with global styles:', data);
  };

  const handlePublish = (data: any) => {
    console.log('Publishing with global styles:', data);
    alert('Theme published with global styling!');
  };

  return (
    <div className="h-screen">
      <TailwindPlayProvider enabled={true}>
        <ThemeBuilder
          config={ENHANCED_ZMP_PUCK_CONFIG}
          data={puckData}
          onChange={handleChange}
          onPublish={handlePublish}
          enableTailwindPlay={true}
          headerActions={[
            {
              type: 'button',
              label: 'Style Guide',
              icon: '🎨',
              variant: 'secondary',
              onClick: () => {
                alert('Style Guide:\n\n' +
                  '• Use the Global Styles panel for each component\n' +
                  '• Quick selectors for common properties\n' +
                  '• Custom classes for advanced styling\n' +
                  '• Real-time preview with TailwindCSS\n' +
                  '• Responsive breakpoint controls'
                );
              },
            },
            {
              type: 'button',
              label: 'Export Styles',
              icon: '📋',
              variant: 'secondary',
              onClick: () => {
                const extractedClasses = extractAllClasses(puckData);
                navigator.clipboard.writeText(extractedClasses.join(' '));
                alert(`Copied ${extractedClasses.length} classes to clipboard!`);
              },
            },
          ]}
          viewports={[
            { width: 375, height: 667, label: 'Mobile', icon: '📱' },
            { width: 768, height: 1024, label: 'Tablet', icon: '📱' },
            { width: 1200, height: 800, label: 'Desktop', icon: '💻' },
            { width: 1920, height: 1080, label: 'Large', icon: '🖥️' },
          ]}
        />
      </TailwindPlayProvider>
    </div>
  );
};

/**
 * Style Inspector Component
 * Shows all applied styles in the current design
 */
export const StyleInspector: React.FC<{ data: any }> = ({ data }) => {
  const extractedClasses = extractAllClasses(data);
  const classCount = extractedClasses.length;
  const uniqueClasses = [...new Set(extractedClasses)];

  return (
    <div className="p-4 bg-gray-50 border rounded-lg">
      <h3 className="font-bold mb-3">Style Inspector</h3>
      
      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <span className="font-medium">Total Classes:</span> {classCount}
        </div>
        <div>
          <span className="font-medium">Unique Classes:</span> {uniqueClasses.length}
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium text-sm">Applied Classes:</h4>
        <div className="max-h-40 overflow-auto">
          <div className="flex flex-wrap gap-1">
            {uniqueClasses.map((className, index) => (
              <span
                key={index}
                className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
              >
                {className}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Advanced Global Styling Example
 * Shows complex styling scenarios
 */
export const AdvancedGlobalStylingExample: React.FC = () => {
  const [puckData, setPuckData] = useState(GLOBAL_STYLING_EXAMPLE_DATA);
  const [showInspector, setShowInspector] = useState(false);

  return (
    <div className="h-screen flex">
      {/* Main Editor */}
      <div className="flex-1">
        <TailwindPlayProvider enabled={true}>
          <ThemeBuilder
            config={ENHANCED_ZMP_PUCK_CONFIG}
            data={puckData}
            onChange={setPuckData}
            enableTailwindPlay={true}
          />
        </TailwindPlayProvider>
      </div>

      {/* Style Inspector Panel */}
      {showInspector && (
        <div className="w-80 border-l bg-white p-4 overflow-auto">
          <div className="flex items-center justify-between mb-4">
            <h2 className="font-bold">Style Inspector</h2>
            <button
              onClick={() => setShowInspector(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>
          <StyleInspector data={puckData} />
        </div>
      )}

      {/* Toggle Inspector Button */}
      {!showInspector && (
        <button
          onClick={() => setShowInspector(true)}
          className="fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
        >
          🔍 Inspect Styles
        </button>
      )}
    </div>
  );
};

/**
 * Helper function to extract all classes from Puck data
 */
function extractAllClasses(data: any): string[] {
  const classes: string[] = [];

  const extract = (obj: any) => {
    if (!obj || typeof obj !== 'object') return;

    // Extract from various class properties
    ['className', 'globalStyles', 'customClasses'].forEach(prop => {
      if (typeof obj[prop] === 'string' && obj[prop]) {
        classes.push(...obj[prop].split(' ').filter(Boolean));
      }
    });

    // Recursively extract from nested objects and arrays
    Object.values(obj).forEach(value => {
      if (Array.isArray(value)) {
        value.forEach(extract);
      } else if (typeof value === 'object') {
        extract(value);
      }
    });
  };

  extract(data);
  return classes;
}

export default {
  GlobalStylingDemo,
  StyleInspector,
  AdvancedGlobalStylingExample,
};
