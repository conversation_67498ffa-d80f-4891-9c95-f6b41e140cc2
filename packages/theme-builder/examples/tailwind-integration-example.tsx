/**
 * Example: TailwindCSS Integration with Theme Builder
 * Shows how to use the new TailwindCSS integration features
 */

import React, { useState, useCallback } from 'react';
import { 
  ThemeBuilder, 
  TailwindPlayProvider, 
  useThemeBuilderAPI,
  ThemeRenderer 
} from '@kit/theme-builder';

// Example Puck data with TailwindCSS classes
const EXAMPLE_PUCK_DATA = {
  content: [
    {
      type: 'ZMPGrid',
      props: {
        tailwindColumns: 3,
        tailwindGap: 'md',
        customClasses: 'lg:grid-cols-4 md:gap-6',
        useTailwind: true,
      },
      content: [
        {
          type: 'ZMPCard',
          props: {
            title: 'Dynamic Card',
            className: 'bg-blue-500 text-white p-6 rounded-lg shadow-lg',
            tailwindColor: 'primary',
            customClasses: 'hover:scale-105 transition-transform duration-200',
          },
        },
        {
          type: 'ZMPButton',
          props: {
            text: 'Interactive Button',
            className: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-full',
            customClasses: 'hover:from-purple-600 hover:to-pink-600 active:scale-95 transition-all',
          },
        },
        {
          type: 'ZMPText',
          props: {
            content: 'Arbitrary Values Example',
            className: 'text-[#ff6b35] text-[28px] font-[700] leading-[1.2] mb-[20px]',
            customClasses: 'hover:text-[#e55a2b] transition-colors duration-300',
          },
        },
      ],
    },
  ],
  root: {
    props: {
      title: 'TailwindCSS Demo',
      primaryColor: '#3b82f6',
      secondaryColor: '#64748b',
      accentColor: '#f59e0b',
      enableTailwindStyling: true,
      containerClasses: 'max-w-7xl mx-auto px-6 py-12 bg-gradient-to-br from-blue-50 to-indigo-100',
    },
  },
};

// Theme configuration for Tailwind Play
const THEME_CONFIG = {
  colors: {
    primary: {
      main: '#3b82f6',
      light: '#60a5fa',
      dark: '#1d4ed8',
    },
    secondary: {
      main: '#64748b',
      light: '#94a3b8',
      dark: '#334155',
    },
    accent: {
      main: '#f59e0b',
      light: '#fbbf24',
      dark: '#d97706',
    },
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    headings: {
      fontFamily: 'Inter, system-ui, sans-serif',
      fontWeight: '700',
    },
    body: {
      fontFamily: 'Inter, system-ui, sans-serif',
      fontWeight: '400',
    },
  },
};

/**
 * Example 1: Basic TailwindCSS Integration
 */
export const BasicTailwindExample: React.FC = () => {
  const [puckData, setPuckData] = useState(EXAMPLE_PUCK_DATA);

  const handleChange = useCallback((data: any) => {
    setPuckData(data);
    console.log('Puck data changed:', data);
  }, []);

  const handlePublish = useCallback((data: any) => {
    console.log('Publishing theme:', data);
    // In real app, this would navigate to preview
  }, []);

  return (
    <div className="h-screen">
      <TailwindPlayProvider theme={THEME_CONFIG} enabled={true}>
        <ThemeBuilder
          data={puckData}
          onChange={handleChange}
          onPublish={handlePublish}
          enableTailwindPlay={true}
          theme={THEME_CONFIG}
          headerActions={[
            {
              type: 'button',
              label: 'Preview',
              icon: '👁️',
              variant: 'secondary',
              onClick: () => console.log('Preview clicked'),
            },
          ]}
          viewports={[
            { width: 375, height: 667, label: 'Mobile', icon: '📱' },
            { width: 768, height: 1024, label: 'Tablet', icon: '📱' },
            { width: 1200, height: 800, label: 'Desktop', icon: '💻' },
          ]}
        />
      </TailwindPlayProvider>
    </div>
  );
};

/**
 * Example 2: With API Integration
 */
export const APIIntegratedExample: React.FC = () => {
  const [puckData, setPuckData] = useState(EXAMPLE_PUCK_DATA);
  
  // Use the new API integration hook
  const { 
    debouncedSave, 
    cssUrl, 
    isLoading, 
    error, 
    lastSaved 
  } = useThemeBuilderAPI({
    accountId: 'example-account-123',
    tempThemeId: 'example-temp-456',
    autoSaveDelay: 1000,
    enableTailwindCSS: true,
  });

  const handleChange = useCallback((data: any) => {
    setPuckData(data);
    debouncedSave(data); // Auto-save with debouncing
  }, [debouncedSave]);

  const handlePublish = useCallback(async (data: any) => {
    try {
      // Force immediate save before publish
      await debouncedSave.flush?.();
      console.log('Theme published with CSS URL:', cssUrl);
    } catch (err) {
      console.error('Publish failed:', err);
    }
  }, [debouncedSave, cssUrl]);

  return (
    <div className="h-screen">
      {/* Status bar */}
      <div className="bg-gray-100 px-4 py-2 text-sm border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className={`inline-flex items-center gap-1 ${isLoading ? 'text-blue-600' : 'text-green-600'}`}>
              {isLoading ? '⏳ Saving...' : '✅ Saved'}
            </span>
            {lastSaved && (
              <span className="text-gray-500">
                Last saved: {lastSaved.toLocaleTimeString()}
              </span>
            )}
            {cssUrl && (
              <span className="text-blue-600">
                CSS: {cssUrl}
              </span>
            )}
          </div>
          {error && (
            <span className="text-red-600">
              Error: {error}
            </span>
          )}
        </div>
      </div>

      <TailwindPlayProvider theme={THEME_CONFIG} enabled={true}>
        <ThemeBuilder
          data={puckData}
          onChange={handleChange}
          onPublish={handlePublish}
          enableTailwindPlay={true}
          theme={THEME_CONFIG}
          headerActions={[
            {
              type: 'button',
              label: 'Publish',
              icon: '🚀',
              variant: 'primary',
              onClick: () => handlePublish(puckData),
              disabled: isLoading,
            },
          ]}
        />
      </TailwindPlayProvider>
    </div>
  );
};

/**
 * Example 3: Theme Renderer with CSS Loading
 */
export const RendererExample: React.FC<{ configId: string }> = ({ configId }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-8">Theme Renderer Example</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6">
          <ThemeRenderer
            data={EXAMPLE_PUCK_DATA}
            configId={configId}
            enableTailwindCSS={true}
            className="theme-preview"
          />
        </div>
      </div>
    </div>
  );
};

/**
 * Example 4: Performance Monitoring
 */
export const PerformanceExample: React.FC = () => {
  const [puckData, setPuckData] = useState(EXAMPLE_PUCK_DATA);
  const [metrics, setMetrics] = useState<any>(null);

  const { debouncedSave } = useThemeBuilderAPI({
    accountId: 'perf-test-account',
    tempThemeId: 'perf-test-temp',
  });

  const handleChange = useCallback(async (data: any) => {
    const startTime = performance.now();
    
    setPuckData(data);
    
    try {
      await debouncedSave(data);
      const endTime = performance.now();
      
      setMetrics({
        saveTime: endTime - startTime,
        classCount: extractClassCount(data),
        timestamp: new Date(),
      });
    } catch (error) {
      console.error('Performance test failed:', error);
    }
  }, [debouncedSave]);

  const extractClassCount = (data: any): number => {
    let count = 0;
    const extract = (obj: any) => {
      if (obj?.className) count += obj.className.split(/\s+/).length;
      if (obj?.customClasses) count += obj.customClasses.split(/\s+/).length;
      if (Array.isArray(obj)) obj.forEach(extract);
      if (typeof obj === 'object') Object.values(obj).forEach(extract);
    };
    extract(data);
    return count;
  };

  return (
    <div className="h-screen flex">
      <div className="flex-1">
        <TailwindPlayProvider theme={THEME_CONFIG} enabled={true}>
          <ThemeBuilder
            data={puckData}
            onChange={handleChange}
            enableTailwindPlay={true}
            theme={THEME_CONFIG}
          />
        </TailwindPlayProvider>
      </div>
      
      {/* Performance metrics panel */}
      <div className="w-80 bg-gray-100 p-4 border-l">
        <h3 className="font-bold mb-4">Performance Metrics</h3>
        {metrics ? (
          <div className="space-y-2 text-sm">
            <div>Save Time: {metrics.saveTime.toFixed(2)}ms</div>
            <div>Class Count: {metrics.classCount}</div>
            <div>Last Update: {metrics.timestamp.toLocaleTimeString()}</div>
            <div className={`font-semibold ${metrics.saveTime < 700 ? 'text-green-600' : 'text-red-600'}`}>
              {metrics.saveTime < 700 ? '✅ Performance Good' : '⚠️ Performance Slow'}
            </div>
          </div>
        ) : (
          <div className="text-gray-500">Make changes to see metrics...</div>
        )}
      </div>
    </div>
  );
};

export default {
  BasicTailwindExample,
  APIIntegratedExample,
  RendererExample,
  PerformanceExample,
};
