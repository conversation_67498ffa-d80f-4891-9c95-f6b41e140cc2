/**
 * Global Styles Sidebar Example
 * Shows how Global Styles appear in component sidebar when selected
 */

import React, { useState, useCallback } from 'react';
import { ThemeBuilder, TailwindPlayProvider } from '@kit/theme-builder';
import { createGlobalStyleField } from '../src/components/global-style-system';

// Example component with Global Styles in sidebar
const ExampleButton = {
  fields: {
    text: {
      type: 'text' as const,
      label: 'Button Text',
    },
    variant: {
      type: 'select' as const,
      label: 'Variant',
      options: [
        { label: 'Primary', value: 'primary' },
        { label: 'Secondary', value: 'secondary' },
        { label: 'Outline', value: 'outline' },
      ],
    },
    // Global Styles will appear in sidebar when component is selected
    globalStyles: createGlobalStyleField('Button', ['colors', 'spacing', 'typography', 'borders', 'effects']),
    customClasses: {
      type: 'text' as const,
      label: 'Custom Classes',
      placeholder: 'hover:scale-105 transition-transform',
    },
  },
  render: ({ text, variant, globalStyles, customClasses }: any) => {
    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200';
    
    const variantClasses = {
      primary: 'bg-blue-600 text-white hover:bg-blue-700',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700',
      outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white',
    };

    const allClasses = [
      baseClasses,
      variantClasses[variant] || variantClasses.primary,
      globalStyles,
      customClasses,
    ].filter(Boolean).join(' ');

    return (
      <button className={allClasses}>
        {text || 'Button'}
      </button>
    );
  },
};

const ExampleCard = {
  fields: {
    title: {
      type: 'text' as const,
      label: 'Card Title',
    },
    content: {
      type: 'textarea' as const,
      label: 'Card Content',
    },
    // Global Styles for Card component
    globalStyles: createGlobalStyleField('Card', ['colors', 'borders', 'spacing', 'effects']),
    customClasses: {
      type: 'text' as const,
      label: 'Custom Classes',
      placeholder: 'hover:shadow-lg transition-shadow',
    },
  },
  render: ({ title, content, globalStyles, customClasses }: any) => {
    const baseClasses = 'bg-white rounded-lg shadow-md p-6';
    const allClasses = [baseClasses, globalStyles, customClasses].filter(Boolean).join(' ');

    return (
      <div className={allClasses}>
        {title && <h3 className="text-lg font-semibold mb-2">{title}</h3>}
        {content && <p className="text-gray-600">{content}</p>}
      </div>
    );
  },
};

const ExampleText = {
  fields: {
    content: {
      type: 'textarea' as const,
      label: 'Text Content',
    },
    tag: {
      type: 'select' as const,
      label: 'HTML Tag',
      options: [
        { label: 'Paragraph', value: 'p' },
        { label: 'Heading 1', value: 'h1' },
        { label: 'Heading 2', value: 'h2' },
        { label: 'Heading 3', value: 'h3' },
      ],
    },
    // Global Styles for Text component
    globalStyles: createGlobalStyleField('Text', ['typography', 'colors', 'spacing']),
    customClasses: {
      type: 'text' as const,
      label: 'Custom Classes',
      placeholder: 'hover:text-blue-600 transition-colors',
    },
  },
  render: ({ content, tag, globalStyles, customClasses }: any) => {
    const Tag = tag || 'p';
    const allClasses = [globalStyles, customClasses].filter(Boolean).join(' ');

    return (
      <Tag className={allClasses}>
        {content || 'Your text here...'}
      </Tag>
    );
  },
};

// Puck configuration with Global Styles
const GLOBAL_STYLES_CONFIG = {
  components: {
    Button: ExampleButton,
    Card: ExampleCard,
    Text: ExampleText,
  },
  categories: {
    form: {
      title: 'Form',
      components: ['Button'],
    },
    display: {
      title: 'Display',
      components: ['Card'],
    },
    typography: {
      title: 'Typography',
      components: ['Text'],
    },
  },
};

// Example data
const EXAMPLE_DATA = {
  content: [
    {
      type: 'Text',
      props: {
        content: 'Global Styles Sidebar Demo',
        tag: 'h1',
        globalStyles: 'text-3xl font-bold text-center mb-8 text-gray-900',
        customClasses: '',
      },
    },
    {
      type: 'Card',
      props: {
        title: 'Example Card',
        content: 'Click on this card to see Global Styles in the sidebar. You can apply TailwindCSS classes visually.',
        globalStyles: 'mb-6',
        customClasses: 'hover:shadow-xl transition-shadow duration-300',
      },
    },
    {
      type: 'Button',
      props: {
        text: 'Style This Button',
        variant: 'primary',
        globalStyles: 'px-6 py-3 rounded-lg',
        customClasses: 'hover:scale-105 active:scale-95 transition-transform',
      },
    },
    {
      type: 'Text',
      props: {
        content: 'Select any component above to see Global Styles options in the sidebar →',
        tag: 'p',
        globalStyles: 'text-center text-gray-600 mt-8',
        customClasses: '',
      },
    },
  ],
  root: {
    props: {
      title: 'Global Styles Sidebar Demo',
    },
  },
};

/**
 * Basic Global Styles Sidebar Example
 */
export const GlobalStylesSidebarDemo: React.FC = () => {
  const [data, setData] = useState(EXAMPLE_DATA);

  const handleChange = useCallback((newData: any) => {
    setData(newData);
    console.log('Data changed with global styles:', newData);
  }, []);

  const handlePublish = useCallback((publishData: any) => {
    console.log('Publishing with global styles:', publishData);
    alert('Theme published! Check console for data with global styles.');
  }, []);

  return (
    <div className="h-screen">
      <TailwindPlayProvider enabled={true}>
        <ThemeBuilder
          config={GLOBAL_STYLES_CONFIG}
          data={data}
          onChange={handleChange}
          onPublish={handlePublish}
        />
      </TailwindPlayProvider>
    </div>
  );
};

/**
 * Advanced Example with Instructions
 */
export const GlobalStylesSidebarAdvanced: React.FC = () => {
  const [data, setData] = useState(EXAMPLE_DATA);
  const [showInstructions, setShowInstructions] = useState(true);

  const handleChange = useCallback((newData: any) => {
    setData(newData);
  }, []);

  const handlePublish = useCallback((publishData: any) => {
    console.log('Publishing:', publishData);
    
    // Extract all global styles
    const extractGlobalStyles = (obj: any): string[] => {
      const styles: string[] = [];
      
      const extract = (item: any) => {
        if (item?.props?.globalStyles) {
          styles.push(...item.props.globalStyles.split(' ').filter(Boolean));
        }
        if (item?.content) {
          item.content.forEach(extract);
        }
      };
      
      if (obj?.content) {
        obj.content.forEach(extract);
      }
      
      return [...new Set(styles)];
    };

    const allGlobalStyles = extractGlobalStyles(publishData);
    alert(`Published with ${allGlobalStyles.length} unique global style classes!`);
  }, []);

  return (
    <div className="h-screen flex flex-col">
      {/* Instructions Banner */}
      {showInstructions && (
        <div className="bg-blue-50 border-b border-blue-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="text-blue-600">ℹ️</div>
              <div>
                <div className="font-medium text-blue-900">How to use Global Styles</div>
                <div className="text-sm text-blue-700">
                  1. Click on any component in the preview
                  2. Look for "Global Styles" section in the right sidebar
                  3. Expand it to see style categories
                  4. Click style options to apply them instantly
                </div>
              </div>
            </div>
            <button
              onClick={() => setShowInstructions(false)}
              className="text-blue-600 hover:text-blue-800"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Theme Builder */}
      <div className="flex-1">
        <TailwindPlayProvider enabled={true}>
          <ThemeBuilder
            config={GLOBAL_STYLES_CONFIG}
            data={data}
            onChange={handleChange}
            onPublish={handlePublish}
          />
        </TailwindPlayProvider>
      </div>
    </div>
  );
};

/**
 * Component Showcase Example
 */
export const ComponentShowcase: React.FC = () => {
  const showcaseData = {
    content: [
      {
        type: 'Text',
        props: {
          content: 'Component Showcase',
          tag: 'h1',
          globalStyles: 'text-4xl font-bold text-center mb-12 text-gray-900',
        },
      },
      {
        type: 'Card',
        props: {
          title: 'Card Component',
          content: 'This card has global styles applied. Click to modify colors, spacing, borders, and effects.',
          globalStyles: 'mb-8 bg-gradient-to-br from-blue-50 to-indigo-100 border border-blue-200',
        },
      },
      {
        type: 'Button',
        props: {
          text: 'Primary Button',
          variant: 'primary',
          globalStyles: 'mr-4 px-8 py-4 text-lg font-semibold rounded-xl shadow-lg',
          customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-200',
        },
      },
      {
        type: 'Button',
        props: {
          text: 'Secondary Button',
          variant: 'secondary',
          globalStyles: 'px-8 py-4 text-lg font-semibold rounded-xl',
          customClasses: 'hover:scale-105 transition-transform duration-200',
        },
      },
      {
        type: 'Text',
        props: {
          content: 'This text component demonstrates typography styling options. You can change font size, weight, color, and spacing.',
          tag: 'p',
          globalStyles: 'text-lg leading-relaxed text-gray-700 mt-8 max-w-2xl mx-auto text-center',
        },
      },
    ],
    root: {
      props: {
        title: 'Component Showcase',
      },
    },
  };

  const [data, setData] = useState(showcaseData);

  return (
    <div className="h-screen">
      <TailwindPlayProvider enabled={true}>
        <ThemeBuilder
          config={GLOBAL_STYLES_CONFIG}
          data={data}
          onChange={setData}
          onPublish={(data) => console.log('Showcase published:', data)}
        />
      </TailwindPlayProvider>
    </div>
  );
};

export default {
  GlobalStylesSidebarDemo,
  GlobalStylesSidebarAdvanced,
  ComponentShowcase,
};
