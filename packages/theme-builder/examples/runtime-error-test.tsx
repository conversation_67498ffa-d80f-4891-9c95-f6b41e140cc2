/**
 * Runtime Error Test Component
 * Tests the fixed TailwindPlayProvider to ensure no runtime errors
 */

import React, { useState, useEffect } from 'react';
import { 
  TailwindPlayProvider, 
  SafeTailwindPlayProvider,
  useTailwindPlay,
  useSafeTailwindPlay 
} from '@kit/theme-builder';

// Test component that rapidly mounts/unmounts TailwindPlayProvider
const TailwindPlayStressTest: React.FC = () => {
  const [showProvider, setShowProvider] = useState(true);
  const [mountCount, setMountCount] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);

  // Rapidly toggle provider to test cleanup
  useEffect(() => {
    const interval = setInterval(() => {
      setShowProvider(prev => !prev);
      setMountCount(prev => prev + 1);
    }, 2000); // Toggle every 2 seconds

    return () => clearInterval(interval);
  }, []);

  // Listen for errors
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (event.message.includes('removeChild')) {
        setErrors(prev => [...prev, `${new Date().toLocaleTimeString()}: ${event.message}`]);
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">TailwindPlay Runtime Error Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Status Panel */}
        <div className="bg-white border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Test Status</h2>
          <div className="space-y-2 text-sm">
            <div>Mount Count: <span className="font-mono">{mountCount}</span></div>
            <div>Provider Active: <span className={`font-mono ${showProvider ? 'text-green-600' : 'text-red-600'}`}>
              {showProvider ? 'YES' : 'NO'}
            </span></div>
            <div>Errors: <span className={`font-mono ${errors.length === 0 ? 'text-green-600' : 'text-red-600'}`}>
              {errors.length}
            </span></div>
          </div>
          
          {errors.length > 0 && (
            <div className="mt-4">
              <h3 className="font-medium text-red-600 mb-2">Errors:</h3>
              <div className="bg-red-50 border border-red-200 rounded p-2 max-h-32 overflow-auto">
                {errors.map((error, index) => (
                  <div key={index} className="text-xs text-red-700 mb-1">
                    {error}
                  </div>
                ))}
              </div>
              <button
                onClick={() => setErrors([])}
                className="mt-2 text-xs bg-red-100 hover:bg-red-200 text-red-700 px-2 py-1 rounded"
              >
                Clear Errors
              </button>
            </div>
          )}
        </div>

        {/* Test Controls */}
        <div className="bg-white border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Test Controls</h2>
          <div className="space-y-3">
            <button
              onClick={() => setShowProvider(!showProvider)}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
            >
              {showProvider ? 'Hide' : 'Show'} Provider
            </button>
            
            <button
              onClick={() => {
                setMountCount(0);
                setErrors([]);
              }}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
            >
              Reset Test
            </button>
            
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      </div>

      {/* Test Content */}
      <div className="mt-6 bg-white border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Test Content</h2>
        
        {showProvider ? (
          <TailwindPlayProvider 
            enabled={true}
            theme={{
              colors: {
                primary: {
                  500: '#3b82f6',
                  600: '#2563eb',
                },
              },
            }}
          >
            <TestContent />
          </TailwindPlayProvider>
        ) : (
          <div className="text-gray-500 italic">
            TailwindPlayProvider is not mounted
          </div>
        )}
      </div>

      {/* Safe Provider Test */}
      <div className="mt-6 bg-white border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Safe Provider Test</h2>
        
        <SafeTailwindPlayProvider 
          enabled={true}
          onLoad={() => console.log('Safe provider loaded')}
          onError={(error) => console.error('Safe provider error:', error)}
        >
          <SafeTestContent />
        </SafeTailwindPlayProvider>
      </div>
    </div>
  );
};

// Test content component
const TestContent: React.FC = () => {
  const { isReady } = useTailwindPlay();
  
  return (
    <div className="space-y-4">
      <div className={`p-4 rounded-lg ${isReady ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
        TailwindPlay Status: {isReady ? 'Ready' : 'Loading...'}
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="bg-blue-500 text-white p-4 rounded-lg">
          <h3 className="font-semibold">Blue Card</h3>
          <p className="text-sm opacity-90">Testing TailwindCSS classes</p>
        </div>
        
        <div className="bg-green-500 text-white p-4 rounded-lg">
          <h3 className="font-semibold">Green Card</h3>
          <p className="text-sm opacity-90">With hover effects</p>
        </div>
        
        <div className="bg-purple-500 text-white p-4 rounded-lg">
          <h3 className="font-semibold">Purple Card</h3>
          <p className="text-sm opacity-90">And transitions</p>
        </div>
      </div>
      
      <button className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg transition-colors">
        Custom Theme Button
      </button>
    </div>
  );
};

// Safe test content component
const SafeTestContent: React.FC = () => {
  const { isLoaded, error, retry } = useSafeTailwindPlay();
  
  return (
    <div className="space-y-4">
      <div className={`p-4 rounded-lg ${
        error ? 'bg-red-100 text-red-800' : 
        isLoaded ? 'bg-green-100 text-green-800' : 
        'bg-yellow-100 text-yellow-800'
      }`}>
        Safe TailwindPlay Status: {
          error ? `Error: ${error.message}` :
          isLoaded ? 'Loaded' : 'Loading...'
        }
        {error && (
          <button
            onClick={retry}
            className="ml-2 bg-red-200 hover:bg-red-300 text-red-800 px-2 py-1 rounded text-sm"
          >
            Retry
          </button>
        )}
      </div>
      
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg">
        <h3 className="text-xl font-bold mb-2">Safe Provider Test</h3>
        <p className="opacity-90">
          This content is rendered with the SafeTailwindPlayProvider, 
          which includes better error handling and script management.
        </p>
      </div>
    </div>
  );
};

export default TailwindPlayStressTest;
