/**
 * Simple Usage Example - No External Dependencies
 * Shows basic TailwindCSS integration without lodash
 */

import React, { useState, useCallback } from 'react';
import { 
  ThemeBuilder, 
  TailwindPlayProvider,
  debounce // Using our custom debounce utility
} from '@kit/theme-builder';

// Simple example data
const SIMPLE_PUCK_DATA = {
  content: [
    {
      type: 'ZMPButton',
      props: {
        text: 'Hello World',
        className: 'bg-blue-500 text-white px-4 py-2 rounded',
        customClasses: 'hover:bg-blue-600 transition-colors',
      },
    },
  ],
  root: {
    props: {
      title: 'Simple Theme',
      containerClasses: 'max-w-4xl mx-auto p-4',
    },
  },
};

/**
 * Simple Theme Builder Example
 */
export const SimpleThemeBuilder: React.FC = () => {
  const [puckData, setPuckData] = useState(SIMPLE_PUCK_DATA);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  // Simple save function with debouncing
  const saveConfig = useCallback(
    debounce(async (data: any) => {
      setSaveStatus('saving');
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('Config saved:', data);
        setSaveStatus('saved');
        
        // Reset status after 2 seconds
        setTimeout(() => setSaveStatus('idle'), 2000);
      } catch (error) {
        console.error('Save failed:', error);
        setSaveStatus('error');
      }
    }, 1000),
    []
  );

  const handleChange = useCallback((data: any) => {
    setPuckData(data);
    saveConfig(data);
  }, [saveConfig]);

  const handlePublish = useCallback((data: any) => {
    console.log('Publishing theme:', data);
    alert('Theme published! (This is just a demo)');
  }, []);

  return (
    <div className="h-screen flex flex-col">
      {/* Status Bar */}
      <div className="bg-gray-100 px-4 py-2 border-b">
        <div className="flex items-center justify-between">
          <h1 className="font-semibold">Simple Theme Builder</h1>
          <div className="flex items-center gap-2">
            {saveStatus === 'saving' && (
              <span className="text-blue-600">💾 Saving...</span>
            )}
            {saveStatus === 'saved' && (
              <span className="text-green-600">✅ Saved</span>
            )}
            {saveStatus === 'error' && (
              <span className="text-red-600">❌ Error</span>
            )}
          </div>
        </div>
      </div>

      {/* Theme Builder */}
      <div className="flex-1">
        <TailwindPlayProvider enabled={true}>
          <ThemeBuilder
            data={puckData}
            onChange={handleChange}
            onPublish={handlePublish}
            enableTailwindPlay={true}
            headerActions={[
              {
                type: 'button',
                label: 'Preview',
                icon: '👁️',
                variant: 'secondary',
                onClick: () => console.log('Preview clicked'),
              },
              {
                type: 'button',
                label: 'Publish',
                icon: '🚀',
                variant: 'primary',
                onClick: () => handlePublish(puckData),
              },
            ]}
          />
        </TailwindPlayProvider>
      </div>
    </div>
  );
};

/**
 * Minimal API Integration Example
 */
export const MinimalAPIExample: React.FC = () => {
  const [puckData, setPuckData] = useState(SIMPLE_PUCK_DATA);
  const [isLoading, setIsLoading] = useState(false);

  // Manual save function
  const saveToAPI = useCallback(async (data: any) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/theme-builder/save-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: data,
          accountId: 'demo-account',
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log('Save successful:', result);
      
      return result;
    } catch (error) {
      console.error('Save failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleSave = useCallback(() => {
    saveToAPI(puckData);
  }, [puckData, saveToAPI]);

  const handleChange = useCallback((data: any) => {
    setPuckData(data);
  }, []);

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-gray-100 px-4 py-2 border-b">
        <div className="flex items-center justify-between">
          <h1 className="font-semibold">Minimal API Example</h1>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            {isLoading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>

      {/* Builder */}
      <div className="flex-1">
        <TailwindPlayProvider enabled={true}>
          <ThemeBuilder
            data={puckData}
            onChange={handleChange}
            enableTailwindPlay={true}
          />
        </TailwindPlayProvider>
      </div>
    </div>
  );
};

/**
 * Debug Example - Shows extracted classes
 */
export const DebugExample: React.FC = () => {
  const [puckData, setPuckData] = useState(SIMPLE_PUCK_DATA);
  const [extractedClasses, setExtractedClasses] = useState<string[]>([]);

  // Extract classes for debugging
  const extractClasses = useCallback((data: any) => {
    const classes = new Set<string>();
    
    const extract = (obj: any) => {
      if (!obj || typeof obj !== 'object') return;
      
      if (typeof obj.className === 'string') {
        obj.className.split(/\s+/).forEach((cls: string) => {
          if (cls.trim()) classes.add(cls.trim());
        });
      }
      
      if (typeof obj.customClasses === 'string') {
        obj.customClasses.split(/\s+/).forEach((cls: string) => {
          if (cls.trim()) classes.add(cls.trim());
        });
      }
      
      Object.values(obj).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(extract);
        } else if (typeof value === 'object') {
          extract(value);
        }
      });
    };
    
    extract(data);
    return Array.from(classes);
  }, []);

  const handleChange = useCallback((data: any) => {
    setPuckData(data);
    const classes = extractClasses(data);
    setExtractedClasses(classes);
  }, [extractClasses]);

  // Extract classes on mount
  React.useEffect(() => {
    const classes = extractClasses(puckData);
    setExtractedClasses(classes);
  }, [puckData, extractClasses]);

  return (
    <div className="h-screen flex">
      {/* Builder */}
      <div className="flex-1">
        <TailwindPlayProvider enabled={true}>
          <ThemeBuilder
            data={puckData}
            onChange={handleChange}
            enableTailwindPlay={true}
          />
        </TailwindPlayProvider>
      </div>
      
      {/* Debug Panel */}
      <div className="w-80 bg-gray-50 border-l p-4 overflow-auto">
        <h3 className="font-bold mb-4">Debug Info</h3>
        
        <div className="mb-4">
          <h4 className="font-semibold mb-2">Extracted Classes ({extractedClasses.length})</h4>
          <div className="space-y-1 text-sm">
            {extractedClasses.map((cls, index) => (
              <div key={index} className="bg-white px-2 py-1 rounded border">
                <code>{cls}</code>
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <h4 className="font-semibold mb-2">Puck Data</h4>
          <pre className="text-xs bg-white p-2 rounded border overflow-auto max-h-40">
            {JSON.stringify(puckData, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default {
  SimpleThemeBuilder,
  MinimalAPIExample,
  DebugExample,
};
