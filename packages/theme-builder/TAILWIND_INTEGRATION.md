# TailwindCSS Integration with Puck Editor

Complete implementation of TailwindCSS integration with Puck Editor for real-time theme building and CSS generation.

## 🎯 Overview

This implementation provides:
- **Real-time preview** with Tailwind Play CDN (<200ms)
- **Server-side CSS generation** with TailwindCSS CLI (<700ms)
- **MD5 caching** for optimal performance (cache hit ~50ms)
- **Arbitrary values support** (e.g., `w-[123px]`, `bg-[#123456]`)
- **Production-ready** CSS delivery
- **No database schema changes** - uses existing config JSONB column

## 🚀 Features

### ✅ Dynamic Styling Options
- **Predefined Classes**: Curated set of Tailwind classes for common use cases
- **Safelist Support**: Dynamic class generation with Tailwind v4.0 safelist
- **Component Variants**: Multiple styling variants for each component
- **Responsive Design**: Built-in responsive utilities
- **Custom Classes**: Support for custom Tailwind classes

### ✅ Component Support
- **ZMPRoot**: Global theme configuration with Tailwind integration
- **ZMPGrid**: Dynamic grid layouts with Tailwind classes
- **ZMPButton**: Multiple button variants with Tailwind styling
- **ZMPCard**: Card components with dynamic styling options
- **ZMPText**: Typography components with Tailwind utilities

## 📦 Installation & Setup

### 1. Dependencies
The package includes all necessary dependencies:
```json
{
  "tailwindcss": "4.1.3",
  "@tailwindcss/postcss": "4.1.3",
  "postcss": "^8.4.47"
}
```

### 2. CSS Import
The main CSS file is automatically imported:
```typescript
import '@kit/theme-builder'; // Includes Tailwind CSS
```

### 3. PostCSS Configuration
PostCSS is configured for Tailwind v4.0:
```javascript
// postcss.config.mjs
export default {
  plugins: {
    '@tailwindcss/postcss': {},
  },
};
```

## 🎨 Dynamic Styling System

### Predefined Options
The system provides predefined options for common styling needs:

```typescript
// Color variants
const colors = {
  primary: { bg: 'bg-blue-600', text: 'text-blue-600' },
  secondary: { bg: 'bg-gray-600', text: 'text-gray-600' },
  accent: { bg: 'bg-amber-500', text: 'text-amber-500' },
  // ... more colors
};

// Size variants
const sizes = {
  xs: { padding: 'px-2 py-1', text: 'text-xs' },
  sm: { padding: 'px-3 py-2', text: 'text-sm' },
  md: { padding: 'px-4 py-2', text: 'text-base' },
  // ... more sizes
};
```

### Safelist for Dynamic Classes
Dynamic classes are pre-defined in `safelist.txt`:
```
bg-blue-600
bg-gray-600
text-white
grid-cols-1
grid-cols-2
gap-4
p-4
rounded-lg
shadow-md
```

## 🔧 Component Usage

### ZMPRoot with Tailwind
```typescript
<ZMPRoot
  enableTailwindStyling={true}
  tailwindTheme="primary"
  containerClasses="max-w-4xl mx-auto"
>
  {children}
</ZMPRoot>
```

### ZMPButton with Dynamic Styling
```typescript
<ZMPButton
  text="Click me"
  useTailwind={true}
  tailwindVariant="filled"
  tailwindColor="primary"
  tailwindSize="md"
  tailwindBorderRadius="lg"
  customClasses="hover:scale-105 transition-transform"
/>
```

### ZMPGrid with Tailwind
```typescript
<ZMPGrid
  useTailwind={true}
  tailwindColumns={3}
  tailwindGap="md"
  customClasses="lg:grid-cols-4 md:gap-6"
/>
```

## 🎛️ Puck Editor Integration

### Field Configuration
Components expose Tailwind options in the Puck editor:

```typescript
fields: {
  useTailwind: {
    type: 'radio',
    label: 'Use Tailwind Styling',
    options: [
      { label: 'Yes', value: true },
      { label: 'No', value: false },
    ],
  },
  tailwindColor: {
    type: 'select',
    label: 'Color Theme',
    options: Object.entries(TAILWIND_OPTIONS.colors).map(([key, value]) => ({
      label: value.label,
      value: key,
    })),
  },
  customClasses: {
    type: 'text',
    label: 'Custom Classes',
    placeholder: 'e.g., hover:scale-105 active:scale-95',
  },
}
```

### Dynamic Class Generation
```typescript
import { generateDynamicClasses } from '@kit/theme-builder/utils';

const classes = generateDynamicClasses({
  variant: 'filled',
  size: 'md',
  color: 'primary',
  borderRadius: 'lg',
  shadow: 'md',
  customClasses: 'hover:scale-105',
});
```

## 🎯 Best Practices

### 1. Use Predefined Options
Prefer predefined options over custom classes for consistency:
```typescript
// ✅ Good
tailwindColor="primary"
tailwindSize="md"

// ❌ Avoid
customClasses="bg-blue-600 px-4 py-2"
```

### 2. Responsive Design
Use responsive utilities in custom classes:
```typescript
customClasses="lg:grid-cols-4 md:gap-6 sm:grid-cols-2"
```

### 3. Performance Considerations
- All dynamic classes are pre-defined in safelist
- No runtime class generation
- Optimized CSS bundle size

### 4. Fallback Support
Components support both Tailwind and ZMP-UI styling:
```typescript
// Tailwind styling
<ZMPButton useTailwind={true} tailwindColor="primary" />

// ZMP-UI styling (fallback)
<ZMPButton useTailwind={false} variant="primary" />
```

## 🔍 Troubleshooting

### Classes Not Applied
1. Check if class is in safelist.txt
2. Verify Tailwind CSS is imported
3. Ensure PostCSS is configured correctly

### Build Issues
1. Run `npm run build:css` to generate CSS
2. Check PostCSS configuration
3. Verify Tailwind dependencies

### Type Errors
1. Ensure proper TypeScript configuration
2. Check component prop types
3. Verify utility function imports

## 📚 API Reference

### Utility Functions
- `cn()`: Combine class names
- `generateDynamicClasses()`: Generate component classes
- `generateGridClasses()`: Generate grid classes
- `generateCSSVariables()`: Convert theme to CSS variables

### Constants
- `TAILWIND_OPTIONS`: Predefined styling options
- `DEFAULT_COLORS`: Default color palette
- `DEFAULT_TYPOGRAPHY`: Default typography settings

## 🔄 Migration Guide

### From ZMP-UI Only
1. Enable Tailwind in components: `useTailwind={true}`
2. Configure Tailwind options in Puck editor
3. Test styling in preview mode
4. Gradually migrate components

### Adding Custom Classes
1. Add classes to safelist.txt
2. Rebuild CSS: `npm run build:css`
3. Use in customClasses prop
4. Test in development mode

### Database Schema

CSS data is stored within the existing `config` JSONB column:

```json
{
  "content": [/* Puck data */],
  "root": {/* Root props */},
  "cssText": "/* Generated CSS */",
  "configHash": "abc123def456",
  "generatedAt": "2024-06-24T10:30:00Z"
}
```

**Benefits:**
- No schema changes required
- Atomic updates (config + CSS together)
- Backward compatibility maintained

## 🎉 Examples

See the `examples/` directory for complete implementation examples:
- Basic Tailwind integration with real-time preview
- API integration with auto-save
- Theme renderer with CSS loading
- Performance monitoring
