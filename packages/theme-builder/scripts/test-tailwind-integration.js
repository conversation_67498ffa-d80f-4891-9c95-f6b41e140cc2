#!/usr/bin/env node

/**
 * Test script for TailwindCSS integration
 * Tests all components of the TailwindCSS + Puck Editor integration
 */

const { performance } = require('perf_hooks');
const fs = require('fs').promises;
const path = require('path');

// Mock Puck data for testing
const mockPuckData = {
  content: [
    {
      type: 'ZMPGrid',
      props: {
        tailwindColumns: 3,
        tailwindGap: 'md',
        customClasses: 'lg:grid-cols-4 md:gap-6',
        useTailwind: true,
      },
      content: [
        {
          type: 'ZMPCard',
          props: {
            title: 'Test Card',
            className: 'bg-blue-500 text-white p-4 rounded-lg shadow-md',
            tailwindColor: 'primary',
            tailwindSize: 'md',
            customClasses: 'hover:scale-105 transition-transform',
          },
        },
        {
          type: 'ZMPButton',
          props: {
            text: 'Click Me',
            className: 'bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-full',
            tailwindVariant: 'filled',
            tailwindColor: 'success',
            customClasses: 'active:scale-95 transition-all duration-200',
          },
        },
      ],
    },
    {
      type: 'ZMPText',
      props: {
        content: 'Dynamic Text with Arbitrary Values',
        className: 'text-[#ff6b35] text-[24px] font-[600] leading-[1.4] mb-[16px]',
        customClasses: 'hover:text-[#e55a2b] transition-colors',
      },
    },
  ],
  root: {
    props: {
      title: 'Test Theme',
      primaryColor: '#3b82f6',
      secondaryColor: '#64748b',
      accentColor: '#f59e0b',
      enableTailwindStyling: true,
      containerClasses: 'max-w-6xl mx-auto px-4 py-8',
    },
  },
  // CSS data will be stored here
  cssText: null,
  configHash: null,
  generatedAt: null,
};

// Test configuration
const testConfig = {
  baseUrl: 'http://localhost:3000',
  accountId: 'test-account-123',
  tempThemeId: 'temp-theme-456',
  performanceTargets: {
    saveConfig: 700, // ms
    getConfig: 300, // ms
    cacheHit: 50, // ms
    preview: 200, // ms
  },
};

class TailwindIntegrationTester {
  constructor() {
    this.results = {
      tests: [],
      performance: {},
      errors: [],
      summary: {
        passed: 0,
        failed: 0,
        total: 0,
      },
    };
  }

  async runAllTests() {
    console.log('🚀 Starting TailwindCSS Integration Tests...\n');

    try {
      // Test 1: Class Extraction
      await this.testClassExtraction();

      // Test 2: CSS Generation
      await this.testCSSGeneration();

      // Test 3: API Performance
      await this.testAPIPerformance();

      // Test 4: Caching
      await this.testCaching();

      // Test 5: Arbitrary Values
      await this.testArbitraryValues();

      // Test 6: Real-time Preview
      await this.testRealtimePreview();

      // Generate report
      this.generateReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      this.results.errors.push({
        test: 'Test Suite',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async testClassExtraction() {
    console.log('📋 Testing Class Extraction...');
    const startTime = performance.now();

    try {
      // Simulate class extraction from Puck data
      const extractedClasses = this.extractClassesFromMockData(mockPuckData);
      
      const expectedClasses = [
        'lg:grid-cols-4',
        'md:gap-6',
        'bg-blue-500',
        'text-white',
        'p-4',
        'rounded-lg',
        'shadow-md',
        'hover:scale-105',
        'transition-transform',
        'bg-green-600',
        'hover:bg-green-700',
        'px-6',
        'py-3',
        'rounded-full',
        'active:scale-95',
        'transition-all',
        'duration-200',
        'text-[#ff6b35]',
        'text-[24px]',
        'font-[600]',
        'leading-[1.4]',
        'mb-[16px]',
        'hover:text-[#e55a2b]',
        'transition-colors',
        'max-w-6xl',
        'mx-auto',
        'px-4',
        'py-8',
      ];

      const duration = performance.now() - startTime;
      const passed = this.validateExtractedClasses(extractedClasses, expectedClasses);

      this.recordTest('Class Extraction', passed, duration, {
        extracted: extractedClasses.length,
        expected: expectedClasses.length,
        classes: extractedClasses,
      });

      console.log(`   ✅ Extracted ${extractedClasses.length} classes in ${duration.toFixed(2)}ms`);

    } catch (error) {
      this.recordTest('Class Extraction', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ Failed: ${error.message}`);
    }
  }

  async testCSSGeneration() {
    console.log('🎨 Testing CSS Generation...');
    const startTime = performance.now();

    try {
      // Simulate CSS generation
      const classes = this.extractClassesFromMockData(mockPuckData);
      const cssResult = await this.simulateCSSGeneration(classes);
      
      const duration = performance.now() - startTime;
      const passed = cssResult.css.length > 0 && duration < testConfig.performanceTargets.saveConfig;

      this.recordTest('CSS Generation', passed, duration, {
        cssSize: cssResult.css.length,
        classCount: classes.length,
        compressionRatio: cssResult.compressionRatio,
      });

      console.log(`   ✅ Generated ${cssResult.css.length} bytes CSS in ${duration.toFixed(2)}ms`);

    } catch (error) {
      this.recordTest('CSS Generation', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ Failed: ${error.message}`);
    }
  }

  async testAPIPerformance() {
    console.log('⚡ Testing API Performance...');

    // Test save-config API
    const saveStartTime = performance.now();
    try {
      const saveResult = await this.simulateAPICall('/api/theme-builder/save-config', 'POST', {
        config: mockPuckData,
        accountId: testConfig.accountId,
        tempThemeId: testConfig.tempThemeId,
      });

      const saveDuration = performance.now() - saveStartTime;
      const savePassed = saveDuration < testConfig.performanceTargets.saveConfig;

      this.recordTest('Save Config API', savePassed, saveDuration, saveResult);
      console.log(`   ✅ Save API: ${saveDuration.toFixed(2)}ms (target: ${testConfig.performanceTargets.saveConfig}ms)`);

    } catch (error) {
      this.recordTest('Save Config API', false, performance.now() - saveStartTime, { error: error.message });
      console.log(`   ❌ Save API failed: ${error.message}`);
    }

    // Test get-config API
    const getStartTime = performance.now();
    try {
      const getResult = await this.simulateAPICall('/api/theme-builder/get-config', 'GET', {
        configId: testConfig.tempThemeId,
        accountId: testConfig.accountId,
      });

      const getDuration = performance.now() - getStartTime;
      const getPassed = getDuration < testConfig.performanceTargets.getConfig;

      this.recordTest('Get Config API', getPassed, getDuration, getResult);
      console.log(`   ✅ Get API: ${getDuration.toFixed(2)}ms (target: ${testConfig.performanceTargets.getConfig}ms)`);

    } catch (error) {
      this.recordTest('Get Config API', false, performance.now() - getStartTime, { error: error.message });
      console.log(`   ❌ Get API failed: ${error.message}`);
    }
  }

  async testCaching() {
    console.log('💾 Testing Caching...');
    const startTime = performance.now();

    try {
      // Simulate cache hit scenario
      const cacheResult = await this.simulateCacheHit();
      
      const duration = performance.now() - startTime;
      const passed = cacheResult.cached && duration < testConfig.performanceTargets.cacheHit;

      this.recordTest('Caching', passed, duration, cacheResult);
      console.log(`   ✅ Cache hit: ${duration.toFixed(2)}ms (target: ${testConfig.performanceTargets.cacheHit}ms)`);

    } catch (error) {
      this.recordTest('Caching', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ Caching failed: ${error.message}`);
    }
  }

  async testArbitraryValues() {
    console.log('🎯 Testing Arbitrary Values...');
    const startTime = performance.now();

    try {
      const arbitraryClasses = [
        'w-[123px]',
        'h-[456px]',
        'bg-[#ff6b35]',
        'text-[24px]',
        'font-[600]',
        'leading-[1.4]',
        'mb-[16px]',
        'p-[12px_24px]',
        'border-[2px_solid_#e5e7eb]',
      ];

      const cssResult = await this.simulateCSSGeneration(arbitraryClasses);
      
      const duration = performance.now() - startTime;
      const passed = cssResult.css.includes('[123px]') && cssResult.css.includes('#ff6b35');

      this.recordTest('Arbitrary Values', passed, duration, {
        classes: arbitraryClasses,
        cssSize: cssResult.css.length,
      });

      console.log(`   ✅ Arbitrary values supported: ${arbitraryClasses.length} classes`);

    } catch (error) {
      this.recordTest('Arbitrary Values', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ Arbitrary values failed: ${error.message}`);
    }
  }

  async testRealtimePreview() {
    console.log('⚡ Testing Real-time Preview...');
    const startTime = performance.now();

    try {
      // Simulate Tailwind Play CDN loading and configuration
      const previewResult = await this.simulateRealtimePreview();
      
      const duration = performance.now() - startTime;
      const passed = duration < testConfig.performanceTargets.preview;

      this.recordTest('Real-time Preview', passed, duration, previewResult);
      console.log(`   ✅ Preview ready: ${duration.toFixed(2)}ms (target: ${testConfig.performanceTargets.preview}ms)`);

    } catch (error) {
      this.recordTest('Real-time Preview', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ Preview failed: ${error.message}`);
    }
  }

  // Helper methods for simulation
  extractClassesFromMockData(data) {
    const classes = new Set();
    
    const extractFromObject = (obj) => {
      if (!obj || typeof obj !== 'object') return;
      
      if (typeof obj.className === 'string') {
        obj.className.split(/\s+/).forEach(cls => classes.add(cls.trim()));
      }
      
      if (typeof obj.customClasses === 'string') {
        obj.customClasses.split(/\s+/).forEach(cls => classes.add(cls.trim()));
      }
      
      Object.values(obj).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(item => extractFromObject(item));
        } else if (typeof value === 'object') {
          extractFromObject(value);
        }
      });
    };
    
    extractFromObject(data);
    return Array.from(classes).filter(cls => cls.length > 0);
  }

  async simulateCSSGeneration(classes) {
    // Simulate CSS generation with realistic timing
    await new Promise(resolve => setTimeout(resolve, Math.random() * 200 + 100));
    
    const css = `/* Generated CSS for ${classes.length} classes */\n` +
                classes.map(cls => `.${cls.replace(/[[\]]/g, '\\$&')} { /* styles */ }`).join('\n');
    
    return {
      css,
      classes,
      size: css.length,
      compressionRatio: 0.3 + Math.random() * 0.2, // Simulate 30-50% compression
    };
  }

  async simulateAPICall(endpoint, method, data) {
    // Simulate API call with realistic timing
    const delay = endpoint.includes('save') ? 200 + Math.random() * 300 : 50 + Math.random() * 150;
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return {
      success: true,
      data: {
        configId: 'test-config-123',
        cssUrl: '/api/theme-builder/css/test-config-123.css',
        hash: 'abc123def456',
        size: 1024 + Math.floor(Math.random() * 2048),
      },
    };
  }

  async simulateCacheHit() {
    // Simulate fast cache hit
    await new Promise(resolve => setTimeout(resolve, 10 + Math.random() * 30));
    
    return {
      cached: true,
      hash: 'abc123def456',
      size: 1536,
    };
  }

  async simulateRealtimePreview() {
    // Simulate Tailwind Play CDN loading
    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
    
    return {
      loaded: true,
      configured: true,
      classes: mockPuckData.content.length,
    };
  }

  validateExtractedClasses(extracted, expected) {
    const extractedSet = new Set(extracted);
    const expectedSet = new Set(expected);
    
    // Check if most expected classes are found (allowing for some variation)
    const found = expected.filter(cls => extractedSet.has(cls));
    return found.length >= expected.length * 0.8; // 80% match threshold
  }

  recordTest(name, passed, duration, data = {}) {
    this.results.tests.push({
      name,
      passed,
      duration: Math.round(duration * 100) / 100,
      data,
      timestamp: new Date().toISOString(),
    });

    if (passed) {
      this.results.summary.passed++;
    } else {
      this.results.summary.failed++;
    }
    this.results.summary.total++;
  }

  generateReport() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`Total Tests: ${this.results.summary.total}`);
    console.log(`Passed: ${this.results.summary.passed} ✅`);
    console.log(`Failed: ${this.results.summary.failed} ❌`);
    console.log(`Success Rate: ${((this.results.summary.passed / this.results.summary.total) * 100).toFixed(1)}%`);

    console.log('\n📈 Performance Summary:');
    console.log('======================');
    this.results.tests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.duration}ms`);
    });

    // Save detailed report
    const reportPath = path.join(__dirname, '../test-results.json');
    fs.writeFile(reportPath, JSON.stringify(this.results, null, 2))
      .then(() => console.log(`\n📄 Detailed report saved to: ${reportPath}`))
      .catch(err => console.error('Failed to save report:', err));
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new TailwindIntegrationTester();
  tester.runAllTests().catch(console.error);
}

module.exports = TailwindIntegrationTester;
