#!/usr/bin/env node

/**
 * Quick test to verify imports work correctly
 */

async function testImports() {
  console.log('🧪 Testing Imports...\n');

  try {
    // Test 1: Test debounce utility
    console.log('1. Testing debounce utility...');
    const { debounce } = require('../src/utils/debounce.ts');
    
    let callCount = 0;
    const debouncedFn = debounce(() => {
      callCount++;
      console.log('   Debounced function called:', callCount);
    }, 100);
    
    // Call multiple times quickly
    debouncedFn();
    debouncedFn();
    debouncedFn();
    
    // Wait for debounce to execute
    await new Promise(resolve => setTimeout(resolve, 150));
    
    if (callCount === 1) {
      console.log('✅ Debounce utility working correctly');
    } else {
      console.log('❌ Debounce utility failed, call count:', callCount);
    }

    // Test 2: Test ThemeAPIService
    console.log('\n2. Testing ThemeAPIService...');
    const { ThemeAPIService } = require('../src/services/theme-api-service.ts');
    
    const apiService = new ThemeAPIService();
    console.log('✅ ThemeAPIService instantiated successfully');

    // Test 3: Test TailwindPlayProvider
    console.log('\n3. Testing TailwindPlayProvider...');
    const { TailwindPlayProvider } = require('../src/components/tailwind-play-provider.tsx');
    console.log('✅ TailwindPlayProvider imported successfully');

    console.log('\n🎉 All imports working correctly!');

  } catch (error) {
    console.error('❌ Import test failed:', error.message);
    console.error('Stack:', error.stack);
    
    // Provide helpful debugging info
    console.log('\n🔍 Debug Info:');
    console.log('Current working directory:', process.cwd());
    console.log('Node version:', process.version);
    
    // Check if files exist
    const fs = require('fs');
    const path = require('path');
    
    const filesToCheck = [
      'src/utils/debounce.ts',
      'src/services/theme-api-service.ts',
      'src/components/tailwind-play-provider.tsx',
    ];
    
    filesToCheck.forEach(file => {
      const fullPath = path.join(process.cwd(), file);
      console.log(`${file}: ${fs.existsSync(fullPath) ? '✅ exists' : '❌ missing'}`);
    });
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testImports().catch(console.error);
}

module.exports = { testImports };
