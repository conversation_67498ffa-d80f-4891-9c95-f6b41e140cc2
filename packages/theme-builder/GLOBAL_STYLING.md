# Global Styling System

Elementor-like global styling system for Puck Editor with TailwindCSS integration.

## 🎯 Overview

The Global Styling System provides a visual, category-based interface for applying TailwindCSS classes to components, similar to Elementor's styling panel.

### Features

- **Visual Style Categories**: Layout, Typography, Colors, Borders, Effects, Responsive
- **Component-Specific Styling**: Different style options based on component type
- **Quick Style Selectors**: Dropdown selectors for common properties
- **Custom Class Input**: Direct TailwindCSS class input with suggestions
- **Real-time Preview**: Instant visual feedback with Tailwind Play CDN
- **Style Presets**: Pre-defined style combinations for quick application

## 🏗️ Architecture

```
Global Style System
├── Style Categories (Layout, Typography, Colors, etc.)
├── Component-Specific Fields
├── Quick Style Selectors
├── Custom Class Input
└── Style Presets
```

## 🚀 Quick Start

### 1. Basic Usage

```tsx
import { 
  ENHANCED_ZMP_PUCK_CONFIG,
  GlobalStylePanel,
  createGlobalStyleField 
} from '@kit/theme-builder';

// Use enhanced config with global styling
<ThemeBuilder
  config={ENHANCED_ZMP_PUCK_CONFIG}
  data={puckData}
  onChange={handleChange}
/>
```

### 2. Custom Component with Global Styling

```tsx
import { createGlobalStyleField, createQuickStyleField } from '@kit/theme-builder';

const MyComponent = {
  fields: {
    text: { type: 'text', label: 'Text' },
    
    // Quick style selectors
    fontSize: createQuickStyleField('typography', 'fontSize', 'Font Size'),
    textColor: createQuickStyleField('colors', 'textColor', 'Text Color'),
    
    // Full global styling panel
    globalStyles: createGlobalStyleField('MyComponent', ['typography', 'colors']),
    
    // Custom classes
    customClasses: {
      type: 'text',
      label: 'Custom Classes',
      placeholder: 'hover:scale-105 transition-transform',
    },
  },
  render: ({ text, fontSize, textColor, globalStyles, customClasses }) => {
    const allClasses = [fontSize, textColor, globalStyles, customClasses]
      .filter(Boolean)
      .join(' ');
    
    return <div className={allClasses}>{text}</div>;
  },
};
```

## 📦 Components

### GlobalStylePanel

Main styling interface component.

```tsx
<GlobalStylePanel
  value="bg-blue-500 text-white p-4"
  onChange={handleChange}
  componentType="Button"
  categories={['colors', 'spacing', 'typography']}
/>
```

**Props:**
- `value`: Current class string
- `onChange`: Callback when classes change
- `componentType`: Component type for filtering relevant styles
- `categories`: Array of style categories to show

### QuickStyleSelector

Dropdown selector for specific style properties.

```tsx
<QuickStyleSelector
  value="text-lg"
  onChange={handleChange}
  category="typography"
  property="fontSize"
  label="Font Size"
/>
```

## 🎨 Style Categories

### Layout
- Display (block, flex, grid, hidden)
- Flex Direction & Alignment
- Gap & Spacing
- Grid Columns

### Typography
- Font Size (xs, sm, base, lg, xl, 2xl, 3xl, 4xl)
- Font Weight (light, normal, medium, semibold, bold)
- Text Align (left, center, right, justify)
- Line Height (tight, normal, relaxed, loose)

### Colors
- Background Colors (transparent, white, gray, blue, green, red, etc.)
- Text Colors (black, white, gray variants, brand colors)

### Borders
- Border Width (none, thin, thick)
- Border Radius (none, sm, md, lg, xl, full)
- Border Colors (gray, blue, green, red variants)

### Effects
- Shadow (none, sm, md, lg, xl, 2xl)
- Opacity (0%, 25%, 50%, 75%, 100%)
- Transform (scale, rotate)

### Responsive
- Breakpoint Prefixes (sm:, md:, lg:, xl:)

## 🔧 Helper Functions

### createGlobalStyleField()

Creates a custom field with full global styling panel.

```tsx
const globalStyleField = createGlobalStyleField(
  'Button',                    // Component type
  ['colors', 'spacing']        // Allowed categories
);
```

### createQuickStyleField()

Creates a dropdown selector for specific style property.

```tsx
const fontSizeField = createQuickStyleField(
  'typography',               // Category
  'fontSize',                 // Property
  'Font Size'                 // Label
);
```

### createEnhancedComponent()

Adds global styling to existing component.

```tsx
const EnhancedButton = createEnhancedComponent(
  BaseButton,                 // Base component
  'Button',                   // Component type
  ['colors', 'spacing']       // Allowed categories
);
```

## 🎯 Style Presets

Pre-defined style combinations for quick application.

```tsx
import { STYLE_PRESETS } from '@kit/theme-builder';

// Button presets
STYLE_PRESETS.buttons.primary    // Primary button styles
STYLE_PRESETS.buttons.secondary  // Secondary button styles
STYLE_PRESETS.buttons.outline    // Outline button styles

// Card presets
STYLE_PRESETS.cards.simple       // Simple card styles
STYLE_PRESETS.cards.elevated     // Elevated card with shadow
STYLE_PRESETS.cards.bordered     // Bordered card styles

// Container presets
STYLE_PRESETS.containers.centered // Centered container
STYLE_PRESETS.containers.fullWidth // Full width container
```

### Using Style Presets

```tsx
import { createStylePresetsField } from '@kit/theme-builder';

const MyComponent = {
  fields: {
    stylePreset: createStylePresetsField('buttons'),
    // ... other fields
  },
  render: ({ stylePreset, ...props }) => (
    <button className={stylePreset}>
      {props.text}
    </button>
  ),
};
```

## 📱 Responsive Design

The system includes responsive breakpoint controls:

```tsx
// Apply different styles at different breakpoints
const responsiveClasses = [
  'text-sm',           // Mobile (default)
  'md:text-base',      // Tablet (768px+)
  'lg:text-lg',        // Desktop (1024px+)
  'xl:text-xl',        // Large (1280px+)
].join(' ');
```

## 🎨 Custom Categories

You can extend the system with custom style categories:

```tsx
const CUSTOM_STYLE_CATEGORIES = {
  ...STYLE_CATEGORIES,
  animations: {
    title: 'Animations',
    icon: '🎬',
    components: ['All'],
    styles: {
      transition: {
        label: 'Transition',
        options: [
          { label: 'None', value: '' },
          { label: 'All', value: 'transition-all' },
          { label: 'Colors', value: 'transition-colors' },
          { label: 'Transform', value: 'transition-transform' },
        ],
      },
      duration: {
        label: 'Duration',
        options: [
          { label: '150ms', value: 'duration-150' },
          { label: '300ms', value: 'duration-300' },
          { label: '500ms', value: 'duration-500' },
          { label: '700ms', value: 'duration-700' },
        ],
      },
    },
  },
};
```

## 🔍 Debugging

### Style Inspector

Use the Style Inspector to see all applied classes:

```tsx
import { StyleInspector } from '@kit/theme-builder/examples';

<StyleInspector data={puckData} />
```

### Extract All Classes

```tsx
function extractAllClasses(data: any): string[] {
  const classes: string[] = [];
  
  const extract = (obj: any) => {
    if (!obj || typeof obj !== 'object') return;
    
    ['className', 'globalStyles', 'customClasses'].forEach(prop => {
      if (typeof obj[prop] === 'string' && obj[prop]) {
        classes.push(...obj[prop].split(' ').filter(Boolean));
      }
    });
    
    Object.values(obj).forEach(value => {
      if (Array.isArray(value)) {
        value.forEach(extract);
      } else if (typeof value === 'object') {
        extract(value);
      }
    });
  };
  
  extract(data);
  return classes;
}
```

## 🎉 Examples

See the `examples/global-styling-example.tsx` for complete implementation examples:

- Basic global styling usage
- Advanced styling scenarios
- Style inspector integration
- Custom component creation

## 🚀 Best Practices

1. **Use Component-Specific Categories**: Only show relevant style categories for each component type
2. **Combine Quick Selectors with Global Panel**: Use quick selectors for common properties, global panel for advanced styling
3. **Provide Style Presets**: Offer pre-defined style combinations for common use cases
4. **Enable Real-time Preview**: Always use with TailwindPlayProvider for instant feedback
5. **Document Custom Classes**: Provide clear placeholders and examples for custom class inputs

## 🔧 Integration with Existing Components

To add global styling to existing components:

```tsx
// 1. Add global styling fields
const fields = {
  ...existingFields,
  globalStyles: createGlobalStyleField('ComponentType'),
  customClasses: {
    type: 'text',
    label: 'Custom Classes',
  },
};

// 2. Update render function
const render = ({ globalStyles, customClasses, ...props }) => {
  const allClasses = [
    props.className,
    globalStyles,
    customClasses,
  ].filter(Boolean).join(' ');
  
  return <Component {...props} className={allClasses} />;
};
```

This global styling system provides a powerful, visual way to apply TailwindCSS classes while maintaining the flexibility and power of the underlying CSS framework.
