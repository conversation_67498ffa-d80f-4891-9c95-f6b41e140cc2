{"name": "@kit/theme-builder", "version": "0.1.0", "description": "Theme builder package for Zalo Mini App using Puck Editor and ZMP-UI", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"development": "./src/index.ts", "import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./components": {"development": "./src/components/index.ts", "import": "./dist/components/index.mjs", "require": "./dist/components/index.js", "types": "./dist/components/index.d.ts"}, "./editor": {"development": "./src/editor/index.ts", "import": "./dist/editor/index.mjs", "require": "./dist/editor/index.js", "types": "./dist/editor/index.d.ts"}, "./renderer": {"development": "./src/renderer/index.mjs", "import": "./dist/renderer/index.mjs", "require": "./dist/renderer/index.js", "types": "./dist/renderer/index.d.ts"}}, "typesVersions": {"*": {"*": ["src/*"], "components": ["src/components/index.ts"], "editor": ["src/editor/index.ts"], "renderer": ["src/renderer/index.ts"]}}, "files": ["dist", "README.md"], "scripts": {"build": "tsup && pnpm run copy:styles", "copy:styles": "mkdir -p dist/styles && cp src/styles/globals.css dist/styles/ && cp src/styles/safelist.txt dist/styles/", "dev": "pnpm run build", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["theme-builder", "puck-editor", "zmp-ui", "zalo-mini-app", "react", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"@measured/puck": "^0.19.1", "@supabase/supabase-js": "2.49.4", "clsx": "^2.1.1", "framer-motion": "^12.6.3", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwindcss": "4.1.3", "zmp-sdk": "^3.0.9", "zmp-ui": "^1.11.10"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.3", "@types/lodash": "^4.17.13", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.24.0", "postcss": "^8.4.47", "tsup": "^6.7.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/theme-builder.git"}, "bugs": {"url": "https://github.com/your-org/theme-builder/issues"}, "homepage": "https://github.com/your-org/theme-builder#readme"}