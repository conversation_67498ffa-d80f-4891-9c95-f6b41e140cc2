/**
 * Test cho API /api/products
 */
import { getAuthenticatedClient, expectStandardResponse, expectPaginationResponse } from '../../../utils/test-utils';

describe('Products API - List Products', () => {
  const api = getAuthenticatedClient();

  it('should return a list of products', async () => {
    const response = await api.get('/api/products');

    expectStandardResponse(response);
    expect(Array.isArray(response.data.data)).toBe(true);

    const products = response.data.data;
    console.log(`Retrieved ${products.length} products`);

    // Ki<PERSON>m tra cấu trúc sản phẩm có chứa thông tin flash_sale và final_price
    if (products.length > 0) {
      const product = products[0];
      expect(product).toHaveProperty('id');
      expect(product).toHaveProperty('name');
      expect(product).toHaveProperty('price');

      // Sản phẩm có thể có hoặc không có flash_sale
      if (product.flash_sale) {
        console.log(`Product ${product.name} has flash sale:`, product.flash_sale);
        expect(product.flash_sale).toHaveProperty('discount_percentage');
        expect(product.flash_sale).toHaveProperty('discounted_price');
        expect(product).toHaveProperty('final_price');
        expect(product.final_price).toBe(product.flash_sale.discounted_price);
      } else {
        console.log(`Product ${product.name} has no flash sale`);
        // Nếu không có flash sale, final_price nên bằng price
        if (product.final_price) {
          expect(product.final_price).toBe(product.price);
        }
      }
    }

    // Kiểm tra xem có sản phẩm nào không
    // Lưu ý: API products có thể không trả về sản phẩm nào do các điều kiện lọc phức tạp
    // - Lọc theo account_id từ token
    // - Yêu cầu sản phẩm phải có ít nhất một chi nhánh (inner join với branch_products)
    // - Lọc bỏ các sản phẩm không có inventory
    if (products.length === 0) {
      console.log('Không tìm thấy sản phẩm nào. Điều này có thể bình thường do các điều kiện lọc của API.');
    } else {
      console.log(`Tìm thấy ${products.length} sản phẩm. Sản phẩm đầu tiên: ${JSON.stringify(products[0])}`);
    }
  });

  it('should filter products by search term', async () => {
    // Sử dụng một search term chung chung hơn để có khả năng cao hơn tìm thấy kết quả
    const searchTerm = 'a';
    const response = await api.get(`/api/products?search=${searchTerm}`);

    expectStandardResponse(response);

    // Kiểm tra kết quả tìm kiếm
    const products = response.data.data;
    console.log(`Found ${products.length} products matching search term containing "${searchTerm}"`);
  });

  it('should support pagination parameters', async () => {
    const response = await api.get(`/api/products?page=1&limit=5`);

    expectStandardResponse(response);

    // Kiểm tra kết quả phân trang
    const products = response.data.data;
    expect(Array.isArray(products)).toBe(true);

    // Số lượng sản phẩm trả về có thể ít hơn hoặc bằng limit
    expect(products.length).toBeLessThanOrEqual(5);

    console.log(`Retrieved ${products.length} products with pagination`);
  });
});
