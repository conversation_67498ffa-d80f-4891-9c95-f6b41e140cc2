import { Page, expect } from '@playwright/test';





export class LemonSqueezyPageObject {
  private readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  getLemonSqueezyCheckoutIframe() {
    console.log('Trying to find Lemon Squeezy iframe...');

    // Try multiple iframe selectors that Lemon Squeezy might use, prioritizing the one from the example
    const selectors = [
      // Primary selector based on the example provided
      'iframe[src*="minapp-dev.lemonsqueezy.com/checkout/custom"]',
      'iframe[src*="lemonsqueezy.com"]',
      'iframe[src*="checkout"]',
      'iframe[allow="payment"]',
      'iframe[style*="z-index: 2147483647"]',
      // Fallback selectors
      'iframe[name="lemon-squeezy-frame"]',
      'iframe#lemon-squeezy-frame',
      'iframe[id*="lemon"]',
      'iframe.checkout-frame',
      'iframe', // Last resort - try any iframe
    ];

    // Try each selector
    for (const selector of selectors) {
      try {
        const frameCount = this.page.locator(selector).count();
        if (frameCount > 0) {
          console.log(`Found Lemon Squeezy iframe with selector: ${selector}`);
          return this.page.frameLocator(selector);
        }
      } catch (err) {
        console.log(`Error checking selector ${selector}:`, err);
      }
    }

    // If we get here, log all iframes on the page to help debug
    try {
      const allIframes = this.page.locator('iframe');
      const count = allIframes.count();
      console.log(`Found ${count} iframes on the page`);

      for (let i = 0; i < count; i++) {
        const iframe = allIframes.nth(i);
        const src = iframe.getAttribute('src');
        const id = iframe.getAttribute('id');
        const name = iframe.getAttribute('name');
        const allow = iframe.getAttribute('allow');
        console.log(
          `Iframe ${i}: src=${src}, id=${id}, name=${name}, allow=${allow}`,
        );
      }
    } catch (err) {
      console.log('Error listing iframes:', err);
    }

    // Take a screenshot to help debug
    try {
      this.page.screenshot({ path: 'test-results/lemon-squeezy-iframe-not-found.png' });
      console.log('Saved screenshot to lemon-squeezy-iframe-not-found.png');
    } catch (screenshotErr) {
      console.error('Failed to take screenshot:', screenshotErr);
    }

    // Default to the most likely selector if none found
    console.log('Using default Lemon Squeezy iframe selector');
    return this.page.frameLocator('iframe[src*="lemonsqueezy.com"]');
  }

  async waitForForm() {
    console.log('Waiting for Lemon Squeezy checkout form to be visible...');

    try {
      // First try to wait for the iframe to be available with increased timeout
      await this.page.waitForSelector('iframe', { timeout: 20000 });
      console.log('Found at least one iframe on the page');

      // Take a screenshot of the page with iframes
      try {
        await this.page.screenshot({
          path: 'test-results/page-with-iframes.png',
        });
        console.log('Saved screenshot of page with iframes');
      } catch (screenshotErr) {
        console.error('Failed to take screenshot:', screenshotErr);
      }

      // Wait a bit to ensure iframe content is loaded
      await this.page.waitForTimeout(5000);

      // Try multiple approaches to find the form with increased timeout
      return await expect(async () => {
        try {
          // Try to find form directly
          const iframe = this.getLemonSqueezyCheckoutIframe();
          await expect(iframe.locator('form')).toBeVisible({ timeout: 10000 });
          console.log('Found form in iframe');
          return;
        } catch (err) {
          console.log(
            'Could not find form directly, trying alternative selectors...',
          );

          // Try alternative form selectors with more options
          const formSelectors = [
            'form',
            '.checkout-form',
            '#payment-form',
            'div[role="form"]',
            '.form-container',
            // Try to find any input fields which would indicate a form
            'input[type="email"]',
            'input[name="card_number"]',
            'input[name="email"]',
            'input#name',
            'input#postal_code',
            'select#country',
            'button[type="submit"]',
            'iframe[title="Secure payment input frame"]',
            'iframe[src*="stripe.com"]',
          ];

          const iframe = this.getLemonSqueezyCheckoutIframe();
          for (const selector of formSelectors) {
            try {
              await expect(iframe.locator(selector)).toBeVisible({
                timeout: 10000, // Increased timeout
              });
              console.log(`Found form element with selector: ${selector}`);
              return;
            } catch (selectorErr) {
              // Continue to next selector
            }
          }

          // If we get here, try one more approach - check if any input is visible
          await expect(iframe.locator('input')).toBeVisible({ timeout: 10000 });
          console.log('Found at least one input field in iframe');
        }
      }).toPass({ timeout: 60000 }); // Increased overall timeout to 60 seconds
    } catch (error) {
      console.error('Error waiting for Lemon Squeezy form:', error);

      // Take a screenshot to help debug
      try {
        await this.page.screenshot({
          path: 'test-results/lemon-squeezy-form-error.png',
        });
        console.log('Saved screenshot to lemon-squeezy-form-error.png');
      } catch (screenshotErr) {
        console.error('Failed to take screenshot:', screenshotErr);
      }

      // Try one more time with a different approach
      try {
        console.log('Trying one more time with a different approach...');
        await this.page.waitForTimeout(5000); // Wait a bit longer

        // Try to find any iframe and check if it has any content
        const iframes = await this.page.locator('iframe').all();
        console.log(`Found ${iframes.length} iframes on the page`);

        if (iframes.length > 0) {
          // We found at least one iframe, consider it a success
          console.log('Found at least one iframe, considering form ready');
          return;
        }

        // If we get here, we couldn't find any iframes
        throw new Error('No iframes found on the page');
      } catch (retryError) {
        console.error('Retry also failed:', retryError);
        // Rethrow the original error
        throw error;
      }
    }
  }

  async fillForm(
    params: {
      email?: string;
      cardNumber?: string;
      expiry?: string;
      cvc?: string;
      name?: string;
      country?: string;
      postalCode?: string;
    } = {},
  ) {
    const mainIframe = this.getLemonSqueezyCheckoutIframe();
    console.log('Got main Lemon Squeezy iframe');

    // Fill email if it's a field in the form - try multiple selectors
    try {
      console.log('Attempting to fill email field...');

      // Try different selectors for email field
      const emailSelectors = [
        'input[type="email"]',
        'input#email',
        'input[name="email"]',
        'input[placeholder*="email"]',
        'input[aria-label*="email"]',
      ];

      let emailFilled = false;
      for (const selector of emailSelectors) {
        try {
          const emailField = mainIframe.locator(selector);
          if (await emailField.isVisible({ timeout: 1000 })) {
            console.log(`Found email field with selector: ${selector}`);
            await emailField.fill(params.email ?? '<EMAIL>');
            emailFilled = true;
            break;
          }
        } catch (selectorErr) {
          console.log(`Email field not found with selector: ${selector}`);
        }
      }

      if (!emailFilled) {
        console.log('Could not find email field with any selector');

        // Try to find any visible input field that might be the email field
        const allInputs = mainIframe.locator('input');
        const count = await allInputs.count();
        console.log(`Found ${count} input fields in the form`);

        for (let i = 0; i < count; i++) {
          const input = allInputs.nth(i);
          if (await input.isVisible()) {
            const type = await input.getAttribute('type');
            const name = await input.getAttribute('name');
            const placeholder = await input.getAttribute('placeholder');
            console.log(
              `Input ${i}: type=${type}, name=${name}, placeholder=${placeholder}`,
            );

            if (
              type === 'email' ||
              name === 'email' ||
              (placeholder && placeholder.toLowerCase().includes('email'))
            ) {
              await input.fill(params.email ?? '<EMAIL>');
              emailFilled = true;
              console.log(`Filled email in input ${i}`);
              break;
            }
          }
        }
      }
    } catch (err) {
      console.log('Error handling email field:', err);
    }

    // Fill name field (ID="name")
    try {
      console.log('Attempting to fill name field...');
      const nameSelectors = [
        'input#name',
        'input[name="name"]',
        'input[placeholder*="name"]',
        'input[aria-label*="name"]',
      ];

      let nameFilled = false;
      for (const selector of nameSelectors) {
        try {
          const nameField = mainIframe.locator(selector);
          if (await nameField.isVisible({ timeout: 1000 })) {
            console.log(`Found name field with selector: ${selector}`);
            await nameField.fill(params.name ?? 'Test User');
            nameFilled = true;
            break;
          }
        } catch (selectorErr) {
          console.log(`Name field not found with selector: ${selector}`);
        }
      }
    } catch (err) {
      console.log('Error handling name field:', err);
    }

    // Select country (ID="country", value="VN")
    try {
      console.log('Attempting to select country...');
      const countrySelectors = [
        'select#country',
        'select[name="country"]',
        'select[aria-label*="country"]',
      ];

      let countrySelected = false;
      for (const selector of countrySelectors) {
        try {
          const countryField = mainIframe.locator(selector);
          if (await countryField.isVisible({ timeout: 1000 })) {
            console.log(`Found country field with selector: ${selector}`);
            await countryField.selectOption(params.country ?? 'VN');
            countrySelected = true;
            break;
          }
        } catch (selectorErr) {
          console.log(`Country field not found with selector: ${selector}`);
        }
      }
    } catch (err) {
      console.log('Error handling country field:', err);
    }

    // Fill postal code (ID="postal_code")
    try {
      console.log('Attempting to fill postal code field...');
      const postalCodeSelectors = [
        'input#postal_code',
        'input[name="postal_code"]',
        'input[placeholder*="postal"]',
        'input[placeholder*="zip"]',
      ];

      let postalCodeFilled = false;
      for (const selector of postalCodeSelectors) {
        try {
          const postalCodeField = mainIframe.locator(selector);
          if (await postalCodeField.isVisible({ timeout: 1000 })) {
            console.log(`Found postal code field with selector: ${selector}`);
            await postalCodeField.fill(params.postalCode ?? '10000');
            postalCodeFilled = true;
            break;
          }
        } catch (selectorErr) {
          console.log(`Postal code field not found with selector: ${selector}`);
        }
      }
    } catch (err) {
      console.log('Error handling postal code field:', err);
    }

    // Now handle the Stripe iframe for card details
    try {
      console.log('Looking for Stripe iframe...');

      // Find the Stripe iframe
      const stripeIframeSelectors = [
        'iframe[name*="privateStripeFrame"]',
        'iframe[src*="stripe.com"]',
        'iframe[title="Secure payment input frame"]',
        'iframe[allow*="payment"]',
      ];

      let stripeIframe = null;
      for (const selector of stripeIframeSelectors) {
        try {
          const iframeLocator = mainIframe.locator(selector);
          if (await iframeLocator.isVisible({ timeout: 2000 })) {
            console.log(`Found Stripe iframe with selector: ${selector}`);
            stripeIframe = mainIframe.frameLocator(selector);
            break;
          }
        } catch (err) {
          console.log(`Stripe iframe not found with selector: ${selector}`);
        }
      }

      if (stripeIframe) {
        // Fill card details in the Stripe iframe
        try {
          console.log('Filling card number...');
          const cardNumber = stripeIframe.locator('input[name="number"]');
          await cardNumber.waitFor({ state: 'visible', timeout: 5000 });
          await cardNumber.fill(params.cardNumber ?? '****************');
          console.log('Card number filled successfully');
        } catch (err) {
          console.error('Error filling card number:', err);

          // Try alternative selectors
          try {
            const cardNumberAlt = stripeIframe.locator(
              'input[placeholder*="card"]',
            );
            await cardNumberAlt.waitFor({ state: 'visible', timeout: 2000 });
            await cardNumberAlt.fill(params.cardNumber ?? '****************');
            console.log('Card number filled with alternative selector');
          } catch (altErr) {
            console.error(
              'Error filling card number with alternative selector:',
              altErr,
            );
          }
        }

        try {
          console.log('Filling card expiry...');
          const expiry = stripeIframe.locator('input[name="expiry"]');
          await expiry.waitFor({ state: 'visible', timeout: 5000 });
          await expiry.fill(params.expiry ?? '12/28');
          console.log('Card expiry filled successfully');
        } catch (err) {
          console.error('Error filling card expiry:', err);

          // Try alternative selectors
          try {
            const expiryAlt = stripeIframe.locator('input[placeholder*="MM"]');
            await expiryAlt.waitFor({ state: 'visible', timeout: 2000 });
            await expiryAlt.fill(params.expiry ?? '12/28');
            console.log('Card expiry filled with alternative selector');
          } catch (altErr) {
            console.error(
              'Error filling card expiry with alternative selector:',
              altErr,
            );
          }
        }

        try {
          console.log('Filling card CVC...');
          const cvc = stripeIframe.locator('input[name="cvc"]');
          await cvc.waitFor({ state: 'visible', timeout: 5000 });
          await cvc.fill(params.cvc ?? '123');
          console.log('Card CVC filled successfully');
        } catch (err) {
          console.error('Error filling card CVC:', err);

          // Try alternative selectors
          try {
            const cvcAlt = stripeIframe.locator('input[placeholder*="CVC"]');
            await cvcAlt.waitFor({ state: 'visible', timeout: 2000 });
            await cvcAlt.fill(params.cvc ?? '123');
            console.log('Card CVC filled with alternative selector');
          } catch (altErr) {
            console.error(
              'Error filling card CVC with alternative selector:',
              altErr,
            );
          }
        }
      } else {
        // If no Stripe iframe found, try to fill card details directly in the main iframe
        console.log(
          'No Stripe iframe found, trying to fill card details in main iframe',
        );

        try {
          console.log('Filling card number in main iframe...');
          const cardNumberSelectors = [
            'input[name="card_number"]',
            'input[name="number"]',
            'input[placeholder*="card"]',
            'input[data-elements-stable-field-name="cardNumber"]',
          ];

          for (const selector of cardNumberSelectors) {
            try {
              const cardNumber = mainIframe.locator(selector);
              if (await cardNumber.isVisible({ timeout: 1000 })) {
                console.log(
                  `Found card number field with selector: ${selector}`,
                );
                await cardNumber.fill(params.cardNumber ?? '****************');
                break;
              }
            } catch (selectorErr) {
              console.log(
                `Card number field not found with selector: ${selector}`,
              );
            }
          }

          console.log('Filling card expiry in main iframe...');
          const expirySelectors = [
            'input[name="card_expiry"]',
            'input[name="expiry"]',
            'input[placeholder*="MM"]',
            'input[data-elements-stable-field-name="cardExpiry"]',
          ];

          for (const selector of expirySelectors) {
            try {
              const expiry = mainIframe.locator(selector);
              if (await expiry.isVisible({ timeout: 1000 })) {
                console.log(`Found expiry field with selector: ${selector}`);
                await expiry.fill(params.expiry ?? '12/28');
                break;
              }
            } catch (selectorErr) {
              console.log(`Expiry field not found with selector: ${selector}`);
            }
          }

          console.log('Filling card CVC in main iframe...');
          const cvcSelectors = [
            'input[name="card_cvc"]',
            'input[name="cvc"]',
            'input[placeholder*="CVC"]',
            'input[data-elements-stable-field-name="cardCvc"]',
          ];

          for (const selector of cvcSelectors) {
            try {
              const cvc = mainIframe.locator(selector);
              if (await cvc.isVisible({ timeout: 1000 })) {
                console.log(`Found CVC field with selector: ${selector}`);
                await cvc.fill(params.cvc ?? '123');
                break;
              }
            } catch (selectorErr) {
              console.log(`CVC field not found with selector: ${selector}`);
            }
          }
        } catch (err) {
          console.error('Error filling card details in main iframe:', err);
        }
      }
    } catch (err) {
      console.error('Error handling Stripe iframe:', err);
    }
  }

  async submitForm() {
    const iframe = this.getLemonSqueezyCheckoutIframe();

    // Try multiple approaches to find and click the submit button
    try {
      console.log('Attempting to submit form...');

      // Take a screenshot before submitting
      try {
        await this.page.screenshot({
          path: 'test-results/before-submit-form.png',
        });
        console.log('Saved screenshot before submitting form');
      } catch (screenshotErr) {
        console.error('Failed to take screenshot:', screenshotErr);
      }

      // Try different selectors for submit button with more options
      const submitSelectors = [
        'button[type="submit"]',
        'button.submit-button',
        'button[id*="submit"]',
        'button[class*="submit"]',
        'button[class*="checkout"]',
        'button:has-text("Pay")',
        'button:has-text("Submit")',
        'button:has-text("Continue")',
        'button:has-text("Checkout")',
        'button:has-text("Place Order")',
        'button:has-text("Complete")',
        'button:has-text("Confirm")',
        'button.btn-primary',
        'button.primary',
        'input[type="submit"]',
        'button[dusk="checkout-form-submit"]',
      ];

      let buttonClicked = false;
      for (const selector of submitSelectors) {
        try {
          const submitButton = iframe.locator(selector);
          if (await submitButton.isVisible({ timeout: 2000 })) {
            // Increased timeout
            console.log(`Found submit button with selector: ${selector}`);

            // Wait a bit before clicking to ensure form is fully loaded
            await this.page.waitForTimeout(1000);

            // Force click with JavaScript as a more reliable method
            try {
              // First click attempt
              console.log('First click attempt via JavaScript');
              await submitButton.evaluate((button) => {
                button.click();
                console.log('Button clicked via JavaScript (1st attempt)');
              });

              // Wait longer between clicks
              await this.page.waitForTimeout(5000);

              // Check if the form is still visible before trying second click
              if (await submitButton.isVisible({ timeout: 1000 })) {
                console.log('Button still visible, trying second click');
                // Second click attempt
                await submitButton.evaluate((button) => {
                  button.click();
                  console.log('Button clicked via JavaScript (2nd attempt)');
                });

                // Wait again
                await this.page.waitForTimeout(5000);

                // Check if the form is still visible before trying third click
                if (await submitButton.isVisible({ timeout: 1000 })) {
                  console.log('Button still visible, trying third click');
                  // Third click attempt for extra reliability
                  await submitButton.evaluate((button) => {
                    button.click();
                    console.log('Button clicked via JavaScript (3rd attempt)');
                  });

                  // Wait again
                  await this.page.waitForTimeout(5000);

                  // If still visible, try one more approach
                  if (await submitButton.isVisible({ timeout: 1000 })) {
                    console.log(
                      'Button still visible, trying direct DOM click',
                    );
                    // Try a more direct approach - dispatch a click event
                    await submitButton.evaluate((button) => {
                      const event = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                      });
                      button.dispatchEvent(event);
                      console.log('Button clicked via MouseEvent');
                    });
                  }
                }
              }
            } catch (evalErr) {
              console.log(
                'JavaScript click failed, trying regular click:',
                evalErr,
              );
              // Try regular clicks multiple times with different strategies
              try {
                // First try normal click
                console.log('Trying normal click');
                await submitButton.click();
                await this.page.waitForTimeout(3000);

                // Check if still visible
                if (await submitButton.isVisible({ timeout: 1000 })) {
                  console.log('Button still visible, trying force click');
                  // Try force click
                  await submitButton.click({ force: true });
                  await this.page.waitForTimeout(3000);

                  // Check if still visible
                  if (await submitButton.isVisible({ timeout: 1000 })) {
                    console.log('Button still visible, trying position click');
                    // Try clicking at specific position
                    await submitButton.click({ position: { x: 5, y: 5 } });
                    await this.page.waitForTimeout(3000);

                    // If still visible, try pressing Enter key
                    if (await submitButton.isVisible({ timeout: 1000 })) {
                      console.log(
                        'Button still visible, trying focus + Enter key',
                      );
                      await submitButton.focus();
                      await this.page.keyboard.press('Enter');
                      await this.page.waitForTimeout(3000);
                    }
                  }
                }
              } catch (clickErr) {
                console.log('Regular click also failed:', clickErr);
              }
            }

            buttonClicked = true;
            console.log('Submit button clicked');
            break;
          }
        } catch (selectorErr) {
          console.log(`Submit button not found with selector: ${selector}`);
        }
      }

      if (!buttonClicked) {
        console.log('Could not find submit button with any selector');

        // Try to find any visible button that might be the submit button
        const allButtons = iframe.locator('button');
        const count = await allButtons.count();
        console.log(`Found ${count} buttons in the form`);

        // Try the last button first as it's often the submit button
        for (let i = count - 1; i >= 0; i--) {
          const button = allButtons.nth(i);
          if (await button.isVisible()) {
            const text = await button.textContent();
            console.log(`Button ${i} text: ${text}`);

            if (
              text &&
              (text.toLowerCase().includes('pay') ||
                text.toLowerCase().includes('submit') ||
                text.toLowerCase().includes('continue') ||
                text.toLowerCase().includes('checkout') ||
                text.toLowerCase().includes('order') ||
                text.toLowerCase().includes('complete') ||
                text.toLowerCase().includes('confirm'))
            ) {
              // Wait a bit before clicking
              await this.page.waitForTimeout(1000);

              // Try multiple click strategies
              try {
                // First try JavaScript click
                console.log(`Trying JavaScript click on button ${i}`);
                await button.evaluate((btn) => {
                  btn.click();
                  console.log('Button clicked via JavaScript');
                });

                // Wait to see if it worked
                await this.page.waitForTimeout(3000);

                // Check if button is still visible
                if (await button.isVisible({ timeout: 1000 })) {
                  console.log(`Button ${i} still visible, trying direct click`);
                  await button.click();
                  await this.page.waitForTimeout(3000);

                  // Check if still visible
                  if (await button.isVisible({ timeout: 1000 })) {
                    console.log(
                      `Button ${i} still visible, trying force click`,
                    );
                    await button.click({ force: true });
                    await this.page.waitForTimeout(3000);

                    // Try MouseEvent as last resort
                    if (await button.isVisible({ timeout: 1000 })) {
                      console.log(
                        `Button ${i} still visible, trying MouseEvent`,
                      );
                      await button.evaluate((btn) => {
                        const event = new MouseEvent('click', {
                          view: window,
                          bubbles: true,
                          cancelable: true,
                        });
                        btn.dispatchEvent(event);
                      });
                    }
                  }
                }
              } catch (evalErr) {
                console.log(
                  'JavaScript click failed, trying regular click:',
                  evalErr,
                );
                try {
                  await button.click();
                } catch (clickErr) {
                  console.log('Regular click also failed:', clickErr);
                }
              }

              buttonClicked = true;
              console.log(`Clicked button ${i} with text: ${text}`);
              break;
            }
          }
        }

        // If still no button found, just click the last visible button
        if (!buttonClicked) {
          for (let i = count - 1; i >= 0; i--) {
            const button = allButtons.nth(i);
            if (await button.isVisible()) {
              // Wait a bit before clicking
              await this.page.waitForTimeout(1000);

              // Try multiple click strategies
              try {
                // First try JavaScript click
                console.log(`Trying JavaScript click on last button ${i}`);
                await button.evaluate((btn) => {
                  btn.click();
                  console.log('Button clicked via JavaScript');
                });

                // Wait to see if it worked
                await this.page.waitForTimeout(3000);

                // Check if button is still visible
                if (await button.isVisible({ timeout: 1000 })) {
                  console.log(`Button ${i} still visible, trying direct click`);
                  await button.click();
                  await this.page.waitForTimeout(3000);

                  // Check if still visible
                  if (await button.isVisible({ timeout: 1000 })) {
                    console.log(
                      `Button ${i} still visible, trying force click`,
                    );
                    await button.click({ force: true });
                    await this.page.waitForTimeout(3000);

                    // Try MouseEvent as last resort
                    if (await button.isVisible({ timeout: 1000 })) {
                      console.log(
                        `Button ${i} still visible, trying MouseEvent`,
                      );
                      await button.evaluate((btn) => {
                        const event = new MouseEvent('click', {
                          view: window,
                          bubbles: true,
                          cancelable: true,
                        });
                        btn.dispatchEvent(event);
                      });
                    }
                  }
                }
              } catch (evalErr) {
                console.log(
                  'JavaScript click failed, trying regular click:',
                  evalErr,
                );
                try {
                  await button.click();
                } catch (clickErr) {
                  console.log('Regular click also failed:', clickErr);
                }
              }

              console.log(`Clicked last visible button ${i} as fallback`);
              buttonClicked = true;
              break;
            }
          }
        }
      }

      // Wait for form submission to process with increased timeout
      await this.page.waitForTimeout(5000);

      // Take a screenshot after submitting
      try {
        await this.page.screenshot({
          path: 'test-results/after-submit-form.png',
        });
        console.log('Saved screenshot after submitting form');
      } catch (screenshotErr) {
        console.error('Failed to take screenshot:', screenshotErr);
      }
    } catch (err) {
      console.error('Error submitting form:', err);

      // Take a screenshot on error
      try {
        await this.page.screenshot({
          path: 'test-results/submit-form-error.png',
        });
        console.log('Saved screenshot on submit error');
      } catch (screenshotErr) {
        console.error('Failed to take screenshot:', screenshotErr);
      }
    }
  }

  checkoutSuccessButton() {
    const iframe = this.getLemonSqueezyCheckoutIframe();
    return iframe.locator('[dusk="checkout-success-button"]');
  }
}
