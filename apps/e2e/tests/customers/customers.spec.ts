import { test, expect } from '@playwright/test';
import { CustomersPageObject } from './customers.po';

// Skip tests that require authentication
const conditionalTest = test.skip;

test.describe.skip('Customers Page', () => {
  let po: CustomersPageObject;

  test.beforeEach(async ({ page }) => {
    po = new CustomersPageObject(page);
  });

  test('should display customers page', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Check if the page title is correct
    await expect(po.page.locator('h1')).toContainText('Customers');

    // Check if the search input is visible
    await expect(po.page.locator('[data-testid="customer-search-input"]')).toBeVisible();

    // Check if the export button is visible
    const hasExportButton = await po.hasExportButton();
    expect(hasExportButton).toBeTruthy();
  });

  test('should display customer table', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Check if the table is visible
    await expect(po.page.locator('table')).toBeVisible();

    // Check if the table headers are correct
    const headers = po.page.locator('table thead th');
    await expect(headers.nth(0)).toContainText('Name');
    await expect(headers.nth(1)).toContainText('Email');
    await expect(headers.nth(2)).toContainText('Phone');
    await expect(headers.nth(3)).toContainText('Orders');
  });

  test('should search for customers', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Get initial count of customers
    const initialCount = await po.getCustomersCount();

    // Search for a customer
    await po.searchCustomer('test');

    // Check if the search results are displayed
    const searchCount = await po.getCustomersCount();

    // Note: This test might need adjustment based on your test data
    // If there are no customers with 'test' in their name/email, this might be 0
    console.log(`Initial count: ${initialCount}, Search count: ${searchCount}`);
  });

  test('should navigate to customer details', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Check if there are any customers
    const customersCount = await po.getCustomersCount();

    if (customersCount > 0) {
      // View the first customer's details
      await po.viewCustomerDetails(0);

      // Check if we're on the customer details page
      await expect(po.page.url()).toMatch(/\/customers\/[^/]+$/);

      // Check if the form is visible
      await expect(po.page.locator('form')).toBeVisible();
    } else {
      console.log('No customers available to test details view');
    }
  });

  test('should handle pagination if available', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Check if pagination is visible
    const hasPagination = await po.hasPagination();

    if (hasPagination) {
      // Go to page 2
      await po.goToPage(2);

      // Check if the URL contains page=2
      await expect(po.page.url()).toContain('page=2');
    } else {
      console.log('Pagination not available, skipping test');
    }
  });

  // Note: The following tests would require creating test data first
  // They are commented out until we have a way to create test customers

  /*
  test('should create a new customer', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Get initial count of customers
    const initialCount = await po.getCustomersCount();

    // Create a new customer
    const name = `Test Customer ${Date.now()}`;
    const email = `test${Date.now()}@example.com`;
    await po.createCustomer(name, email, '1234567890', true);

    // Check if the customer count increased
    const newCount = await po.getCustomersCount();
    expect(newCount).toBe(initialCount + 1);

    // Search for the new customer
    await po.searchCustomer(name);

    // Check if the customer is in the search results
    await expect(po.page.locator('table tbody tr').first()).toContainText(name);
    await expect(po.page.locator('table tbody tr').first()).toContainText(email);
  });

  test('should update a customer', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Create a new customer first
    const name = `Test Customer ${Date.now()}`;
    const email = `test${Date.now()}@example.com`;
    await po.createCustomer(name, email, '1234567890', false);

    // Search for the new customer
    await po.searchCustomer(name);

    // View the customer details
    await po.viewCustomerDetails(0);

    // Update the customer
    const updatedName = `Updated ${name}`;
    const updatedEmail = `updated${Date.now()}@example.com`;
    await po.updateCustomer(updatedName, updatedEmail, '9876543210', true);

    // Search for the updated customer
    await po.searchCustomer(updatedName);

    // Check if the customer details were updated
    await expect(po.page.locator('table tbody tr').first()).toContainText(updatedName);
    await expect(po.page.locator('table tbody tr').first()).toContainText(updatedEmail);
  });

  test('should delete a customer', async () => {
    // Setup and navigate to customers page
    const { slug } = await po.setup();

    // Create a new customer first
    const name = `Test Customer ${Date.now()}`;
    const email = `test${Date.now()}@example.com`;
    await po.createCustomer(name, email, '1234567890', false);

    // Search for the new customer
    await po.searchCustomer(name);

    // Delete the customer
    await po.deleteCustomer(0);

    // Search for the deleted customer
    await po.searchCustomer(name);

    // Check if the customer was deleted
    const searchCount = await po.getCustomersCount();
    expect(searchCount).toBe(0);
  });
  */
});
