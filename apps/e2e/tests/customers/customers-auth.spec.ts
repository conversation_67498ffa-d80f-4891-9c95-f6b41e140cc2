import { test, expect } from '@playwright/test';

// This test requires manual setup:
// 1. Start the application
// 2. Log in manually
// 3. Navigate to the customers page
// 4. Run this test

// Skip all tests since they require manual authentication
test.describe.skip('Customers Page (Authenticated)', () => {
  test.beforeEach(async ({ page }) => {
    // Assume the user is already logged in and on the customers page
    await page.goto('http://localhost:3000/home/<USER>/customers');

    // Wait for the page to load
    await page.waitForTimeout(2000);
  });

  test('should have correct page structure', async ({ page }) => {
    // Check if the page has the correct structure
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('table')).toBeVisible();
  });

  test('should have search functionality', async ({ page }) => {
    // Check if the search input is visible
    const searchInput = await page.locator('input[placeholder*="Search"]');
    await expect(searchInput).toBeVisible();

    // Enter a search term
    await searchInput.fill('test');
    await page.keyboard.press('Enter');

    // Wait for the search results to load
    await page.waitForTimeout(500);
  });

  test('should have export button', async ({ page }) => {
    // Check if the export button is visible
    const exportButton = await page.locator('button:has-text("Export")');
    await expect(exportButton).toBeVisible();
  });

  test('should have table with correct headers', async ({ page }) => {
    // Check if the table is visible
    const table = await page.locator('table');
    await expect(table).toBeVisible();

    // Check if the table headers are correct
    const headers = page.locator('table thead th');
    await expect(headers.nth(0)).toContainText('Name');
    await expect(headers.nth(1)).toContainText('Email');
    await expect(headers.nth(2)).toContainText('Phone');
  });
});
