#!/usr/bin/env node

/**
 * Test script for Miniapp Theme API routes
 * Tests all components of the TailwindCSS + Puck Editor integration for miniapp
 */

const { performance } = require('perf_hooks');

// Mock Puck data for testing with global styling
const mockPuckData = {
  content: [
    {
      type: 'Container',
      props: {
        maxWidth: 'max-w-6xl',
        globalStyles: 'mx-auto py-12 px-4 bg-gradient-to-br from-blue-50 to-indigo-100',
        customClasses: 'min-h-screen',
      },
      content: [
        {
          type: 'Text',
          props: {
            content: 'Welcome to Miniapp Theme Builder',
            tag: 'h1',
            fontSize: 'text-4xl',
            fontWeight: 'font-bold',
            textAlign: 'text-center',
            textColor: 'text-gray-900',
            globalStyles: 'mb-8',
            customClasses: 'hover:text-blue-600 transition-colors duration-300',
          },
        },
        {
          type: 'Grid',
          props: {
            columns: 'grid-cols-1',
            gap: 'gap-8',
            globalStyles: 'md:grid-cols-2 lg:grid-cols-3 mb-12',
          },
          content: [
            {
              type: 'Card',
              props: {
                title: 'Global Styling',
                content: 'Visual styling interface with TailwindCSS integration.',
                backgroundColor: 'bg-white',
                borderRadius: 'rounded-xl',
                shadow: 'shadow-lg',
                padding: 'p-6',
                globalStyles: '',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
            {
              type: 'Card',
              props: {
                title: 'Real-time Preview',
                content: 'Instant visual feedback with Tailwind Play CDN.',
                backgroundColor: 'bg-white',
                borderRadius: 'rounded-xl',
                shadow: 'shadow-lg',
                padding: 'p-6',
                globalStyles: '',
                customClasses: 'hover:shadow-xl hover:scale-105 transition-all duration-300',
              },
            },
          ],
        },
        {
          type: 'Button',
          props: {
            text: 'Get Started',
            variant: 'primary',
            fontSize: 'text-lg',
            backgroundColor: 'bg-blue-600',
            textColor: 'text-white',
            borderRadius: 'rounded-lg',
            padding: 'px-8 py-4',
            globalStyles: 'font-semibold',
            customClasses: 'hover:bg-blue-700 hover:scale-105 active:scale-95 transition-all',
          },
        },
      ],
    },
  ],
  root: {
    props: {
      title: 'Miniapp Theme Demo',
      description: 'Global styling system for miniapp themes',
      backgroundColor: 'bg-gray-50',
      globalStyles: '',
      customClasses: '',
    },
  },
};

// Test configuration
const testConfig = {
  baseUrl: 'http://localhost:3000',
  accountId: 'test-miniapp-account-123',
  tempThemeId: null, // Will be set after first save
  performanceTargets: {
    saveConfig: 700, // ms
    getConfig: 300, // ms
    cacheHit: 50, // ms
    cssServe: 100, // ms
  },
};

class MiniappThemeAPITester {
  constructor() {
    this.results = {
      tests: [],
      performance: {},
      errors: [],
      summary: {
        passed: 0,
        failed: 0,
        total: 0,
      },
    };
  }

  async runAllTests() {
    console.log('🚀 Starting Miniapp Theme API Tests...\n');

    try {
      // Test 1: Save Config API
      await this.testSaveConfigAPI();

      // Test 2: Get Config API
      await this.testGetConfigAPI();

      // Test 3: CSS Serving API
      await this.testCSSServingAPI();

      // Test 4: Performance Benchmarks
      await this.testPerformanceBenchmarks();

      // Test 5: Global Styling Integration
      await this.testGlobalStylingIntegration();

      // Generate report
      this.generateReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      this.results.errors.push({
        test: 'Test Suite',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async testSaveConfigAPI() {
    console.log('💾 Testing Save Config API...');
    const startTime = performance.now();

    try {
      const response = await fetch(`${testConfig.baseUrl}/api/miniapp/theme/save-config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: mockPuckData,
          accountId: testConfig.accountId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      const duration = performance.now() - startTime;

      if (!result.success) {
        throw new Error(result.error || 'Save failed');
      }

      // Store temp theme ID for subsequent tests
      testConfig.tempThemeId = result.data.configId;

      const passed = duration < testConfig.performanceTargets.saveConfig;
      this.recordTest('Save Config API', passed, duration, {
        configId: result.data.configId,
        cssUrl: result.data.cssUrl,
        hash: result.data.hash,
        size: result.data.size,
      });

      console.log(`   ✅ Save API: ${duration.toFixed(2)}ms (target: ${testConfig.performanceTargets.saveConfig}ms)`);
      console.log(`   📄 Config ID: ${result.data.configId}`);
      console.log(`   🎨 CSS URL: ${result.data.cssUrl}`);
      console.log(`   📊 Size: ${result.data.size} bytes`);

    } catch (error) {
      this.recordTest('Save Config API', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ Save API failed: ${error.message}`);
    }
  }

  async testGetConfigAPI() {
    console.log('\n📥 Testing Get Config API...');
    
    if (!testConfig.tempThemeId) {
      console.log('   ⏭️ Skipping (no temp theme ID from save test)');
      return;
    }

    const startTime = performance.now();

    try {
      const url = new URL(`${testConfig.baseUrl}/api/miniapp/theme/get-config`);
      url.searchParams.set('configId', testConfig.tempThemeId);
      url.searchParams.set('accountId', testConfig.accountId);

      const response = await fetch(url.toString());

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      const duration = performance.now() - startTime;

      if (!result.success) {
        throw new Error(result.error || 'Get failed');
      }

      const passed = duration < testConfig.performanceTargets.getConfig;
      this.recordTest('Get Config API', passed, duration, {
        configLoaded: !!result.data.config,
        cssLoaded: !!result.data.cssText,
        cached: result.data.cached,
        hash: result.data.hash,
        size: result.data.size,
      });

      console.log(`   ✅ Get API: ${duration.toFixed(2)}ms (target: ${testConfig.performanceTargets.getConfig}ms)`);
      console.log(`   📄 Config loaded: ${!!result.data.config}`);
      console.log(`   🎨 CSS loaded: ${!!result.data.cssText}`);
      console.log(`   💾 Cached: ${result.data.cached}`);
      console.log(`   📊 Size: ${result.data.size} bytes`);

    } catch (error) {
      this.recordTest('Get Config API', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ Get API failed: ${error.message}`);
    }
  }

  async testCSSServingAPI() {
    console.log('\n🎨 Testing CSS Serving API...');
    
    if (!testConfig.tempThemeId) {
      console.log('   ⏭️ Skipping (no temp theme ID from save test)');
      return;
    }

    const startTime = performance.now();

    try {
      const cssUrl = `${testConfig.baseUrl}/api/miniapp/theme/css/${testConfig.tempThemeId}.css`;
      const response = await fetch(cssUrl);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const cssText = await response.text();
      const duration = performance.now() - startTime;

      const passed = duration < testConfig.performanceTargets.cssServe && cssText.length > 0;
      this.recordTest('CSS Serving API', passed, duration, {
        cssSize: cssText.length,
        contentType: response.headers.get('content-type'),
        cacheControl: response.headers.get('cache-control'),
        etag: response.headers.get('etag'),
      });

      console.log(`   ✅ CSS API: ${duration.toFixed(2)}ms (target: ${testConfig.performanceTargets.cssServe}ms)`);
      console.log(`   📊 CSS size: ${cssText.length} bytes`);
      console.log(`   📋 Content-Type: ${response.headers.get('content-type')}`);
      console.log(`   💾 Cache-Control: ${response.headers.get('cache-control')}`);

    } catch (error) {
      this.recordTest('CSS Serving API', false, performance.now() - startTime, { error: error.message });
      console.log(`   ❌ CSS API failed: ${error.message}`);
    }
  }

  async testPerformanceBenchmarks() {
    console.log('\n⚡ Testing Performance Benchmarks...');

    // Test multiple saves to check caching
    const iterations = 3;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      
      try {
        const response = await fetch(`${testConfig.baseUrl}/api/miniapp/theme/save-config`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            config: mockPuckData,
            accountId: testConfig.accountId,
            tempThemeId: testConfig.tempThemeId,
          }),
        });

        if (response.ok) {
          const duration = performance.now() - startTime;
          times.push(duration);
          console.log(`   📊 Iteration ${i + 1}: ${duration.toFixed(2)}ms`);
        }
      } catch (error) {
        console.log(`   ❌ Iteration ${i + 1} failed: ${error.message}`);
      }
    }

    if (times.length > 0) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);

      console.log(`   📈 Average: ${avgTime.toFixed(2)}ms`);
      console.log(`   ⚡ Fastest: ${minTime.toFixed(2)}ms`);
      console.log(`   🐌 Slowest: ${maxTime.toFixed(2)}ms`);

      const passed = avgTime < testConfig.performanceTargets.saveConfig;
      this.recordTest('Performance Benchmarks', passed, avgTime, {
        iterations,
        average: avgTime,
        min: minTime,
        max: maxTime,
      });
    }
  }

  async testGlobalStylingIntegration() {
    console.log('\n🎨 Testing Global Styling Integration...');

    // Extract classes from mock data
    const extractedClasses = this.extractClassesFromData(mockPuckData);
    
    console.log(`   📋 Extracted ${extractedClasses.length} classes:`);
    extractedClasses.slice(0, 10).forEach(cls => {
      console.log(`      • ${cls}`);
    });
    
    if (extractedClasses.length > 10) {
      console.log(`      ... and ${extractedClasses.length - 10} more`);
    }

    // Check for global styling patterns
    const globalStyleClasses = extractedClasses.filter(cls => 
      cls.includes('hover:') || cls.includes('transition') || cls.includes('duration')
    );

    const responsiveClasses = extractedClasses.filter(cls => 
      cls.includes('md:') || cls.includes('lg:') || cls.includes('xl:')
    );

    console.log(`   ✨ Interactive classes: ${globalStyleClasses.length}`);
    console.log(`   📱 Responsive classes: ${responsiveClasses.length}`);

    const passed = extractedClasses.length > 0 && globalStyleClasses.length > 0;
    this.recordTest('Global Styling Integration', passed, 0, {
      totalClasses: extractedClasses.length,
      interactiveClasses: globalStyleClasses.length,
      responsiveClasses: responsiveClasses.length,
      sampleClasses: extractedClasses.slice(0, 5),
    });

    if (passed) {
      console.log(`   ✅ Global styling integration working`);
    } else {
      console.log(`   ❌ Global styling integration failed`);
    }
  }

  extractClassesFromData(data) {
    const classes = new Set();

    const extract = (obj) => {
      if (!obj || typeof obj !== 'object') return;

      ['className', 'globalStyles', 'customClasses', 'fontSize', 'fontWeight', 
       'textAlign', 'textColor', 'backgroundColor', 'borderRadius', 'padding', 
       'margin', 'shadow', 'gap'].forEach(prop => {
        if (typeof obj[prop] === 'string' && obj[prop]) {
          obj[prop].split(' ').forEach(cls => {
            if (cls.trim()) classes.add(cls.trim());
          });
        }
      });

      Object.values(obj).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(extract);
        } else if (typeof value === 'object') {
          extract(value);
        }
      });
    };

    extract(data);
    return Array.from(classes);
  }

  recordTest(name, passed, duration, data = {}) {
    this.results.tests.push({
      name,
      passed,
      duration: Math.round(duration * 100) / 100,
      data,
      timestamp: new Date().toISOString(),
    });

    if (passed) {
      this.results.summary.passed++;
    } else {
      this.results.summary.failed++;
    }
    this.results.summary.total++;
  }

  generateReport() {
    console.log('\n📊 Miniapp Theme API Test Results:');
    console.log('=====================================');
    console.log(`Total Tests: ${this.results.summary.total}`);
    console.log(`Passed: ${this.results.summary.passed} ✅`);
    console.log(`Failed: ${this.results.summary.failed} ❌`);
    console.log(`Success Rate: ${((this.results.summary.passed / this.results.summary.total) * 100).toFixed(1)}%`);

    console.log('\n📈 Performance Summary:');
    console.log('======================');
    this.results.tests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.duration}ms`);
    });

    if (this.results.summary.failed === 0) {
      console.log('\n🎉 All tests passed! Miniapp Theme API is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Check the logs above for details.');
    }
  }
}

// Polyfill fetch for Node.js if needed
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new MiniappThemeAPITester();
  tester.runAllTests().catch(console.error);
}

module.exports = MiniappThemeAPITester;
