#!/usr/bin/env node

/**
 * Simple test script to debug TailwindCSS service
 */

const path = require('path');

async function testTailwindService() {
  console.log('🧪 Testing TailwindCSS Service...\n');

  try {
    // Test 1: Check if we can import the service
    console.log('1. Testing import...');
    const { tailwindCSSService } = require('../lib/tailwind-css-service');
    console.log('✅ Import successful');

    // Test 2: Test class extraction
    console.log('\n2. Testing class extraction...');
    const mockData = {
      content: [
        {
          type: 'ZMPButton',
          props: {
            className: 'bg-blue-500 text-white p-4',
            customClasses: 'hover:bg-blue-600',
          },
        },
      ],
    };

    const classes = tailwindCSSService.extractClassesFromPuckData(mockData);
    console.log('✅ Extracted classes:', classes);

    // Test 3: Test CSS generation (simple)
    console.log('\n3. Testing CSS generation...');
    const simpleClasses = ['bg-blue-500', 'text-white', 'p-4'];
    
    try {
      const result = await tailwindCSSService.generateCSS(simpleClasses);
      console.log('✅ CSS generation successful');
      console.log('   CSS size:', result.size, 'bytes');
      console.log('   Hash:', result.hash);
      console.log('   Classes:', result.classes.length);
    } catch (cssError) {
      console.log('❌ CSS generation failed:', cssError.message);
      
      // Check if TailwindCSS is available
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);
      
      try {
        await execAsync('npx tailwindcss --help');
        console.log('✅ TailwindCSS CLI is available');
      } catch (cliError) {
        console.log('❌ TailwindCSS CLI not available:', cliError.message);
        console.log('💡 Try running: npm install -g tailwindcss');
      }
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    
    // Debug info
    console.log('\n🔍 Debug Info:');
    console.log('Current working directory:', process.cwd());
    console.log('Node version:', process.version);
    console.log('Platform:', process.platform);
    
    // Check if required files exist
    const fs = require('fs');
    const configPath = path.join(process.cwd(), 'packages', 'theme-builder', 'tailwind.cli.config.js');
    console.log('Config file exists:', fs.existsSync(configPath));
    console.log('Config path:', configPath);
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testTailwindService().catch(console.error);
}

module.exports = { testTailwindService };
