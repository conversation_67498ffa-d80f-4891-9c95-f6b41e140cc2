#!/usr/bin/env node

/**
 * Test API routes for TailwindCSS integration
 */

async function testAPIRoutes() {
  console.log('🧪 Testing API Routes...\n');

  const baseUrl = 'http://localhost:3000';
  
  // Test data
  const testConfig = {
    content: [
      {
        type: 'ZMPButton',
        props: {
          className: 'bg-blue-500 text-white p-4 rounded',
          customClasses: 'hover:bg-blue-600',
        },
      },
    ],
    root: {
      props: {
        title: 'Test Theme',
      },
    },
  };

  try {
    // Test 1: Save Config API
    console.log('1. Testing save-config API...');
    
    const saveResponse = await fetch(`${baseUrl}/api/theme-builder/save-config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        config: testConfig,
        accountId: 'test-account-123',
      }),
    });

    if (!saveResponse.ok) {
      throw new Error(`Save API failed: ${saveResponse.status} ${saveResponse.statusText}`);
    }

    const saveData = await saveResponse.json();
    console.log('✅ Save API successful');
    console.log('   Config ID:', saveData.data?.configId);
    console.log('   CSS URL:', saveData.data?.cssUrl);
    console.log('   Hash:', saveData.data?.hash);

    // Test 2: Get Config API
    if (saveData.data?.configId) {
      console.log('\n2. Testing get-config API...');
      
      const getResponse = await fetch(
        `${baseUrl}/api/theme-builder/get-config?configId=${saveData.data.configId}`
      );

      if (!getResponse.ok) {
        throw new Error(`Get API failed: ${getResponse.status} ${getResponse.statusText}`);
      }

      const getData = await getResponse.json();
      console.log('✅ Get API successful');
      console.log('   Config loaded:', !!getData.data?.config);
      console.log('   CSS loaded:', !!getData.data?.cssText);
      console.log('   Cached:', getData.data?.cached);

      // Test 3: CSS Endpoint
      if (saveData.data?.cssUrl) {
        console.log('\n3. Testing CSS endpoint...');
        
        const cssResponse = await fetch(`${baseUrl}${saveData.data.cssUrl}`);
        
        if (!cssResponse.ok) {
          throw new Error(`CSS endpoint failed: ${cssResponse.status} ${cssResponse.statusText}`);
        }

        const cssText = await cssResponse.text();
        console.log('✅ CSS endpoint successful');
        console.log('   CSS size:', cssText.length, 'bytes');
        console.log('   Contains styles:', cssText.includes('.bg-blue-500'));
      }
    }

    console.log('\n🎉 All API tests passed!');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    
    // Check if server is running
    try {
      const healthResponse = await fetch(`${baseUrl}/api/health`);
      if (healthResponse.ok) {
        console.log('✅ Server is running');
      } else {
        console.log('⚠️ Server health check failed');
      }
    } catch (healthError) {
      console.log('❌ Server is not running or not accessible');
      console.log('💡 Make sure to start the development server: npm run dev');
    }
  }
}

// Polyfill fetch for Node.js if needed
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

// Run test if this script is executed directly
if (require.main === module) {
  testAPIRoutes().catch(console.error);
}

module.exports = { testAPIRoutes };
