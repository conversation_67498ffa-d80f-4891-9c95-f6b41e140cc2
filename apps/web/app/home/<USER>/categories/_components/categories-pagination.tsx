'use client';

import { Pagination } from '@kit/ui/pagination';

interface CategoriesPaginationProps {
  currentPage: number;
  limit: number;
  total: number;
}

export function CategoriesPagination({
  currentPage,
  limit,
  total,
}: CategoriesPaginationProps) {
  return (
    <Pagination
      currentPage={currentPage}
      pageSize={limit}
      total={total}
      showItemsCounter
      showFirstLastButtons
      className="bg-white"
    />
  );
}
