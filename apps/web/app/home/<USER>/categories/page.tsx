import { Suspense } from 'react';

import { Plus } from 'lucide-react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import { CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { PageBody } from '@kit/ui/page';
import { SearchListInput } from '@kit/ui/search-list-input';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';
import CategoriesList from './_components/categories-list';
import CreateCategoryModal from './_components/create-category-modal';
import { loadCategories } from './_lib/server/categories-page.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('categories:pageTitle');

  return {
    title,
  };
};

interface PageSearchParams {
  page?: string;
  query?: string;
}

async function CategoriesPage({
  params,
  searchParams,
}: {
  params: Promise<{ account: string }>;
  searchParams: Promise<PageSearchParams>;
}) {
  const client = getSupabaseServerClient();
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);

  const accountSlug = resolvedParams.account;
  const currentPage = resolvedSearchParams.page
    ? parseInt(resolvedSearchParams.page)
    : 1;
  const searchQuery = resolvedSearchParams.query || '';

  const api = createTeamAccountsApi(client);

  try {
    const teamAccount = await api.getTeamAccount(accountSlug);

    if (!teamAccount) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const [{ categories, total }, { user, account }] = await Promise.all([
      loadCategories(client, teamAccount.id, currentPage, searchQuery),
      loadTeamWorkspace(accountSlug),
    ]);

    if (!account) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const canManageCategories =
      account?.permissions?.includes('categories.manage') ?? false;
    const limit = 10;
    const offset = (currentPage - 1) * limit;

    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="common:routes:categories" />}
          description={<AppBreadcrumbs />}
          account={account.slug}
        />

        <PageBody>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>
              <SearchListInput
                defaultValue={searchQuery}
                placeholder={
                  <Trans i18nKey="categories:search:placeholder">
                    Search categories...
                  </Trans>
                }
                data-testid="categories-page-search"
              />
            </CardTitle>

            <If condition={canManageCategories}>
              <CreateCategoryModal>
                <Button data-testid="categories-page-add-button">
                  <Plus className="mr-2 h-4 w-4" />
                  <Trans i18nKey="categories:createNew">Add Category</Trans>
                </Button>
              </CreateCategoryModal>
            </If>
          </CardHeader>

          <CardContent>
            <Suspense fallback={<div>Loading...</div>}>
              <CategoriesList
                categories={categories}
                canManage={canManageCategories}
                account={account}
                currentPage={currentPage - 1}
                limit={limit}
                total={total}
                filters={{
                  query: searchQuery,
                }}
              />
            </Suspense>
          </CardContent>
        </PageBody>
      </>
    );
  } catch (error: any) {
    console.error('Error loading categories page:', {
      error: error.message,
      params,
      context: 'categories.page',
    });

    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:loadingFailed">
              Failed to load data. Please try again later.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}

export default withI18n(CategoriesPage);
