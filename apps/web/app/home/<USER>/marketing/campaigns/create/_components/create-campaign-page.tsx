'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { Mail, MessageSquare, Phone, Send, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface CreateCampaignPageProps {
  account: any;
  accountSlug: string;
  segments: any[];
  preSelectedSegment?: any;
  preSelectedType?: string;
}

export function CreateCampaignPage({
  account,
  accountSlug,
  segments,
  preSelectedSegment,
  preSelectedType,
}: CreateCampaignPageProps) {
  const { t } = useTranslation(['marketing', 'common']);
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: preSelectedType || (preSelectedSegment ? 'email' : ''),
    segmentId: preSelectedSegment?.id || '',
    subject: '',
    content: '',
    scheduledAt: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Simulate campaign creation
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success(t('marketing:campaigns.create.messages.success'));
      router.push(`/home/<USER>/marketing/campaigns`);
    } catch (error) {
      toast.error(t('marketing:campaigns.create.messages.error'));
    } finally {
      setIsLoading(false);
    }
  };

  const selectedSegment = segments.find((s) => s.id === formData.segmentId);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="ms-5 mb-8">
        <h1 className="mb-2 text-3xl font-bold">
          {t('marketing:campaigns.create.title')}
        </h1>
        <p className="text-muted-foreground">
          {t('marketing:campaigns.create.description')}
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Campaign Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>
                {t('marketing:campaigns.create.form.campaignDetails')}
              </CardTitle>
              <CardDescription>
                {t(
                  'marketing:campaigns.create.form.campaignDetailsDescription',
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Campaign Name */}
                <div className="space-y-2">
                  <Label htmlFor="name">
                    {t('marketing:campaigns.create.form.campaignName')}
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    placeholder={t(
                      'marketing:campaigns.create.form.campaignNamePlaceholder',
                    )}
                    required
                  />
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">
                    {t('marketing:campaigns.create.form.description')}
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    placeholder={t(
                      'marketing:campaigns.create.form.descriptionPlaceholder',
                    )}
                    rows={3}
                  />
                </div>

                {/* Campaign Type */}
                <div className="space-y-2">
                  <Label htmlFor="type">
                    {t('marketing:campaigns.create.form.campaignType')}
                  </Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) =>
                      setFormData({ ...formData, type: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'marketing:campaigns.create.form.selectCampaignType',
                        )}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {t('marketing:campaigns.create.types.email')}
                        </div>
                      </SelectItem>
                      <SelectItem value="sms">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          {t('marketing:campaigns.create.types.sms')}
                        </div>
                      </SelectItem>
                      <SelectItem value="phone">
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          {t('marketing:campaigns.create.types.phone')}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Target Segment */}
                <div className="space-y-2">
                  <Label htmlFor="segment">
                    {t('marketing:campaigns.create.form.targetSegment')}
                  </Label>
                  <Select
                    value={formData.segmentId}
                    onValueChange={(value) =>
                      setFormData({ ...formData, segmentId: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'marketing:campaigns.create.form.selectTargetSegment',
                        )}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {segments.map((segment) => (
                        <SelectItem key={segment.id} value={segment.id}>
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4" />
                            {segment.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Email/SMS Subject */}
                {(formData.type === 'email' || formData.type === 'sms') && (
                  <div className="space-y-2">
                    <Label htmlFor="subject">
                      {formData.type === 'email'
                        ? t('marketing:campaigns.create.form.emailSubject')
                        : t('marketing:campaigns.create.form.smsTitle')}
                    </Label>
                    <Input
                      id="subject"
                      value={formData.subject}
                      onChange={(e) =>
                        setFormData({ ...formData, subject: e.target.value })
                      }
                      placeholder={t(
                        'marketing:campaigns.create.form.subjectPlaceholder',
                        { type: formData.type },
                      )}
                      required
                    />
                  </div>
                )}

                {/* Content */}
                <div className="space-y-2">
                  <Label htmlFor="content">
                    {formData.type === 'email'
                      ? t('marketing:campaigns.create.form.emailContent')
                      : formData.type === 'sms'
                        ? t('marketing:campaigns.create.form.smsMessage')
                        : t('marketing:campaigns.create.form.callScript')}
                  </Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) =>
                      setFormData({ ...formData, content: e.target.value })
                    }
                    placeholder={t(
                      'marketing:campaigns.create.form.contentPlaceholder',
                      { type: formData.type },
                    )}
                    rows={8}
                    required
                  />
                </div>

                {/* Schedule */}
                <div className="space-y-2">
                  <Label htmlFor="scheduledAt">
                    {t('marketing:campaigns.create.form.schedule')}
                  </Label>
                  <Input
                    id="scheduledAt"
                    type="datetime-local"
                    value={formData.scheduledAt}
                    onChange={(e) =>
                      setFormData({ ...formData, scheduledAt: e.target.value })
                    }
                  />
                  <p className="text-muted-foreground text-sm">
                    {t('marketing:campaigns.create.form.scheduleDescription')}
                  </p>
                </div>

                {/* Submit Button */}
                <div className="flex gap-3">
                  <Button type="submit" disabled={isLoading}>
                    <Send className="mr-2 h-4 w-4" />
                    {isLoading
                      ? t('marketing:campaigns.create.form.creating')
                      : t('marketing:campaigns.create.form.createCampaign')}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                  >
                    {t('marketing:campaigns.create.form.cancel')}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Campaign Preview */}
        <div className="space-y-6">
          {/* Selected Segment Info */}
          {selectedSegment && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  {t('marketing:campaigns.create.preview.targetSegment')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium">{selectedSegment.name}</h3>
                  <p className="text-muted-foreground text-sm">
                    {selectedSegment.description}
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="capitalize">
                    {selectedSegment.type.replace('_', ' ')}
                  </Badge>
                  {selectedSegment.is_auto_updating && (
                    <Badge variant="secondary">
                      {t('marketing:campaigns.create.preview.autoUpdating')}
                    </Badge>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">
                      {t('marketing:campaigns.create.preview.customers')}
                    </p>
                    <p className="font-medium">
                      {selectedSegment.customer_count.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">
                      {t('marketing:campaigns.create.preview.growthRate')}
                    </p>
                    <p className="font-medium">
                      {selectedSegment.growth_rate > 0 ? '+' : ''}
                      {selectedSegment.growth_rate.toFixed(1)}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Campaign Preview */}
          {formData.type && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {formData.type === 'email' && <Mail className="h-5 w-5" />}
                  {formData.type === 'sms' && (
                    <MessageSquare className="h-5 w-5" />
                  )}
                  {formData.type === 'phone' && <Phone className="h-5 w-5" />}
                  {t('marketing:campaigns.create.preview.campaignPreview')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.subject && (
                  <div>
                    <p className="text-muted-foreground text-sm">
                      {t('marketing:campaigns.create.preview.subject')}
                    </p>
                    <p className="font-medium">{formData.subject}</p>
                  </div>
                )}

                {formData.content && (
                  <div>
                    <p className="text-muted-foreground text-sm">
                      {t('marketing:campaigns.create.preview.content')}
                    </p>
                    <div className="bg-muted/50 rounded-lg p-3 text-sm">
                      {formData.content}
                    </div>
                  </div>
                )}

                {formData.scheduledAt && (
                  <div>
                    <p className="text-muted-foreground text-sm">
                      {t('marketing:campaigns.create.preview.scheduledFor')}
                    </p>
                    <p className="font-medium">
                      {new Date(formData.scheduledAt).toLocaleString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Campaign Tips */}
          <Card>
            <CardHeader>
              <CardTitle>
                {t('marketing:campaigns.create.tips.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex items-start gap-2">
                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                <p>{t('marketing:campaigns.create.tips.personalize')}</p>
              </div>
              <div className="flex items-start gap-2">
                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                <p>{t('marketing:campaigns.create.tips.testFirst')}</p>
              </div>
              <div className="flex items-start gap-2">
                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                <p>{t('marketing:campaigns.create.tips.monitor')}</p>
              </div>
              <div className="flex items-start gap-2">
                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                <p>{t('marketing:campaigns.create.tips.followUp')}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
