'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { OrderFormData } from '../order.schema';

interface UpdateOrderParams extends OrderFormData {
  id: string;
  accountId: string;
  accountSlug: string;
}

export async function updateOrder({
  id,
  customer_id,
  product_id,
  branch_id,
  quantity,
  total_amount,
  status,
  payment_method,
  accountId,
  accountSlug,
}: UpdateOrderParams) {
  const client = getSupabaseServerClient();

  // Lấy trạng thái hiện tại của đơn hàng
  const { data: currentOrder, error: getError } = await client
    .from('customer_orders')
    .select('status')
    .eq('id', id)
    .eq('account_id', accountId)
    .single();

  if (getError) {
    throw getError;
  }

  // Cập nhật thông tin cơ bản của đơn hàng
  const { error } = await client
    .from('customer_orders')
    .update({
      customer_id,
      product_id,
      branch_id,
      quantity,
      total_amount,
      payment_method,
    })
    .eq('id', id)
    .eq('account_id', accountId);

  if (error) {
    throw error;
  }

  // Nếu trạng thái thay đổi, cập nhật trạng thái đơn hàng
  if (status !== currentOrder.status) {
    const { error: statusError } = await client
      .from('customer_orders')
      .update({ status })
      .eq('id', id)
      .eq('account_id', accountId);

    if (statusError) {
      throw statusError;
    }
  }

  revalidatePath(`/home/<USER>/orders`);
}
