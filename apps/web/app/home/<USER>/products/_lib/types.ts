import { z } from 'zod';

export interface AttributeValue {
  [key: string]: string; // e.g., { "Size": "XL" } or { "Color": "Red" }
}

export interface AttributeCombination {
  attribute_values: AttributeValue;
  quantity: number;
}

export interface InventoryFormData {
  branch_id: string;
  // For products without attributes
  base_quantity?: number;
  // For products with attributes
  attribute_combinations?: AttributeCombination[];
}

export const productSchema = z.object({
  // Basic info
  name: z.string().min(1, 'products:validation:name_required'),
  type: z.enum(['physical', 'digital', 'service']),
  description: z.string().optional(),
  price: z.number().min(0, 'products:validation:price_positive'),
  compare_at_price: z.number().min(0, 'products:validation:price_positive'),
  status: z.enum(['active', 'inactive', 'draft']).default('draft'),
  category_id: z.string().uuid('products:validation:category_invalid'),

  // Identification
  sku: z.string().optional().nullable(),
  barcode: z.string().optional().nullable(),

  // Physical product fields
  weight: z.number().min(0).optional().nullable(),
  dimensions: z
    .object({
      length: z.number().min(0),
      width: z.number().min(0),
      height: z.number().min(0),
    })
    .optional()
    .nullable(),

  // Tax and pricing
  tax_rate: z.number().min(0).max(100).optional().nullable(),
  cost_per_item: z.number().min(0).optional().nullable(),

  // Media
  image_url: z.string().optional(),
  image_urls: z.array(z.string()).optional(),
  images: z
    .array(
      z.object({
        url: z.string(),
        tempPath: z.string().optional(),
      }),
    )
    .optional()
    .default([]),

  // Metadata
  vendor: z.string().optional(),
  categories: z.array(z.string()).optional(),
  collections: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  seo: z
    .object({
      title: z.string().optional(),
      description: z.string().optional(),
      keywords: z.string().optional(), // Added keywords field
    })
    .optional(),
  metadata: z.record(z.unknown()).optional(),

  // Distribution
  is_global: z.boolean().default(true), // Added for branch section
  selected_branches: z.array(z.string()).optional(),

  // Inventory
  track_inventory: z.boolean().default(false),
  inventory_quantity: z.number().min(0).optional(),
  inventory: z
    .array(
      z.object({
        branch_id: z.string().uuid(),
        base_quantity: z.number().min(0).optional(),
        attribute_combinations: z
          .array(
            z.object({
              attribute_values: z.record(z.string()),
              quantity: z.number().min(0),
            }),
          )
          .optional(),
      }),
    )
    .optional(),

  // Attributes
  attributes: z
    .array(
      z.object({
        name: z.string().min(1, 'products:validation:attribute_name_required'),
        values: z.array(
          z.string().min(1, 'products:validation:attribute_value_required'),
        ),
        hasPriceModifier: z.boolean().default(false),
        priceModifiers: z.array(z.number()).default([]),
      }),
    )
    .optional(),
});

export type ProductFormData = z.infer<typeof productSchema>;
