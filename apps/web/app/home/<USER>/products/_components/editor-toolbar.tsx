// app/home/<USER>/products/new/_components/editor-toolbar.tsx
'use client';

import {
  AlignCenter,
  AlignLeft,
  AlignRight,
  Bold,
  Image as ImageIcon,
  Italic,
  List,
  ListOrdered,
  Redo,
  Undo,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Separator } from '@kit/ui/separator';
import { Toggle } from '@kit/ui/toggle';

// app/home/<USER>/products/new/_components/editor-toolbar.tsx

interface EditorToolbarProps {
  editor: any;
  onImageSelect: (file: File) => Promise<string | null>; // Callback để chèn ảnh
}

export function EditorToolbar({ editor, onImageSelect }: EditorToolbarProps) {
  if (!editor) return null;

  // Hàm xử lý khi người dùng chọn ảnh
  const handleImageSelect = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (event: any) => {
      const file = event.target.files[0];
      if (file) {
        const url = await onImageSelect(file);
        if (url) {
          editor.chain().focus().setImage({ src: url }).run();
        }
      }
    };
    input.click();
  };

  return (
    <div className="flex flex-wrap items-center gap-1 p-2">
      {/* Định dạng văn bản */}
      <div className="flex items-center gap-1">
        <Toggle
          size="sm"
          pressed={editor.isActive('bold')}
          onPressedChange={() => editor.chain().focus().toggleBold().run()}
          className="h-8 w-8 p-0"
        >
          <Bold className="h-4 w-4" />
        </Toggle>

        <Toggle
          size="sm"
          pressed={editor.isActive('italic')}
          onPressedChange={() => editor.chain().focus().toggleItalic().run()}
          className="h-8 w-8 p-0"
        >
          <Italic className="h-4 w-4" />
        </Toggle>

        <Separator orientation="vertical" className="mx-1 h-6" />
      </div>

      {/* Heading */}
      <div className="flex items-center gap-1">
        <Toggle
          size="sm"
          pressed={editor.isActive('heading', { level: 2 })}
          onPressedChange={() =>
            editor.chain().focus().toggleHeading({ level: 2 }).run()
          }
          className="h-8 px-2 text-sm font-semibold"
        >
          H2
        </Toggle>

        <Toggle
          size="sm"
          pressed={editor.isActive('heading', { level: 3 })}
          onPressedChange={() =>
            editor.chain().focus().toggleHeading({ level: 3 }).run()
          }
          className="h-8 px-2 text-sm font-semibold"
        >
          H3
        </Toggle>

        <Separator orientation="vertical" className="mx-1 h-6" />
      </div>

      {/* Danh sách */}
      <div className="flex items-center gap-1">
        <Toggle
          size="sm"
          pressed={editor.isActive('bulletList')}
          onPressedChange={() =>
            editor.chain().focus().toggleBulletList().run()
          }
          className="h-8 w-8 p-0"
        >
          <List className="h-4 w-4" />
        </Toggle>

        <Toggle
          size="sm"
          pressed={editor.isActive('orderedList')}
          onPressedChange={() =>
            editor.chain().focus().toggleOrderedList().run()
          }
          className="h-8 w-8 p-0"
        >
          <ListOrdered className="h-4 w-4" />
        </Toggle>

        <Separator orientation="vertical" className="mx-1 h-6" />
      </div>

      {/* Căn chỉnh */}
      <div className="flex items-center gap-1">
        <Toggle
          size="sm"
          pressed={
            editor.isActive('paragraph', { textAlign: 'left' }) ||
            editor.isActive('heading', { textAlign: 'left' })
          }
          onPressedChange={() =>
            editor.chain().focus().setTextAlign('left').run()
          }
          className="h-8 w-8 p-0"
        >
          <AlignLeft className="h-4 w-4" />
        </Toggle>

        <Toggle
          size="sm"
          pressed={
            editor.isActive('paragraph', { textAlign: 'center' }) ||
            editor.isActive('heading', { textAlign: 'center' })
          }
          onPressedChange={() =>
            editor.chain().focus().setTextAlign('center').run()
          }
          className="h-8 w-8 p-0"
        >
          <AlignCenter className="h-4 w-4" />
        </Toggle>

        <Toggle
          size="sm"
          pressed={
            editor.isActive('paragraph', { textAlign: 'right' }) ||
            editor.isActive('heading', { textAlign: 'right' })
          }
          onPressedChange={() =>
            editor.chain().focus().setTextAlign('right').run()
          }
          className="h-8 w-8 p-0"
        >
          <AlignRight className="h-4 w-4" />
        </Toggle>

        <Separator orientation="vertical" className="mx-1 h-6" />
      </div>

      {/* Chèn ảnh */}
      <div className="flex items-center gap-1">
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={handleImageSelect}
        >
          <ImageIcon className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-6" />
      </div>

      {/* Undo/Redo */}
      <div className="flex items-center gap-1">
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => editor.chain().focus().undo().run()}
        >
          <Undo className="h-4 w-4" />
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => editor.chain().focus().redo().run()}
        >
          <Redo className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
