'use client';

import { useState } from 'react';
import { useTransition } from 'react';

import { toast } from 'sonner';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Trans } from '@kit/ui/trans';

import { deleteProduct } from '~/home/<USER>/products/_lib/server/delete-product';

import type { Product } from '../_lib/server/products-page.loader';

interface DeleteProductButtonProps {
  product: Product;
  account: { id: string; slug: string };

  children: React.ReactNode;
}

export default function DeleteProductButton({
  product,
  account,
  children,
}: DeleteProductButtonProps) {
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();

  const onDelete = () => {
    startTransition(async () => {
      try {
        await deleteProduct({
          id: product.id,
          accountId: account.id,
          accountSlug: account.slug,
        });

        toast.success(
          <Trans i18nKey="products:delete:success">
            Product deleted successfully
          </Trans>,
        );

        setOpen(false);
      } catch (error) {
        toast.error(
          <Trans i18nKey="products:delete:error">
            Failed to delete product
          </Trans>,
        );
      }
    });
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey="products:delete:title">Delete Product</Trans>
          </AlertDialogTitle>
          <AlertDialogDescription>
            <Trans
              i18nKey="products:delete:description"
              values={{ name: product.name }}
            >
              Are you sure you want to delete product "{product.name}"? This
              action cannot be undone.
            </Trans>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            <Trans i18nKey="common:cancel">Cancel</Trans>
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelete}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            disabled={isPending}
          >
            <Trans i18nKey="common:delete">Delete</Trans>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
