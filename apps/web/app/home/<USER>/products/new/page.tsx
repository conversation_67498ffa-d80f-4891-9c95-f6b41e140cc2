import { Suspense } from 'react';

import { Alert, AlertDescription } from '@kit/ui/alert';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { checkCanCreateResource } from '../../_lib/server/resource-access';
import { loadTeamWorkspace } from '../../_lib/server/team-account-workspace.loader';
import { NewProductWrapper } from './_components/new-product-wrapper';

interface NewProductPageProps {
  params: Promise<{ account: string }>;
}

export default async function NewProductPage({ params }: NewProductPageProps) {
  const { account: accountSlug } = await params;

  try {
    const { account } = await loadTeamWorkspace(accountSlug);

    if (!account) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    // Kiểm tra quyền tạo sản phẩm
    const { canCreate, reason, current: resourceCurrent, limit: resourceLimit } = await checkCanCreateResource(account.id, 'products');

    return (
      <>
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="products:createNew">Add Product</Trans>}
          description={
            <Trans i18nKey="products:createNewDescription">
              Create a new product for your store
            </Trans>
          }
        />

        <PageBody data-testid="products-page">
          <Suspense fallback={<div>Loading...</div>}>
            <NewProductWrapper
              accountId={account.id}
              accountSlug={account.slug}
              canCreate={canCreate}
              reason={reason}
              resourceCurrent={resourceCurrent}
              resourceLimit={resourceLimit}
            />
          </Suspense>
        </PageBody>
      </>
    );
  } catch (error) {
    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:generic">
              An error occurred. Please try again.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}
