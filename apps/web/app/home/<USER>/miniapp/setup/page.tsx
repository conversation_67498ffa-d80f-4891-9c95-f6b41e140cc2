import { redirect } from 'next/navigation';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { checkCanCreateResource } from '~/home/<USER>/_lib/server/resource-access';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { PreviewStep } from './_components/preview-step';
import { WizardSteps } from './_components/wizard-steps';
import ChooseTemplatePage from './choose-template/page';
import CustomizationPage from './customization/page';

interface SetupPageProps {
  params: Promise<{ account: string }>;
  searchParams: Promise<{
    step?: string;
    themeId?: string;
    miniAppId?: string;
  }>;
}

export default async function SetupPage({
  params,
  searchParams,
}: SetupPageProps) {
  // Await both params and searchParams
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);

  const { account } = resolvedParams;
  const { step, themeId, miniAppId } = resolvedSearchParams;
  const currentStep = step || '1';

  // Kiểm tra quyền tạo miniapp
  const supabase = getSupabaseServerClient();
  const teamAccountsApi = createTeamAccountsApi(supabase);
  const accountData = await teamAccountsApi.getTeamAccount(account);

  if (!accountData) {
    redirect('/404');
  }

  // Kiểm tra quyền tạo miniapp
  const { canCreate, reason, current, limit } = await checkCanCreateResource(
    accountData.id,
    'miniapp',
  );

  if (!canCreate) {
    // Redirect về trang danh sách miniapp với thông báo
    redirect(`/home/<USER>/miniapp?error=${reason}`);
  }

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account}
        title={<Trans i18nKey="miniapp:setupTitle">Setup Mini App</Trans>}
        description={<AppBreadcrumbs />}
      />
      <PageBody data-testid="miniapp-setup-page">
        <div className="container py-6">
          <div className="mx-auto mb-8 max-w-4xl">
            <WizardSteps currentStep={currentStep} />
          </div>

          <div className="mx-auto max-w-4xl">
            {currentStep === '1' && (
              <ChooseTemplatePage
                params={{ account }}
                searchParams={{ miniAppId: miniAppId || '', step: currentStep }}
              />
            )}
            {currentStep === '2' && themeId && (
              <CustomizationPage
                params={{ account }}
                searchParams={{ themeId }}
              />
            )}
            {currentStep === '3' && themeId && (
              <PreviewStep account={account} themeId={themeId} />
            )}
          </div>
        </div>
      </PageBody>
    </>
  );
}
