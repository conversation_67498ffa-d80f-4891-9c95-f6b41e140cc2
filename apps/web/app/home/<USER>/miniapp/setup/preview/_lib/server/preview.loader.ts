'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export type PreviewData = {
  miniAppId: string;
  templateId: string;
  themeId: string;
  shortLink?: string;
  qrCodeUrl?: string;
  logoUrl?: string;
  brandColor: string;
  accessibility: {
    modifierKeys: boolean;
    highContrast: boolean;
    autoplayVideos: 'system' | 'on' | 'off';
    openLinksInDesktop: boolean;
  };
  // Theme Builder data
  puckData?: any;
  themeConfig?: any;
};

// Helper function to load theme data
async function loadThemeData(supabase: any, themeId: string, tempThemeId?: string) {
  let config: any = {};
  let theme: any = null;

  // Try temp theme first if provided
  if (tempThemeId) {
    const { data: tempTheme } = await supabase
      .from('temp_themes')
      .select('config, account_theme_id, theme_id')
      .eq('id', tempThemeId)
      .maybeSingle();

    if (tempTheme) {
      config = tempTheme.config || {};
      const actualThemeId = tempTheme.account_theme_id || tempTheme.theme_id || themeId;

      // Load theme metadata
      const { data: themeData } = await supabase
        .from('account_themes')
        .select(`
          id, mini_app_id, template_id,
          template:themes(id, name, thumbnail_url, preview_url)
        `)
        .eq('id', actualThemeId)
        .maybeSingle();

      if (themeData) {
        theme = themeData;
        return { theme, config };
      }
    }
  }

  // Fallback to account theme
  const { data: accountTheme, error } = await supabase
    .from('account_themes')
    .select(`
      id, mini_app_id, template_id, config,
      template:themes(id, name, thumbnail_url, preview_url)
    `)
    .eq('id', themeId)
    .maybeSingle();

  if (error) throw new Error('Failed to load theme');
  if (!accountTheme) throw new Error('Theme not found');

  return {
    theme: accountTheme,
    config: accountTheme.config || {}
  };
}

// Helper function to load short link
async function loadShortLink(supabase: any, themeId: string, tempThemeId?: string) {
  const { data: shortLinkData } = await supabase
    .from('short_links')
    .select('url')
    .eq('reference_id', themeId)
    .eq('reference_table', 'account_themes')
    .maybeSingle();

  const url = shortLinkData?.url;
  const shortLink = tempThemeId && url ? `${url}?tempThemeId=${tempThemeId}` : url;
  const qrCodeUrl = shortLink
    ? `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(shortLink)}`
    : undefined;

  return { shortLink, qrCodeUrl };
}

export async function loadPreview(
  themeId: string,
  tempThemeId?: string,
): Promise<PreviewData> {
  const supabase = getSupabaseServerClient();

  // Load theme data - prioritize temp theme if available
  const { theme, config } = await loadThemeData(supabase, themeId, tempThemeId);

  // Load short link and generate QR code
  const { shortLink, qrCodeUrl } = await loadShortLink(supabase, themeId, tempThemeId);

  // Build preview data
  return {
    miniAppId: theme.mini_app_id,
    templateId: theme.template_id,
    themeId: theme.id,
    qrCodeUrl,
    shortLink,
    logoUrl: config.logoUrl,
    brandColor: config.brandColor || '#000000',
    accessibility: {
      modifierKeys: config.accessibility?.modifierKeys ?? true,
      highContrast: config.accessibility?.highContrast ?? false,
      autoplayVideos: config.accessibility?.autoplayVideos ?? 'system',
      openLinksInDesktop: config.accessibility?.openLinksInDesktop ?? false,
    },
    puckData: config.puckData || { content: [], root: { props: {} }, zones: {} },
    themeConfig: config,
  };
}
