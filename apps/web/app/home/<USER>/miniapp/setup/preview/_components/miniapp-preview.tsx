'use client';

import { useEffect, useRef, useState } from 'react';

import { ThemeRenderer } from '@kit/theme-builder';
import { Card } from '@kit/ui/card';

import type { PreviewData } from '../_lib/server/preview.loader';

interface MiniAppPreviewProps {
  previewData: PreviewData;
}

export function MiniAppPreview({ previewData }: MiniAppPreviewProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);

  useEffect(() => {
    if (previewData.qrCodeUrl) {
      setQrCodeUrl(previewData.qrCodeUrl);
    } else if (previewData.shortLink) {
      const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(previewData.shortLink)}`;
      setQrCodeUrl(qrUrl);
    }
  }, [previewData]);

  if (!previewData) {
    return (
      <div className="flex h-[600px] items-center justify-center">
        <div className="text-sm text-red-500">
          Failed to load preview data
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-sm">
      {/* Mobile Preview with ThemeRenderer */}
      <Card className="overflow-hidden bg-white shadow-lg">
        <div className="bg-gray-900 px-4 py-2">
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 rounded-full bg-red-500"></div>
            <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
            <div className="h-3 w-3 rounded-full bg-green-500"></div>
            <div className="ml-auto text-xs text-gray-300">Zalo Mini App</div>
          </div>
        </div>

        <div className="relative aspect-[9/16] w-full bg-white">
          {previewData.puckData && previewData.themeConfig ? (
            <div className="h-full w-full">
              <ThemeRenderer data={previewData.puckData} />
            </div>
          ) : (
            <div className="flex h-full items-center justify-center bg-gray-50">
              <div className="text-center">
                <div className="mb-2 text-gray-400">
                  <svg
                    className="mx-auto h-12 w-12"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">No theme data available</p>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
