'use client';

import { useState, useEffect } from 'react';

import type { PreviewData } from '../_lib/server/preview.loader';
import { MiniAppPreview } from './miniapp-preview';
import { PreviewActions } from './preview-actions';

interface PreviewWrapperProps {
  account: string;
  themeId: string;
  tempThemeId?: string;
  previewData: PreviewData;
}

export function PreviewWrapper({ account, themeId, tempThemeId, previewData }: PreviewWrapperProps) {
  // Debug: Track component lifecycle
  useEffect(() => {
    console.log('🔄 PreviewWrapper mounted with previewData:', {
      hasData: !!previewData,
      themeId,
      tempThemeId
    });

    return () => {
      console.log('🔄 PreviewWrapper unmounted');
    };
  }, [previewData, themeId, tempThemeId]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Preview Section */}
      <div className="lg:col-span-2">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold mb-4">Live Preview</h2>
          <MiniAppPreview
            previewData={previewData}
          />
        </div>
      </div>

      {/* Actions Section */}
      <div className="lg:col-span-1">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold mb-4">Actions</h2>
          <PreviewActions
            account={account}
            themeId={themeId}
            tempThemeId={tempThemeId}
            qrCodeUrl={previewData.qrCodeUrl}
            shortLink={previewData.shortLink}
          />
        </div>
      </div>
    </div>
  );
}
