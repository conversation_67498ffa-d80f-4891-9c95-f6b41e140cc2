'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function updateMiniAppStatus(
  id: string,
  isActive: boolean,
  accountSlug: string,
) {
  const supabase = getSupabaseServerClient();

  try {
    const { error } = await supabase
      .from('account_themes')
      .update({
        is_active: isActive,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    if (error) throw error;

    // Revalidate related paths
    revalidatePath(`/home/<USER>/miniapp`);
    revalidatePath(`/home/<USER>/miniapp/setup`);
  } catch (error) {
    console.error('Error updating miniapp status:', error);
    throw new Error('Failed to update miniapp status');
  }
}
