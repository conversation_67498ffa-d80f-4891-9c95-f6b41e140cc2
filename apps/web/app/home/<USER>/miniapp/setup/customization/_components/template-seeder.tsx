'use client';

import { useState } from 'react';
import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

import { seedMarketingTemplates } from '../_lib/server/theme-templates.actions';
import MARKETING_TEMPLATES from '../_lib/constants/marketing-templates';

interface TemplateSeederProps {
  isAdmin?: boolean;
}

export function TemplateSeeder({ isAdmin = false }: TemplateSeederProps) {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedResult, setSeedResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleSeedTemplates = async () => {
    if (!isAdmin) {
      toast.error('Only admins can seed templates');
      return;
    }

    setIsSeeding(true);
    setSeedResult(null);

    try {
      const result = await seedMarketingTemplates();
      setSeedResult(result);
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      const errorMessage = 'Failed to seed templates';
      setSeedResult({
        success: false,
        message: errorMessage,
      });
      toast.error(errorMessage);
    } finally {
      setIsSeeding(false);
    }
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>🌱</span>
          Template Seeder (Admin Only)
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Seed Action */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h3 className="font-semibold">Seed Marketing Templates</h3>
            <p className="text-sm text-gray-600">
              Add {MARKETING_TEMPLATES.length} marketing templates to the database
            </p>
          </div>
          
          <Button
            onClick={handleSeedTemplates}
            disabled={isSeeding}
            className="min-w-[120px]"
          >
            {isSeeding ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Seeding...
              </>
            ) : (
              'Seed Templates'
            )}
          </Button>
        </div>

        {/* Seed Result */}
        {seedResult && (
          <Alert variant={seedResult.success ? 'default' : 'destructive'}>
            {seedResult.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription>{seedResult.message}</AlertDescription>
          </Alert>
        )}

        {/* Templates Preview */}
        <div>
          <h3 className="font-semibold mb-4">Available Templates ({MARKETING_TEMPLATES.length})</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {MARKETING_TEMPLATES.map((template) => (
              <Card key={template.id} className="h-full">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    {/* Template Preview */}
                    <div className="h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span className="text-2xl">🎨</span>
                    </div>
                    
                    {/* Template Info */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm line-clamp-1">
                          {template.name}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          {template.category}
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-gray-600 line-clamp-2">
                        {template.description}
                      </p>
                      
                      <div className="text-xs text-gray-500">
                        ID: {template.id}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Template Structure Info */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-2">Template Structure</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div>• Each template follows Puck data model structure</div>
            <div>• Contains content array with ZMP components</div>
            <div>• Includes root props for theme configuration</div>
            <div>• Uses slots for nested component structure</div>
            <div>• Optimized for education marketing to attract parents</div>
          </div>
        </div>

        {/* Categories Summary */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium mb-2">Template Categories</h4>
          <div className="flex flex-wrap gap-2">
            {[...new Set(MARKETING_TEMPLATES.map(t => t.category))].map((category) => (
              <Badge key={category} variant="secondary">
                {category} ({MARKETING_TEMPLATES.filter(t => t.category === category).length})
              </Badge>
            ))}
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="p-4 bg-green-50 rounded-lg">
          <h4 className="font-medium mb-2">How to Use</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div>1. Click "Seed Templates" to add templates to database</div>
            <div>2. Templates will be available in the theme selector modal</div>
            <div>3. Users can preview and apply templates in the theme builder</div>
            <div>4. Templates are designed for education sector marketing</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default TemplateSeeder;
