'use client';

import {useSupabase} from '@kit/supabase/hooks/use-supabase';
import {useQuery} from '@tanstack/react-query';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@kit/ui/card';
import {Alert, AlertDescription, AlertTitle} from '@kit/ui/alert';
import {AlertCircle, CheckCircle2} from 'lucide-react';
import {Skeleton} from '@kit/ui/skeleton';
import {ConnectButton} from '../../../../integrations/zns/_components/connect-button';
import {Trans} from '@kit/ui/trans';

interface ThemeOaConnectProps {
    themeId: string;
    accountId: string;
}

export function ThemeOaConnect({themeId, accountId}: ThemeOaConnectProps) {
    const supabase = useSupabase();

    // Lấy thông tin theme
    const {data: theme, isLoading: isLoadingTheme} = useQuery({
        queryKey: ['theme', themeId],
        queryFn: async () => {
            const {data, error} = await supabase
                .from('themes')
                .select('*, oa_config:oa_configurations(*)')
                .eq('id', themeId)
                .single();

            if (error) throw error;
            return data;
        },
        enabled: !!themeId
    });

    // Kiểm tra xem người dùng có phải là author của theme không
    const {data: isAuthor, isLoading: isLoadingAuthor} = useQuery({
        queryKey: ['is-theme-author', themeId],
        queryFn: async () => {
            const {data: user} = await supabase.auth.getUser();
            if (!user.user) return false;

            return theme?.author_id === user.user.id;
        },
        enabled: !!theme
    });

    // Kiểm tra trạng thái kết nối của OA
    const {data: isConnected, isLoading: isLoadingConnection} = useQuery({
        queryKey: ['oa-connection-status', theme?.oa_config?.id],
        queryFn: async () => {
            if (!theme?.oa_config?.id) return false;

            // Kiểm tra token có còn hạn hay không
            const hasValidToken = theme.oa_config.access_token &&
                theme.oa_config.token_expires_at &&
                new Date(theme.oa_config.token_expires_at) > new Date();

            if (hasValidToken) return true;

            // Nếu token hết hạn, kiểm tra bằng RPC function
            const {data: isValid} = await supabase.rpc('is_oa_token_valid', {
                oa_config_id: theme.oa_config.id
            });

            return !!isValid;
        },
        enabled: !!theme?.oa_config?.id
    });

    const isLoading = isLoadingTheme || isLoadingAuthor || isLoadingConnection;

    if (isLoading) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle><Skeleton className="h-6 w-48"/></CardTitle>
                    <CardDescription><Skeleton className="h-4 w-full"/></CardDescription>
                </CardHeader>
                <CardContent>
                    <Skeleton className="h-20 w-full"/>
                </CardContent>
            </Card>
        );
    }

    // Nếu không phải là author, không hiển thị phần kết nối
    if (!isAuthor) {
        return null;
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>
                    <Trans i18nKey="miniapp:oaConnection">Kết nối Zalo OA</Trans>
                </CardTitle>
                <CardDescription>
                    <Trans i18nKey="miniapp:oaConnectionDesc">
                        Kết nối Zalo OA để người dùng có thể sử dụng theme này
                    </Trans>
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <Alert variant={isConnected ? 'default' : 'destructive'}>
                    {isConnected ? (
                        <CheckCircle2 className="h-4 w-4"/>
                    ) : (
                        <AlertCircle className="h-4 w-4"/>
                    )}
                    <AlertTitle>
                        {isConnected ? 'Đã kết nối' : 'Chưa kết nối'}
                    </AlertTitle>
                    <AlertDescription>
                        {isConnected
                            ? 'Zalo OA đã được kết nối và sẵn sàng sử dụng'
                            : 'Bạn cần kết nối Zalo OA để người dùng có thể sử dụng theme này'}
                    </AlertDescription>
                </Alert>

                <div className="flex justify-end">
                    <ConnectButton
                        accountId={accountId}
                        isConnected={isConnected}
                        oaConfigId={theme?.oa_config?.id}
                        appId={theme?.oa_config?.app_id}
                    />
                </div>
            </CardContent>
        </Card>
    );
}
