import { ThemeConfig } from '../types/theme-config.types';

// Default colors - defined once to avoid duplication
export const DEFAULT_COLORS: ThemeConfig['colors'] = {
  primary: { main: '#2563eb', light: '#3b82f6', dark: '#1d4ed8' },
  secondary: { main: '#4f46e5', light: '#6366f1', dark: '#4338ca' },
  accent: { main: '#f59e0b', light: '#fbbf24', dark: '#d97706' },
  background: { default: '#ffffff', paper: '#f3f4f6' },
  text: { primary: '#111827', secondary: '#4b5563' },
};

// Default typography - defined once to avoid duplication
export const DEFAULT_TYPOGRAPHY: ThemeConfig['typography'] = {
  fontFamily: 'Inter, system-ui, sans-serif',
  headings: { fontFamily: 'Inter, system-ui, sans-serif', fontWeight: '600' },
  body: { fontFamily: 'Inter, system-ui, sans-serif', fontWeight: '400' },
};

// Minimal empty RootData structure for puckData
export const EMPTY_PUCK_DATA = {
  content: [],
  root: { props: {} },
  zones: {},
};

// Default theme name
export const DEFAULT_THEME_NAME = 'My Theme';
