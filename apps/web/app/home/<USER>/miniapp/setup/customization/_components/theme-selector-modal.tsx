'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@kit/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { Badge } from '@kit/ui/badge';
import { Card, CardContent } from '@kit/ui/card';
import { ThemeRenderer } from '@kit/theme-builder';
import { Trans } from '@kit/ui/trans';
import { ChevronLeft, ChevronRight, Eye, Check, Loader2 } from 'lucide-react';

import { loadThemeTemplates, ThemeTemplate } from '../_lib/server/theme-templates.actions';

interface ThemeSelectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (templateData: any) => void;
  currentData?: any;
}

const ITEMS_PER_PAGE = 6;

export function ThemeSelectorModal({
  isOpen,
  onClose,
  onSelectTemplate,
  currentData,
}: ThemeSelectorModalProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<ThemeTemplate | null>(null);
  const [templates, setTemplates] = useState<ThemeTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  // Load templates when modal opens or page changes
  useEffect(() => {
    if (isOpen) {
      loadTemplates();
    }
  }, [isOpen, currentPage]);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      console.log('Loading templates, page:', currentPage);
      const result = await loadThemeTemplates(currentPage, ITEMS_PER_PAGE);
      console.log('Templates loaded:', result);
      setTemplates(result.templates);
      setTotalPages(result.totalPages);
      setTotal(result.total);
    } catch (error) {
      console.error('Failed to load templates:', error);
      // Fallback to static templates if server action fails
      const MARKETING_TEMPLATES = await import('../_lib/constants/marketing-templates').then(m => m.default);
      const total = MARKETING_TEMPLATES.length;
      const totalPages = Math.ceil(total / ITEMS_PER_PAGE);
      const offset = (currentPage - 1) * ITEMS_PER_PAGE;
      const paginatedTemplates = MARKETING_TEMPLATES.slice(offset, offset + ITEMS_PER_PAGE);

      setTemplates(paginatedTemplates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        thumbnail_url: template.thumbnail,
        config: template.data,
        type: 'default' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })));
      setTotalPages(totalPages);
      setTotal(total);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectTemplate = (template: ThemeTemplate) => {
    setSelectedTemplate(template.id);
    onSelectTemplate(template.config);
    onClose();
  };

  const handlePreview = (template: ThemeTemplate) => {
    console.log('Preview template:', template);
    console.log('Template config:', template.config);
    console.log('Config type:', typeof template.config);
    console.log('Config keys:', template.config ? Object.keys(template.config) : 'null');
    setPreviewTemplate(template);
  };

  const closePreview = () => {
    setPreviewTemplate(null);
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      landing: 'bg-blue-100 text-blue-800',
      programs: 'bg-green-100 text-green-800',
      testimonials: 'bg-purple-100 text-purple-800',
      contact: 'bg-orange-100 text-orange-800',
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <span>🎨</span>
              <Trans i18nKey="miniapp:themeSelector:title">
                Chọn Template Marketing
              </Trans>
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col h-full">
            {/* Templates Grid */}
            <div className="flex-1 overflow-y-auto p-4">
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading templates...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {templates.map((template) => (
                  <Card
                    key={template.id}
                    className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105"
                  >
                    <CardContent className="p-4">
                      {/* Template Preview */}
                      <div className="relative mb-3 h-32 bg-gray-100 rounded-lg overflow-hidden">
                        {template.thumbnail_url ? (
                          <img
                            src={template.thumbnail_url}
                            alt={template.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                            <span className="text-4xl">🎨</span>
                          </div>
                        )}
                        
                        {/* Overlay with actions */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePreview(template);
                              }}
                              className="bg-white text-gray-900 hover:bg-gray-100"
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Preview
                            </Button>
                            <Button
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectTemplate(template);
                              }}
                            >
                              <Check className="h-4 w-4 mr-1" />
                              Chọn
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Template Info */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-sm line-clamp-1">
                            {template.name}
                          </h3>
                          <Badge className={getCategoryColor(template.category)}>
                            {template.category}
                          </Badge>
                        </div>
                        
                        <p className="text-xs text-gray-600 line-clamp-2">
                          {template.description}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between p-4 border-t">
              <div className="text-sm text-gray-600">
                Trang {currentPage} / {totalPages} ({total} templates)
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={prevPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Trước
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant={page === currentPage ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  ))}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                >
                  Sau
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Preview Modal */}
      {previewTemplate && (
        <Dialog open={!!previewTemplate} onOpenChange={closePreview}>
          <DialogContent className="max-w-4xl max-h-[90vh]">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>👀 Preview: {previewTemplate.name}</span>
                <Button
                  onClick={() => handleSelectTemplate(previewTemplate)}
                  size="sm"
                >
                  <Check className="h-4 w-4 mr-1" />
                  Chọn Template Này
                </Button>
              </DialogTitle>
            </DialogHeader>
            
            <div className="flex-1 overflow-y-auto">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="bg-white rounded-lg shadow-sm overflow-hidden max-h-96 overflow-y-auto">
                  {previewTemplate.config ? (
                    <div>
                      <div className="p-2 bg-blue-50 text-xs text-blue-600 border-b">
                        Preview: {previewTemplate.name}
                      </div>
                      <ThemeRenderer
                        data={previewTemplate.config}
                        className="w-full"
                      />
                    </div>
                  ) : (
                    <div className="p-8 text-center text-gray-500">
                      <p className="mb-4">⚠️ No preview data available</p>
                      <div className="text-left">
                        <p className="text-sm font-medium mb-2">Template Info:</p>
                        <div className="bg-gray-100 p-3 rounded text-xs">
                          <div>ID: {previewTemplate.id}</div>
                          <div>Name: {previewTemplate.name}</div>
                          <div>Category: {previewTemplate.category}</div>
                          <div>Config: {previewTemplate.config ? 'Available' : 'Missing'}</div>
                        </div>
                        {previewTemplate.config && (
                          <pre className="mt-4 text-xs bg-gray-100 p-2 rounded max-h-32 overflow-y-auto">
                            {JSON.stringify(previewTemplate.config, null, 2)}
                          </pre>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}

export default ThemeSelectorModal;
