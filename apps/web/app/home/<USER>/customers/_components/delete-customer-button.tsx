'use client';

import { useState } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { Trans } from '@kit/ui/trans';

import { deleteCustomer } from '../_lib/server/actions';
import type { Customer } from '../_lib/types';

interface DeleteCustomerButtonProps {
  customer: Customer;
  account: { id: string; slug: string };
  children: React.ReactNode;
}

export default function DeleteCustomerButton({
  customer,
  account,
  children,
}: DeleteCustomerButtonProps) {
  const [open, setOpen] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteCustomer(customer.id, account.slug);
      setOpen(false);
    } catch (error) {
      console.error('Failed to delete customer:', error);
    }
  };

  return (
    <>
      <span onClick={() => setOpen(true)}>{children}</span>
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <Trans i18nKey="customers:deleteDialog:title">
                Delete Customer
              </Trans>
            </AlertDialogTitle>
            <AlertDialogDescription>
              <Trans i18nKey="customers:deleteDialog:description">
                Are you sure you want to delete this customer? This action
                cannot be undone.
              </Trans>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              <Trans i18nKey="common:actions:cancel">Cancel</Trans>
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} data-testid="confirm-delete-button">
              <Trans i18nKey="common:actions:delete">Delete</Trans>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
