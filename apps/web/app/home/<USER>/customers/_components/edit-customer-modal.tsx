'use client';

import { useState } from 'react';

import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';

import type { Customer } from '../_lib/types';
import CustomerForm from './customer-form';

interface EditCustomerModalProps {
  customer: Customer;
  account: { id: string; slug: string };
  children: React.ReactNode;
}

export default function EditCustomerModal({
  customer,
  account,
  children,
}: EditCustomerModalProps) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <span onClick={() => setOpen(true)}>{children}</span>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              <Trans i18nKey="customers:editDialog:title">Edit Customer</Trans>
            </DialogTitle>
          </DialogHeader>
          <CustomerForm
            customer={customer}
            account={account}
            onSuccess={() => setO<PERSON>(false)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
