'use client';

import { useState } from 'react';

import { But<PERSON> } from '@kit/ui/button';
import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Trans } from '@kit/ui/trans';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface CustomerPhoneInputProps {
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
}

export function CustomerPhoneInput({ value, onChange, disabled }: CustomerPhoneInputProps) {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState('');

  const handleAddPhone = () => {
    if (!inputValue.trim()) return;

    // Split input by commas
    const phoneNumbers = inputValue.split(',').map(phone => phone.trim()).filter(phone => phone !== '');

    // Basic phone validation (can be improved)
    const phoneRegex = /^\+?[0-9]{8,15}$/;
    const validPhones = phoneNumbers.filter(phone => phoneRegex.test(phone));

    if (validPhones.length === 0) {
      // You could show an error message here
      return;
    }

    // Add phones if they don't already exist
    const newPhones = validPhones.filter(phone => !value.includes(phone));
    if (newPhones.length > 0) {
      onChange([...value, ...newPhones]);
    }

    setInputValue('');
  };

  const handleRemovePhone = (phone: string) => {
    onChange(value.filter((p) => p !== phone));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddPhone();
    }
  };

  return (
    <FormItem>
      <FormLabel>
        <Trans i18nKey="vouchers:customer_phones">Customer Phone Numbers</Trans>
      </FormLabel>
      <div className="space-y-2">
        <div className="flex space-x-2">
          <FormControl>
            <Input
              placeholder={t('vouchers:phone_placeholder', '+84123456789, +84987654321')}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={disabled}
            />
          </FormControl>
          <Button
            type="button"
            onClick={handleAddPhone}
            disabled={disabled || !inputValue.trim()}
          >
            <Trans i18nKey="common:add">Add</Trans>
          </Button>
        </div>

        {value.length > 0 && (
          <ScrollArea className="h-32 rounded-md border">
            <div className="p-2">
              <h4 className="mb-2 text-sm font-medium">
                <Trans i18nKey="vouchers:added_phones">Added Phone Numbers</Trans> ({value.length})
              </h4>
              <div className="flex flex-wrap gap-2">
                {value.map((phone) => (
                  <div
                    key={phone}
                    className="flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm"
                  >
                    <span>{phone}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="ml-1 h-5 w-5"
                      onClick={() => handleRemovePhone(phone)}
                      disabled={disabled}
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">
                        <Trans i18nKey="common:remove">Remove</Trans>
                      </span>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </ScrollArea>
        )}
      </div>
      <FormDescription>
        <Trans i18nKey="vouchers:customer_phones_description">
          Enter phone numbers of customers who can use this voucher. Only applies if
          customer-specific is enabled.
        </Trans>
      </FormDescription>
      <FormMessage />
    </FormItem>
  );
}
