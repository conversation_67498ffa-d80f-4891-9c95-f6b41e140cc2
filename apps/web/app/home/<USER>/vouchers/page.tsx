'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';
import { useParams } from 'next/navigation';

import { format } from 'date-fns';
import { Percent, Plus, Ticket } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { SearchListInput } from '@kit/ui/search-list-input';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { Voucher } from '~/lib/types/voucher';

import { createVoucherApi } from './_lib/server/voucher.api';

export default function VouchersPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const supabase = useSupabase();
  const { t } = useTranslation();
  const [vouchers, setVouchers] = useState<Voucher[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVouchers = async () => {
      setLoading(true);
      try {
        const api = createVoucherApi(supabase);

        // Get team account data using the API
        const teamAccountsApi = createTeamAccountsApi(supabase);
        const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

        if (!teamAccount || !teamAccount.id) {
          throw new Error('Failed to fetch account');
        }

        const data = await api.getVouchers(teamAccount.id);
        setVouchers(data);
      } catch (error) {
        console.error('Error fetching vouchers:', error);
        toast({
          title: 'Error',
          description: 'Failed to load vouchers',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchVouchers();
  }, [accountSlug, supabase]);

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="common:routes.vouchers" />}
        description={<AppBreadcrumbs />}
        account={accountSlug}
      />

      <PageBody>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle>
            <SearchListInput
              defaultValue=""
              placeholder={t('vouchers:search.placeholder')}
              data-testid="vouchers-page-search"
            />
          </CardTitle>

          <Button asChild data-testid="vouchers-page-add-button">
            <Link href={`/home/<USER>/vouchers/new`}>
              <Plus className="mr-2 h-4 w-4" />
              <Trans i18nKey="vouchers:create_new">Create New</Trans>
            </Link>
          </Button>
        </CardHeader>

        <CardContent>
          {loading ? (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-5 w-16 rounded-full" />
                    </div>
                    <Skeleton className="mt-1 h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="mb-2 h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : vouchers.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="bg-muted rounded-full p-3">
                <Ticket className="text-muted-foreground h-10 w-10" />
              </div>
              <h3 className="mt-4 text-lg font-semibold">
                <Trans i18nKey="vouchers:no_vouchers">No Vouchers</Trans>
              </h3>
              <p className="text-muted-foreground mt-2 max-w-md text-sm">
                <Trans i18nKey="vouchers:no_vouchers_description">
                  You haven&apos;t created any vouchers yet. Create your first
                  voucher to offer discounts to your customers.
                </Trans>
              </p>
              <Button className="mt-4" asChild>
                <Link href={`/home/<USER>/vouchers/new`}>
                  <Plus className="mr-2 h-4 w-4" />
                  <Trans i18nKey="vouchers:create_first">
                    Create Your First Voucher
                  </Trans>
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {vouchers.map((voucher) => (
                <Link
                  key={voucher.id}
                  href={`/home/<USER>/vouchers/${voucher.id}`}
                  className="block"
                >
                  <Card className="hover:border-primary/50 h-full overflow-hidden transition-all hover:shadow-md">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center text-base">
                          <Ticket className="mr-2 h-5 w-5 text-blue-500" />
                          {voucher.name}
                        </CardTitle>
                        <div
                          className={`rounded-full px-2 py-1 text-xs font-medium ${
                            voucher.status === 'active'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : voucher.status === 'expired'
                                ? 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                                : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          }`}
                        >
                          <Trans i18nKey={`vouchers:status.${voucher.status}`}>
                            {voucher.status.charAt(0).toUpperCase() +
                              voucher.status.slice(1)}
                          </Trans>
                        </div>
                      </div>
                      <CardDescription className="mt-1 font-mono text-xs font-medium uppercase">
                        {voucher.code}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-muted-foreground line-clamp-2 text-sm">
                          {voucher.description || (
                            <span className="text-muted-foreground/70 italic">
                              No description
                            </span>
                          )}
                        </p>
                        <div className="flex items-center text-sm">
                          <span className="flex items-center font-medium">
                            <Percent className="mr-1 h-3.5 w-3.5 text-blue-500" />
                            {voucher.discount_type === 'percentage'
                              ? `${voucher.discount_value}%`
                              : `${voucher.discount_value.toLocaleString()} VND`}{' '}
                            <Trans i18nKey="vouchers:discount">discount</Trans>
                          </span>
                        </div>
                        {voucher.min_order_value && (
                          <div className="bg-muted/50 rounded-md px-2 py-1 text-xs">
                            <Trans i18nKey="vouchers:min_order">
                              For orders over{' '}
                              {voucher.min_order_value.toLocaleString()} VND
                            </Trans>
                          </div>
                        )}
                        <div className="text-muted-foreground flex items-center justify-between text-xs">
                          <span>
                            <Trans i18nKey="vouchers:valid_until">
                              Valid until
                            </Trans>{' '}
                            {format(
                              new Date(voucher.end_date),
                              'dd/MM/yyyy HH:mm',
                            )}
                          </span>
                          {voucher.max_uses && (
                            <span>
                              {voucher.uses_count || 0}/{voucher.max_uses} used
                            </span>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}
        </CardContent>
      </PageBody>
    </>
  );
}
