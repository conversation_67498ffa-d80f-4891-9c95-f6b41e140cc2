'use client';

import { useCallback, useEffect, useState } from 'react';

import Link from 'next/link';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  ArrowLeft,
  Percent,
  ShoppingBag,
  Ticket,
  Users,
  Wand2,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Checkbox } from '@kit/ui/checkbox';
import { DateTimePicker } from '@kit/ui/date-time-picker';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { getProductsAndCategories } from '../_actions/get-products-categories';
import { CustomerPhoneInput } from '../_components/customer-phone-input';
import { ProductCategorySelector } from '../_components/product-category-selector';
import { ValidPeriodDisplay } from '../_components/valid-period-display';
import { createVoucherAction } from '../_lib/server/voucher.actions';

const voucherFormSchema = z.object({
  code: z
    .string()
    .min(1, { message: 'Code is required' })
    .refine((val) => /^[A-Z0-9_-]+$/i.test(val), {
      message: 'Code can only contain letters, numbers, underscores, and hyphens',
    }),
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().optional(),
  discount_type: z.enum(['percentage', 'fixed']),
  discount_value: z
    .number()
    .min(0.01, { message: 'Discount value must be greater than 0' }),
  min_order_value: z.number().optional(),
  max_discount_value: z.number().optional(),
  max_uses: z.number().optional(),
  start_date: z.date({
    required_error: 'Start date is required',
  }),
  end_date: z.date({
    required_error: 'End date is required',
  }),
  // Advanced restrictions
  is_customer_specific: z.boolean().optional(),
  customer_phones: z.array(z.string()).optional(),
  usage_limit_per_customer: z.number().optional(),
  first_time_customers_only: z.boolean().optional(),
  min_previous_orders: z.number().optional(),
  included_product_ids: z.array(z.string()).default([]),
  excluded_product_ids: z.array(z.string()).default([]),
  included_category_ids: z.array(z.string()).default([]),
  excluded_category_ids: z.array(z.string()).default([]),
}).refine(
  (data) => {
    return data.end_date > data.start_date;
  },
  {
    message: 'End date must be after start date',
    path: ['end_date'],
  }
);

type VoucherFormValues = z.infer<typeof voucherFormSchema>;

// Function to generate a random voucher code
const generateVoucherCode = (locale = 'vi') => {
  // Define character sets for different parts of the code
  const prefixes = {
    en: [
      'SUMMER',
      'WINTER',
      'SPRING',
      'FALL',
      'SALE',
      'PROMO',
      'DEAL',
      'SAVE',
      'GIFT',
      'SPECIAL',
    ],
    vi: [
      'SALE',
      'GIAMGIA',
      'KHUYENMAI',
      'TETHOLIDAY',
      'QUATET',
      'SINHNHAT',
      'CHAOHE',
      'CHAOTHU',
      'CHAODONG',
      'CHAOXUAN',
    ],
  };
  const numbers = '0123456789';

  // Randomly select a prefix based on locale
  const prefixList = prefixes[locale] || prefixes.en;
  const prefix = prefixList[Math.floor(Math.random() * prefixList.length)];

  // Generate a random number part (2-4 digits)
  const numDigits = Math.floor(Math.random() * 3) + 2; // 2 to 4 digits
  let numberPart = '';
  for (let i = 0; i < numDigits; i++) {
    numberPart += numbers.charAt(Math.floor(Math.random() * numbers.length));
  }

  // Add current year to make it more relevant
  const year = new Date().getFullYear().toString().slice(2); // Get last 2 digits of year

  // Return the combined code
  return `${prefix}${numberPart}${year}`;
};

export default function NewVoucherPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const router = useRouter();
  const supabase = useSupabase();
  const { t } = useTranslation();
  const { accounts, account, user } = useTeamAccountWorkspace();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [products, setProducts] = useState<{ id: string; name: string }[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>(
    [],
  );
  const [isLoadingItems, setIsLoadingItems] = useState(false);

  // Load products and categories
  const loadProductsAndCategories = useCallback(async () => {
    if (!account?.id) return;

    setIsLoadingItems(true);
    try {
      const result = await getProductsAndCategories(account.id);
      if (result.success) {
        setProducts(result.products);
        setCategories(result.categories);
      }
    } catch (error) {
      console.error('Failed to load products and categories', error);
    } finally {
      setIsLoadingItems(false);
    }
  }, [account?.id]);

  useEffect(() => {
    loadProductsAndCategories();
  }, [loadProductsAndCategories]);

  const form = useForm<VoucherFormValues>({
    resolver: zodResolver(voucherFormSchema),
    defaultValues: {
      code: '',
      name: '',
      description: '',
      discount_type: 'percentage',
      discount_value: 10,
      min_order_value: undefined,
      max_discount_value: undefined,
      max_uses: undefined,
      start_date: new Date(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      // Advanced restrictions
      is_customer_specific: false,
      customer_phones: [],
      usage_limit_per_customer: undefined,
      first_time_customers_only: false,
      min_previous_orders: undefined,
      included_product_ids: [],
      excluded_product_ids: [],
      included_category_ids: [],
      excluded_category_ids: [],
    },
  });

  const discountType = form.watch('discount_type');

  // Check if voucher code exists
  const checkVoucherCodeExists = async (code: string): Promise<boolean> => {
    try {
      if (!account?.id) return false;

      const { data, error } = await supabase
        .from('vouchers')
        .select('id')
        .eq('account_id', account.id)
        .eq('code', code.toUpperCase())
        .limit(1);

      if (error) {
        console.error('Error checking voucher code:', error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error('Error checking voucher code:', error);
      return false;
    }
  };

  const onSubmit = async (values: VoucherFormValues) => {
    setIsSubmitting(true);

    try {
      const teamAccountsApi = createTeamAccountsApi(supabase);
      const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

      if (!teamAccount || !teamAccount.id) {
        throw new Error('Failed to fetch account');
      }

      // Validate percentage discount
      if (
        values.discount_type === 'percentage' &&
        (values.discount_value <= 0 || values.discount_value > 100)
      ) {
        form.setError('discount_value', {
          type: 'manual',
          message: 'Percentage discount must be between 0 and 100',
        });
        setIsSubmitting(false);
        return;
      }

      // Check if voucher code already exists
      const codeExists = await checkVoucherCodeExists(values.code);
      if (codeExists) {
        form.setError('code', {
          type: 'manual',
          message: 'Voucher code already exists. Please use a different code.',
        });
        setIsSubmitting(false);
        return;
      }

      const result = await createVoucherAction({
        accountId: account.id,
        accountSlug,
        code: values.code.toUpperCase(),
        name: values.name,
        description: values.description,
        discount_type: values.discount_type,
        discount_value: values.discount_value,
        min_order_value: values.min_order_value,
        max_discount_value: values.max_discount_value,
        max_uses: values.max_uses,
        start_date: values.start_date.toISOString(),
        end_date: values.end_date.toISOString(),
        // Advanced restrictions
        is_customer_specific: values.is_customer_specific,
        customer_phones: values.customer_phones,
        usage_limit_per_customer: values.usage_limit_per_customer,
        first_time_customers_only: values.first_time_customers_only,
        min_previous_orders: values.min_previous_orders,
        excluded_product_ids: values.excluded_product_ids,
        included_product_ids: values.included_product_ids,
        excluded_category_ids: values.excluded_category_ids,
        included_category_ids: values.included_category_ids,
        // We'll handle adding customer phones separately after voucher creation if needed
      });

      if (!result.success) {
        if (result.error?.includes('Voucher code already exists')) {
          form.setError('code', {
            type: 'manual',
            message: 'Voucher code already exists. Please use a different code.',
          });
          setIsSubmitting(false);
          return;
        }
        throw new Error(result.error || 'Failed to create voucher');
      }

      // If customer-specific and has phone numbers, add them to the voucher
      if (
        values.is_customer_specific &&
        values.customer_phones &&
        values.customer_phones.length > 0
      ) {
        try {
          // Import the server action
          const { addCustomerPhonesToVoucher } = await import(
            '../_actions/add-customer-phones'
          );

          await addCustomerPhonesToVoucher(
            result.id,
            values.customer_phones,
            accountSlug,
          );
        } catch (phoneError) {
          console.error('Failed to add customer phones to voucher', phoneError);
          // Continue anyway, we'll show a success message for the voucher creation
        }
      }

      toast.success('Voucher created successfully');

      // Redirect to the voucher detail page
      router.push(`/home/<USER>/vouchers/${result.id}`);
    } catch (error) {
      console.error('Error creating voucher:', error);

      // Check for specific error messages
      const errorMessage = error instanceof Error ? error.message : 'Failed to create voucher';

      if (errorMessage.includes('Voucher code already exists')) {
        form.setError('code', {
          type: 'manual',
          message: 'Voucher code already exists. Please use a different code.',
        });
      } else if (errorMessage.includes('End date must be after start date')) {
        form.setError('end_date', {
          type: 'manual',
          message: 'End date must be after start date',
        });
      } else if (errorMessage.includes('Percentage discount must be between')) {
        form.setError('discount_value', {
          type: 'manual',
          message: 'Percentage discount must be between 0 and 100',
        });
      } else {
        // Generic error toast for other errors
        toast.error(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="vouchers:create_voucher" />}
        description={<AppBreadcrumbs />}
        account={accountSlug}
      />

      <PageBody>
        <div className="mx-auto max-w-3xl">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-2">
              <Link href={`/home/<USER>/vouchers`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                <Trans i18nKey="common:back">Back</Trans>
              </Link>
            </Button>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <div className="rounded-full bg-blue-100 p-2 dark:bg-blue-900/20">
                  <Ticket className="h-5 w-5 text-blue-600 dark:text-blue-500" />
                </div>
                <div>
                  <CardTitle>
                    <Trans i18nKey="vouchers:create_voucher">
                      Create Voucher
                    </Trans>
                  </CardTitle>
                  <CardDescription>
                    <Trans i18nKey="vouchers:create_voucher_description">
                      Create a new voucher to offer discounts to your customers.
                    </Trans>
                  </CardDescription>
                  <ValidPeriodDisplay
                    startDate={form.getValues('start_date')}
                    endDate={form.getValues('end_date')}
                  />
                </div>
              </div>
            </CardHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:code">Code</Trans>
                            </FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  placeholder={t(
                                    'vouchers:code_placeholder',
                                    'SUMMER2025',
                                  )}
                                  className="pr-10 uppercase"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(e.target.value.toUpperCase())
                                  }
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="absolute top-0 right-0 h-full px-3 py-2"
                                  onClick={() => {
                                    const locale = t('common:locale', 'vi');
                                    const code = generateVoucherCode(locale);
                                    field.onChange(code);
                                  }}
                                  title={t(
                                    'vouchers:generate_code',
                                    'Generate code',
                                  )}
                                >
                                  <Wand2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:code_description">
                                A unique code that customers will enter to apply
                                the discount.
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:name">Name</Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'vouchers:name_placeholder',
                                  'Summer Discount',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="vouchers:description">
                              Description
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={t(
                                'vouchers:description_placeholder',
                                'Special discount for summer products...',
                              )}
                              className="resize-none"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="discount_type"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel>
                            <Trans i18nKey="vouchers:discount_type">
                              Discount Type
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex space-x-4"
                            >
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem
                                    value="percentage"
                                    id="percentage"
                                  />
                                </FormControl>
                                <label htmlFor="percentage" className="text-sm">
                                  <Trans i18nKey="vouchers:percentage">
                                    Percentage
                                  </Trans>
                                </label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="fixed" id="fixed" />
                                </FormControl>
                                <label htmlFor="fixed" className="text-sm">
                                  <Trans i18nKey="vouchers:fixed">
                                    Fixed Amount
                                  </Trans>
                                </label>
                              </div>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="discount_value"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:discount_value">
                                Discount Value
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  type="number"
                                  placeholder={
                                    discountType === 'percentage'
                                      ? t(
                                          'vouchers:percentage_placeholder',
                                          '20',
                                        )
                                      : t('vouchers:fixed_placeholder', '50000')
                                  }
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(parseFloat(e.target.value))
                                  }
                                  value={field.value || ''}
                                />
                                {discountType === 'percentage' && (
                                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                    <Percent className="text-muted-foreground h-4 w-4" />
                                  </div>
                                )}
                              </div>
                            </FormControl>
                            <FormDescription>
                              {discountType === 'percentage' ? (
                                <Trans i18nKey="vouchers:percentage_description">
                                  Percentage discount (1-100)
                                </Trans>
                              ) : (
                                <Trans i18nKey="vouchers:fixed_description">
                                  Fixed amount discount
                                </Trans>
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="min_order_value"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:min_order_value">
                                Minimum Order Value
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder={t(
                                  'vouchers:min_order_value_placeholder',
                                  '100000',
                                )}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? parseFloat(e.target.value)
                                      : undefined,
                                  )
                                }
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:min_order_value_description">
                                Minimum order value required to use this voucher
                                (optional)
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="start_date"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="vouchers:start_date">
                                Start Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="end_date"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="vouchers:end_date">
                                End Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="max_uses"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:max_uses">
                                Maximum Uses
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder={t(
                                  'vouchers:max_uses_placeholder',
                                  '100',
                                )}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? parseInt(e.target.value, 10)
                                      : undefined,
                                  )
                                }
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:max_uses_description">
                                Maximum number of times this voucher can be used
                                (optional)
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {discountType === 'percentage' && (
                        <FormField
                          control={form.control}
                          name="max_discount_value"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                <Trans i18nKey="vouchers:max_discount_value">
                                  Maximum Discount Value
                                </Trans>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder={t(
                                    'vouchers:max_discount_value_placeholder',
                                    '50000',
                                  )}
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      e.target.value
                                        ? parseFloat(e.target.value)
                                        : undefined,
                                    )
                                  }
                                  value={field.value || ''}
                                />
                              </FormControl>
                              <FormDescription>
                                <Trans i18nKey="vouchers:max_discount_value_description">
                                  Maximum discount amount when using percentage
                                  discount (optional)
                                </Trans>
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  </div>

                  {/* Advanced Restrictions */}
                  <div className="mt-8 space-y-4">
                    <div className="flex items-center space-x-2">
                      <div className="rounded-full bg-blue-100 p-2 dark:bg-blue-900/20">
                        <Users className="h-5 w-5 text-blue-600 dark:text-blue-500" />
                      </div>
                      <h3 className="text-lg font-medium">
                        <Trans i18nKey="vouchers:customer_restrictions">
                          Customer Restrictions
                        </Trans>
                      </h3>
                    </div>

                    <FormField
                      control={form.control}
                      name="is_customer_specific"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              <Trans i18nKey="vouchers:is_customer_specific">
                                Customer-specific voucher
                              </Trans>
                            </FormLabel>
                            <FormDescription>
                              <Trans i18nKey="vouchers:is_customer_specific_description">
                                If enabled, this voucher can only be used by
                                specific customers
                              </Trans>
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    {form.watch('is_customer_specific') && (
                      <FormField
                        control={form.control}
                        name="customer_phones"
                        render={({ field }) => (
                          <CustomerPhoneInput
                            value={field.value || []}
                            onChange={field.onChange}
                            disabled={isSubmitting}
                          />
                        )}
                      />
                    )}

                    <FormField
                      control={form.control}
                      name="usage_limit_per_customer"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="vouchers:usage_limit_per_customer">
                              Usage Limit Per Customer
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder={t(
                                'vouchers:usage_limit_per_customer_placeholder',
                                '1',
                              )}
                              {...field}
                              onChange={(e) =>
                                field.onChange(
                                  e.target.value
                                    ? parseInt(e.target.value, 10)
                                    : undefined,
                                )
                              }
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="vouchers:usage_limit_per_customer_description">
                              Maximum number of times a single customer can use
                              this voucher (optional)
                            </Trans>
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="first_time_customers_only"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                <Trans i18nKey="vouchers:first_time_customers_only">
                                  First-time customers only
                                </Trans>
                              </FormLabel>
                              <FormDescription>
                                <Trans i18nKey="vouchers:first_time_customers_only_description">
                                  If enabled, this voucher can only be used by
                                  customers with no previous orders
                                </Trans>
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="min_previous_orders"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:min_previous_orders">
                                Minimum Previous Orders
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder={t(
                                  'vouchers:min_previous_orders_placeholder',
                                  '1',
                                )}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? parseInt(e.target.value, 10)
                                      : undefined,
                                  )
                                }
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:min_previous_orders_description">
                                Minimum number of previous orders required to
                                use this voucher (optional)
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Product and Category Restrictions */}
                    <div className="mt-8 space-y-4">
                      <div className="flex items-center space-x-2">
                        <div className="rounded-full bg-green-100 p-2 dark:bg-green-900/20">
                          <ShoppingBag className="h-5 w-5 text-green-600 dark:text-green-500" />
                        </div>
                        <h3 className="text-lg font-medium">
                          <Trans i18nKey="vouchers:product_restrictions">
                            Product Restrictions
                          </Trans>
                        </h3>
                      </div>

                      <FormField
                        control={form.control}
                        name="included_product_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:included_products')}
                            description={t(
                              'vouchers:included_products_description',
                            )}
                            items={products}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_products')}
                            emptyMessage={t('vouchers:no_products')}
                            disabled={isSubmitting}
                          />
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="excluded_product_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:excluded_products')}
                            description={t(
                              'vouchers:excluded_products_description',
                            )}
                            items={products}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_products')}
                            emptyMessage={t('vouchers:no_products')}
                            disabled={isSubmitting}
                          />
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="included_category_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:included_categories')}
                            description={t(
                              'vouchers:included_categories_description',
                            )}
                            items={categories}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_categories')}
                            emptyMessage={t('vouchers:no_categories')}
                            disabled={isSubmitting}
                          />
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="excluded_category_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:excluded_categories')}
                            description={t(
                              'vouchers:excluded_categories_description',
                            )}
                            items={categories}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_categories')}
                            emptyMessage={t('vouchers:no_categories')}
                            disabled={isSubmitting}
                          />
                        )}
                      />
                    </div>
                  </div>
                </CardContent>

                <CardFooter className="bg-muted/10 flex justify-between border-t px-6 py-4">
                  <Button variant="outline" type="button" asChild>
                    <Link href={`/home/<USER>/vouchers`}>
                      <Trans i18nKey="common:cancel">Cancel</Trans>
                    </Link>
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <Trans i18nKey="common:saving">Saving...</Trans>
                    ) : (
                      <Trans i18nKey="vouchers:save_voucher">
                        Save Voucher
                      </Trans>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </div>
      </PageBody>
    </>
  );
}
