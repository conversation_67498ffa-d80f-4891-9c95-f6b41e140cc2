'use client';

import Link from 'next/link';

import { ArrowUpRight, CheckCircle2, Crown, Zap } from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';

interface SubscriptionCardProps {
  plan: string;
  status: 'active' | 'inactive' | 'trial' | 'expired';
  usagePercent: number;
  daysLeft?: number;
  features: string[];
  nextPlan?: string;
}

// Hàm helper để lấy văn bản trạng thái
function getStatusText(
  status: 'active' | 'inactive' | 'trial' | 'expired',
): string {
  switch (status) {
    case 'active':
      return 'Đang hoạt động';
    case 'inactive':
      return 'Không hoạt động';
    case 'trial':
      return 'Dùng thử';
    case 'expired':
      return 'Hết hạn';
    default:
      return '';
  }
}

export function SubscriptionCard({
  plan,
  status,
  usagePercent,
  daysLeft,
  features,
  nextPlan,
}: SubscriptionCardProps) {
  // <PERSON>ác định màu sắc dựa trên trạng thái
  const statusColors = {
    active:
      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
    trial: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
    expired: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
  };

  // Xác định màu sắc dựa trên phần trăm sử dụng
  const getProgressColor = (percent: number) => {
    if (percent > 90) return 'bg-red-500';
    if (percent > 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Xác định icon cho plan
  const getPlanIcon = (planName: string) => {
    if (planName.toLowerCase().includes('enterprise')) {
      return <Crown className="h-5 w-5 text-yellow-500" />;
    }
    if (planName.toLowerCase().includes('business')) {
      return <Zap className="h-5 w-5 text-purple-500" />;
    }
    return null;
  };

  return (
    <Card className="overflow-hidden border-none bg-gradient-to-br from-white to-slate-50 shadow-md transition-all duration-200 hover:shadow-lg dark:from-slate-900 dark:to-slate-800">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">Gói đăng ký</CardTitle>
          <Badge className={`${statusColors[status]} px-3 py-1 font-medium`}>
            {getStatusText(status)}
          </Badge>
        </div>
        <CardDescription>
          Thông tin về gói đăng ký hiện tại của bạn
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="from-primary/10 to-primary/5 border-primary/10 rounded-xl border bg-gradient-to-r p-4">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getPlanIcon(plan)}
              <div className="text-xl font-bold tracking-tight">{plan}</div>
            </div>
            {daysLeft !== undefined && (
              <div className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm font-medium">
                Còn {daysLeft} ngày
              </div>
            )}
          </div>

          <div className="mb-1 flex items-center justify-between text-sm">
            <span className="font-medium">Lưu lượng sử dụng</span>
            <span className="font-medium">{usagePercent}%</span>
          </div>
          <div className="h-2 w-full overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
            <div
              className={`h-full rounded-full ${getProgressColor(usagePercent)}`}
              style={{ width: `${usagePercent}%` }}
            ></div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center gap-1 text-sm font-medium">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            Tính năng bao gồm
          </div>
          <ul className="grid grid-cols-1 gap-2 text-sm">
            {features.map((feature, index) => (
              <li key={index} className="flex items-center">
                <span className="bg-primary mr-2 h-2 w-2 rounded-full"></span>
                <span className="text-muted-foreground">{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        {nextPlan && (
          <div className="rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4 text-sm dark:border-blue-900/30 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              <span className="font-medium text-blue-800 dark:text-blue-400">
                Nâng cấp lên gói {nextPlan} để có thêm tính năng
              </span>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-0">
        <Button
          asChild
          className="from-primary to-primary/90 hover:from-primary/90 hover:to-primary w-full bg-gradient-to-r shadow-md"
        >
          <Link
            href="/home/<USER>/settings/subscription"
            className="flex items-center justify-center"
          >
            Quản lý gói đăng ký
            <ArrowUpRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
