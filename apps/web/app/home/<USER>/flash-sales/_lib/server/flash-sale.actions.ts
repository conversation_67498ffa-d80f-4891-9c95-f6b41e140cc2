'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { z } from 'zod';

import { CreateFlashSaleParams } from '~/lib/types/flash-sale';

import { createFlashSaleApi } from './flash-sale.api';

const CreateFlashSaleSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  start_time: z.string().transform((val) => new Date(val)),
  end_time: z.string().transform((val) => new Date(val)),
  status: z.enum(['draft', 'active', 'ended', 'cancelled']).default('draft'),
  products: z.array(
    z.object({
      product_id: z.string().uuid(),
      discount_percentage: z.number().min(1).max(100),
      quantity_limit: z.number().optional(),
    }),
  ),
});

export const createFlashSaleAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const api = createFlashSaleApi(client);

    const { accountId, ...flashSaleData } = data;

    const ctx = {
      name: 'flash-sales.create',
      userId: user.id,
      accountId,
    };

    logger.info(ctx, 'Creating flash sale');

    try {
      const params: CreateFlashSaleParams = {
        name: flashSaleData.name,
        description: flashSaleData.description,
        start_time: flashSaleData.start_time,
        end_time: flashSaleData.end_time,
        status: flashSaleData.status,
        products: flashSaleData.products,
      };

      const result = await api.createFlashSale(accountId, params);

      if (!result.success) {
        logger.error({ ...ctx, error: result.message }, 'Failed to create flash sale');
        return { success: false, error: result.message };
      }

      logger.info({ ...ctx, flashSaleId: result.id }, 'Flash sale created successfully');

      revalidatePath(`/home/<USER>/flash-sales`);

      return { success: true, id: result.id };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to create flash sale');
      return { success: false, error: 'Failed to create flash sale' };
    }
  },
  {
    schema: CreateFlashSaleSchema.extend({
      accountId: z.string().uuid(),
      accountSlug: z.string(),
    }),
  },
);

export const updateFlashSaleStatusAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const api = createFlashSaleApi(client);

    const ctx = {
      name: 'flash-sales.update-status',
      userId: user.id,
      flashSaleId: data.id,
      status: data.status,
    };

    logger.info(ctx, 'Updating flash sale status');

    try {
      const result = await api.updateFlashSaleStatus(data.id, data.status);

      if (!result) {
        logger.error(ctx, 'Failed to update flash sale status');
        return { success: false, error: 'Failed to update flash sale status' };
      }

      logger.info(ctx, 'Flash sale status updated successfully');

      revalidatePath(`/home/<USER>/flash-sales`);
      revalidatePath(`/home/<USER>/flash-sales/${data.id}`);

      return { success: true };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to update flash sale status');
      return { success: false, error: 'Failed to update flash sale status' };
    }
  },
  {
    schema: z.object({
      id: z.string().uuid(),
      status: z.enum(['draft', 'active', 'ended', 'cancelled']),
      accountSlug: z.string(),
    }),
  },
);

export const updateFlashSaleAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const api = createFlashSaleApi(client);

    const { accountId, id, ...flashSaleData } = data;

    const ctx = {
      name: 'flash-sales.update',
      userId: user.id,
      accountId,
      flashSaleId: id,
    };

    logger.info(ctx, 'Updating flash sale');

    try {
      const params = {
        name: flashSaleData.name,
        description: flashSaleData.description,
        start_time: flashSaleData.start_time,
        end_time: flashSaleData.end_time,
        status: flashSaleData.status,
        products: flashSaleData.products,
      };

      const result = await api.updateFlashSale(id, accountId, params);

      if (!result.success) {
        logger.error({ ...ctx, error: result.message }, 'Failed to update flash sale');
        return { success: false, error: result.message };
      }

      logger.info(ctx, 'Flash sale updated successfully');

      revalidatePath(`/home/<USER>/flash-sales`);
      revalidatePath(`/home/<USER>/flash-sales/${id}`);

      return { success: true };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to update flash sale');
      return { success: false, error: 'Failed to update flash sale' };
    }
  },
  {
    schema: CreateFlashSaleSchema.extend({
      id: z.string().uuid(),
      accountId: z.string().uuid(),
      accountSlug: z.string(),
      status: z.enum(['draft', 'active', 'ended', 'cancelled']),
    }),
  },
);

export const deleteFlashSaleAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const api = createFlashSaleApi(client);

    const ctx = {
      name: 'flash-sales.delete',
      userId: user.id,
      flashSaleId: data.id,
      accountId: data.accountId,
    };

    logger.info(ctx, 'Deleting flash sale');

    try {
      const result = await api.deleteFlashSale(data.id, data.accountId);

      if (!result.success) {
        logger.error(ctx, 'Failed to delete flash sale');
        return { success: false, error: 'Failed to delete flash sale' };
      }

      logger.info(ctx, 'Flash sale deleted successfully');

      revalidatePath(`/home/<USER>/flash-sales`);

      return { success: true };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to delete flash sale');
      return { success: false, error: 'Failed to delete flash sale' };
    }
  },
  {
    schema: z.object({
      id: z.string().uuid(),
      accountId: z.string().uuid(),
      accountSlug: z.string(),
    }),
  },
);
