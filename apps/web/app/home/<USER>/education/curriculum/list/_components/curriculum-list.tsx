'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Input } from '@kit/ui/input';
import {
  BookOpenCheck,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Filter,
  BookOpen,
  CheckCircle,
  Users
} from 'lucide-react';
import Link from 'next/link';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from '@kit/ui/sonner';

interface Curriculum {
  id: string;
  title: string; // Use title instead of name
  description: string;
  age_group: string;
  duration_minutes: number; // Use duration_minutes instead of duration_weeks
  status: string;
  objectives: string[]; // Use objectives instead of learning_objectives
  activities: string[]; // Use activities instead of skills_focus
  created_at: string;
}

interface Props {
  account: string;
}

function CurriculumList({ account }: Props) {
  const [curriculums, setCurriculums] = useState<Curriculum[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [deleting, setDeleting] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  useEffect(() => {
    loadCurriculums();
  }, []);

  const loadCurriculums = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      const { data, error } = await supabase
        .from('curriculum')
        .select('*')
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCurriculums(data || []);
    } catch (error) {
      console.error('Error loading curriculums:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredCurriculums = curriculums.filter(curriculum => {
    const matchesSearch = curriculum.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         curriculum.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || curriculum.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleDelete = async (curriculumId: string) => {
    const confirmed = window.confirm(
      'Bạn có chắc chắn muốn xóa chương trình học này? Hành động này không thể hoàn tác.'
    );

    if (!confirmed) return;

    try {
      setDeleting(curriculumId);
      const accountId = workspace.account.id;

      const { error: deleteError } = await supabase
        .from('curriculum')
        .delete()
        .eq('id', curriculumId)
        .eq('account_id', accountId);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      toast.success('Chương trình học đã được xóa thành công');
      loadCurriculums(); // Refresh list
    } catch (error: any) {
      console.error('Error deleting curriculum:', error);
      toast.error(error.message || 'Failed to delete curriculum');
    } finally {
      setDeleting(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Đang hoạt động</Badge>;
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800">Bản nháp</Badge>;
      case 'archived':
        return <Badge className="bg-red-100 text-red-800">Đã lưu trữ</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-8">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-teal-600 via-emerald-600 to-green-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <BookOpen className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Danh sách chương trình học</h1>
                  <p className="text-white/90 text-lg">Quản lý tất cả chương trình và lộ trình học tập</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <BookOpen className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{curriculums.length}</div>
                      <div className="text-white/80 text-sm">Tổng chương trình</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{curriculums.filter(c => c.status === 'active').length}</div>
                      <div className="text-white/80 text-sm">Đang hoạt động</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Users className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{filteredCurriculums.length}</div>
                      <div className="text-white/80 text-sm">Kết quả lọc</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Button asChild className="bg-white text-teal-600 hover:bg-white/90 transition-all duration-200">
              <Link href={`/home/<USER>/education/curriculum/new`}>
                <Plus className="h-4 w-4 mr-2" />
                Tạo chương trình mới
              </Link>
            </Button>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm chương trình..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">Tất cả trạng thái</option>
                <option value="active">Đang hoạt động</option>
                <option value="draft">Bản nháp</option>
                <option value="archived">Đã lưu trữ</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Curriculum Grid */}
      {filteredCurriculums.length === 0 ? (
        <Card>
          <CardContent className="p-12">
            <div className="text-center">
              <BookOpenCheck className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm || statusFilter !== 'all' ? 'Không tìm thấy chương trình' : 'Chưa có chương trình học'}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm'
                  : 'Tạo chương trình học đầu tiên để bắt đầu'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button asChild>
                  <Link href={`/home/<USER>/education/curriculum/new`}>
                    <Plus className="h-4 w-4 mr-2" />
                    Tạo chương trình đầu tiên
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCurriculums.map((curriculum) => (
            <Card key={curriculum.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{curriculum.title}</CardTitle>
                    {getStatusBadge(curriculum.status)}
                  </div>
                  <BookOpenCheck className="h-8 w-8 text-blue-600 flex-shrink-0" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {curriculum.description || 'Chưa có mô tả'}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Độ tuổi:</span>
                    <span className="font-medium">{curriculum.age_group || 'Chưa xác định'}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Thời gian:</span>
                    <span className="font-medium">{Math.round((curriculum.duration_minutes || 0) / (7 * 24 * 60))} tuần</span>
                  </div>

                  {curriculum.objectives && curriculum.objectives.length > 0 && (
                    <div>
                      <p className="text-sm text-muted-foreground mb-1">Mục tiêu học tập:</p>
                      <div className="flex flex-wrap gap-1">
                        {curriculum.objectives.slice(0, 2).map((objective, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {objective}
                          </Badge>
                        ))}
                        {curriculum.objectives.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{curriculum.objectives.length - 2} khác
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/home/<USER>/education/curriculum/${curriculum.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/home/<USER>/education/curriculum/${curriculum.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-600 hover:text-red-700"
                      onClick={() => handleDelete(curriculum.id)}
                      disabled={deleting === curriculum.id}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Results count */}
      {filteredCurriculums.length > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          Hiển thị {filteredCurriculums.length} trong tổng số {curriculums.length} chương trình
        </div>
      )}
    </div>
  );
}

export default CurriculumList;
