'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from '@kit/ui/sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import {
  ArrowLeft,
  Edit,
  Trash2,
  BookOpenCheck,
  Clock,
  Users,
  Target,
  Activity,
  BookOpen,
} from 'lucide-react';

interface Props {
  account: string;
  curriculumId: string;
}

interface Curriculum {
  id: string;
  title: string;
  description: string;
  subject: string;
  age_group: string;
  difficulty_level: string;
  duration_minutes: number;
  objectives: string[];
  materials_needed: string[];
  activities: string[];
  assessment_criteria: string[];
  status: string;
  created_at: string;
  updated_at: string;
}

export function CurriculumDetails({ account, curriculumId }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [curriculum, setCurriculum] = useState<Curriculum | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    loadCurriculumDetails();
  }, [curriculumId]);

  const loadCurriculumDetails = async () => {
    try {
      setLoading(true);

      if (!currentAccount?.id) {
        setCurriculum(null);
        return;
      }

      // Load curriculum from database
      const { data: curriculumData, error: curriculumError } = await supabase
        .from('curriculum')
        .select('*')
        .eq('id', curriculumId)
        .eq('account_id', currentAccount.id)
        .single();

      if (curriculumError) {
        throw new Error(curriculumError.message);
      }

      if (!curriculumData) {
        setCurriculum(null);
        return;
      }

      setCurriculum(curriculumData);
    } catch (error: any) {
      console.error('Error loading curriculum details:', error);
      toast.error(error.message || 'Failed to load curriculum details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentAccount?.id || !curriculum) return;

    const confirmed = window.confirm(
      'Bạn có chắc chắn muốn xóa chương trình học này? Hành động này không thể hoàn tác.'
    );

    if (!confirmed) return;

    try {
      setDeleting(true);

      const { error: deleteError } = await supabase
        .from('curriculum')
        .delete()
        .eq('id', curriculumId)
        .eq('account_id', currentAccount.id);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      toast.success('Chương trình học đã được xóa thành công');
      router.push(`/home/<USER>/education/curriculum/list`);
    } catch (error: any) {
      console.error('Error deleting curriculum:', error);
      toast.error(error.message || 'Failed to delete curriculum');
    } finally {
      setDeleting(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Đang hoạt động';
      case 'draft':
        return 'Bản nháp';
      case 'archived':
        return 'Đã lưu trữ';
      default:
        return status;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Đang hoạt động</Badge>;
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800">Bản nháp</Badge>;
      case 'archived':
        return <Badge className="bg-red-100 text-red-800">Đã lưu trữ</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDifficultyBadge = (level: string) => {
    switch (level) {
      case 'beginner':
        return <Badge className="bg-blue-100 text-blue-800">Cơ bản</Badge>;
      case 'intermediate':
        return <Badge className="bg-yellow-100 text-yellow-800">Trung bình</Badge>;
      case 'advanced':
        return <Badge className="bg-red-100 text-red-800">Nâng cao</Badge>;
      default:
        return <Badge variant="outline">{level}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (!curriculum) {
    return <div className="text-center py-8 text-red-600">Curriculum not found</div>;
  }

  return (
    <div className="space-y-8">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lại</span>
        </Button>

        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/home/<USER>/education/curriculum/${curriculumId}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Link>
          </Button>
          <Button
            variant="outline"
            className="text-red-600 hover:text-red-700"
            onClick={handleDelete}
            disabled={deleting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {deleting ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </div>
      </div>

      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-600 via-emerald-600 to-teal-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <BookOpen className="h-6 w-6" />
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    📚 {getStatusText(curriculum.status)}
                  </Badge>
                  {getDifficultyBadge(curriculum.difficulty_level)}
                </div>
              </div>
              <h1 className="text-4xl font-bold mb-2 leading-tight">{curriculum.title}</h1>
              <div className="text-white/90 text-lg space-y-1">
                <p>👶 Độ tuổi: <span className="font-semibold">{curriculum.age_group || 'Chưa xác định'}</span></p>
                <p>⏱️ Thời gian: <span className="font-semibold">{curriculum.duration_minutes} phút</span></p>
                <p>📚 Môn học: <span className="font-semibold">{curriculum.subject || 'Chưa xác định'}</span></p>
                <p>📅 Tạo lúc: <span className="font-semibold">{formatDateTime(curriculum.created_at)}</span></p>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Curriculum Details */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-xl">{curriculum.title}</CardTitle>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                <span>Môn học: {curriculum.subject || 'Chưa xác định'}</span>
                <span>•</span>
                <span>Tạo lúc: {formatDateTime(curriculum.created_at)}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <BookOpenCheck className="h-6 w-6 text-blue-600" />
              {getStatusBadge(curriculum.status)}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Mô tả</h4>
              <div className="p-4 bg-gray-50 rounded-lg">
                {curriculum.description || 'Chưa có mô tả'}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">Thông tin cơ bản</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Độ tuổi:</span>
                    <span>{curriculum.age_group || 'Chưa xác định'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Độ khó:</span>
                    {getDifficultyBadge(curriculum.difficulty_level)}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Thời gian:</span>
                    <span>{curriculum.duration_minutes} phút</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Objectives */}
      {curriculum.objectives && curriculum.objectives.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Mục tiêu học tập</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {curriculum.objectives.map((objective, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-blue-600 mt-1">•</span>
                  <span>{objective}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Activities */}
      {curriculum.activities && curriculum.activities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Hoạt động</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {curriculum.activities.map((activity, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-green-600 mt-1">•</span>
                  <span>{activity}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
