'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from '@kit/ui/sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Plus, X, ArrowLeft } from 'lucide-react';

interface Props {
  account: string;
  curriculumId: string;
}

interface Curriculum {
  id: string;
  title: string;
  description: string;
  subject: string;
  age_group: string;
  difficulty_level: string;
  duration_minutes: number;
  objectives: string[];
  materials_needed: string[];
  activities: string[];
  assessment_criteria: string[];
  status: string;
}

export function EditCurriculumForm({ account, curriculumId }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [curriculum, setCurriculum] = useState<Curriculum | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    subject: '',
    age_group: '',
    difficulty_level: 'beginner',
    duration_minutes: 30,
    status: 'draft',
    objectives: [] as string[],
    materials_needed: [] as string[],
    activities: [] as string[],
    assessment_criteria: [] as string[],
  });

  const [newObjective, setNewObjective] = useState('');
  const [newMaterial, setNewMaterial] = useState('');
  const [newActivity, setNewActivity] = useState('');
  const [newCriteria, setNewCriteria] = useState('');

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    loadCurriculum();
  }, [curriculumId]);

  const loadCurriculum = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!currentAccount?.id) {
        setCurriculum(null);
        return;
      }

      const { data: curriculumData, error: curriculumError } = await supabase
        .from('curriculum')
        .select('*')
        .eq('id', curriculumId)
        .eq('account_id', currentAccount.id)
        .single();

      if (curriculumError) {
        throw new Error(curriculumError.message);
      }

      if (!curriculumData) {
        setCurriculum(null);
        return;
      }

      setCurriculum(curriculumData);

      // Populate form data
      setFormData({
        title: curriculumData.title || '',
        description: curriculumData.description || '',
        subject: curriculumData.subject || '',
        age_group: curriculumData.age_group || '',
        difficulty_level: curriculumData.difficulty_level || 'beginner',
        duration_minutes: curriculumData.duration_minutes || 30,
        status: curriculumData.status || 'draft',
        objectives: curriculumData.objectives || [],
        materials_needed: curriculumData.materials_needed || [],
        activities: curriculumData.activities || [],
        assessment_criteria: curriculumData.assessment_criteria || [],
      });
    } catch (err: any) {
      console.error('Error loading curriculum:', err);
      setError(err.message || 'Failed to load curriculum');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      if (!currentAccount?.id) {
        throw new Error('Account not found');
      }

      const { error: updateError } = await supabase
        .from('curriculum')
        .update({
          title: formData.title,
          description: formData.description,
          subject: formData.subject,
          age_group: formData.age_group,
          difficulty_level: formData.difficulty_level,
          duration_minutes: formData.duration_minutes,
          status: formData.status,
          objectives: formData.objectives,
          materials_needed: formData.materials_needed,
          activities: formData.activities,
          assessment_criteria: formData.assessment_criteria,
          updated_at: new Date().toISOString(),
        })
        .eq('id', curriculumId)
        .eq('account_id', currentAccount.id);

      if (updateError) throw updateError;

      toast.success('Chương trình học đã được cập nhật thành công');
      router.push(`/home/<USER>/education/curriculum/${curriculumId}`);
    } catch (err: any) {
      console.error('Error updating curriculum:', err);
      setError(err.message || 'Có lỗi xảy ra khi cập nhật chương trình học');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addObjective = () => {
    if (newObjective.trim()) {
      setFormData((prev) => ({
        ...prev,
        objectives: [...prev.objectives, newObjective.trim()],
      }));
      setNewObjective('');
    }
  };

  const removeObjective = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      objectives: prev.objectives.filter((_, i) => i !== index),
    }));
  };

  const addMaterial = () => {
    if (newMaterial.trim()) {
      setFormData((prev) => ({
        ...prev,
        materials_needed: [...prev.materials_needed, newMaterial.trim()],
      }));
      setNewMaterial('');
    }
  };

  const removeMaterial = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      materials_needed: prev.materials_needed.filter((_, i) => i !== index),
    }));
  };

  const addActivity = () => {
    if (newActivity.trim()) {
      setFormData((prev) => ({
        ...prev,
        activities: [...prev.activities, newActivity.trim()],
      }));
      setNewActivity('');
    }
  };

  const removeActivity = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      activities: prev.activities.filter((_, i) => i !== index),
    }));
  };

  const addCriteria = () => {
    if (newCriteria.trim()) {
      setFormData((prev) => ({
        ...prev,
        assessment_criteria: [...prev.assessment_criteria, newCriteria.trim()],
      }));
      setNewCriteria('');
    }
  };

  const removeCriteria = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      assessment_criteria: prev.assessment_criteria.filter((_, i) => i !== index),
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (!curriculum) {
    return <div className="text-center py-8 text-red-600">Curriculum not found</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lại</span>
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Thông tin cơ bản</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Tên chương trình *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Nhập tên chương trình học"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Mô tả chi tiết về chương trình học"
                rows={4}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="subject">Môn học</Label>
                <Input
                  id="subject"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  placeholder="Ví dụ: Toán học, Ngôn ngữ"
                />
              </div>
              <div>
                <Label htmlFor="age_group">Độ tuổi</Label>
                <Select
                  value={formData.age_group}
                  onValueChange={(value) => handleInputChange('age_group', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn độ tuổi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="toddler">Nhũ nhi (1-2 tuổi)</SelectItem>
                    <SelectItem value="preschool">Mẫu giáo (3-4 tuổi)</SelectItem>
                    <SelectItem value="kindergarten">Mầm non (5-6 tuổi)</SelectItem>
                    <SelectItem value="mixed">Hỗn hợp</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="difficulty_level">Độ khó</Label>
                <Select
                  value={formData.difficulty_level}
                  onValueChange={(value) => handleInputChange('difficulty_level', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Cơ bản</SelectItem>
                    <SelectItem value="intermediate">Trung bình</SelectItem>
                    <SelectItem value="advanced">Nâng cao</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="duration_minutes">Thời gian (phút)</Label>
                <Input
                  id="duration_minutes"
                  type="number"
                  value={formData.duration_minutes}
                  onChange={(e) =>
                    handleInputChange('duration_minutes', parseInt(e.target.value) || 30)
                  }
                  min="1"
                  max="480"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="status">Trạng thái</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Bản nháp</SelectItem>
                  <SelectItem value="active">Đang hoạt động</SelectItem>
                  <SelectItem value="archived">Đã lưu trữ</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Objectives */}
        <Card>
          <CardHeader>
            <CardTitle>Mục tiêu học tập</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newObjective}
                onChange={(e) => setNewObjective(e.target.value)}
                placeholder="Thêm mục tiêu học tập"
                onKeyPress={(e) =>
                  e.key === 'Enter' && (e.preventDefault(), addObjective())
                }
              />
              <Button type="button" onClick={addObjective} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.objectives.length > 0 && (
              <div className="space-y-2">
                {formData.objectives.map((objective, index) => (
                  <div
                    key={index}
                    className="bg-muted flex items-center justify-between rounded-lg p-2"
                  >
                    <span className="text-sm">{objective}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeObjective(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Hoạt động</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newActivity}
                onChange={(e) => setNewActivity(e.target.value)}
                placeholder="Thêm hoạt động"
                onKeyPress={(e) =>
                  e.key === 'Enter' && (e.preventDefault(), addActivity())
                }
              />
              <Button type="button" onClick={addActivity} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.activities.length > 0 && (
              <div className="space-y-2">
                {formData.activities.map((activity, index) => (
                  <div
                    key={index}
                    className="bg-muted flex items-center justify-between rounded-lg p-2"
                  >
                    <span className="text-sm">{activity}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeActivity(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {error && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(`/home/<USER>/education/curriculum/${curriculumId}`)}
          >
            Hủy
          </Button>
          <Button type="submit" disabled={saving}>
            {saving ? 'Đang cập nhật...' : 'Cập nhật chương trình'}
          </Button>
        </div>
      </form>
    </div>
  );
}
