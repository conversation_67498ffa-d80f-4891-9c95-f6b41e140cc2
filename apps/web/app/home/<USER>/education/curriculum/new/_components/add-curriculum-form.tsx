'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { Plus, X } from 'lucide-react';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface Props {
  account: string;
}

function AddCurriculumForm({ account }: Props) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    age_group: '',
    duration_weeks: 12,
    status: 'draft',
    learning_objectives: [] as string[],
    skills_focus: [] as string[],
  });

  const [newObjective, setNewObjective] = useState('');
  const [newSkill, setNewSkill] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      const { error: insertError } = await supabase.from('curriculum').insert({
        account_id: accountId,
        title: formData.name, // Map name to title
        description: formData.description,
        age_group: formData.age_group,
        duration_minutes: formData.duration_weeks * 7 * 24 * 60, // Convert weeks to minutes
        status: formData.status,
        objectives: formData.learning_objectives, // Map to objectives JSONB
        activities: formData.skills_focus, // Map skills_focus to activities JSONB
      });

      if (insertError) throw insertError;

      toast.success('Chương trình học đã được tạo thành công');
      router.push(`/home/<USER>/education/curriculum/list`);
    } catch (err: any) {
      console.error('Error creating curriculum:', err);
      setError(err.message || 'Có lỗi xảy ra khi tạo chương trình học');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addObjective = () => {
    if (newObjective.trim()) {
      setFormData((prev) => ({
        ...prev,
        learning_objectives: [...prev.learning_objectives, newObjective.trim()],
      }));
      setNewObjective('');
    }
  };

  const removeObjective = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      learning_objectives: prev.learning_objectives.filter(
        (_, i) => i !== index,
      ),
    }));
  };

  const addSkill = () => {
    if (newSkill.trim()) {
      setFormData((prev) => ({
        ...prev,
        skills_focus: [...prev.skills_focus, newSkill.trim()],
      }));
      setNewSkill('');
    }
  };

  const removeSkill = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      skills_focus: prev.skills_focus.filter((_, i) => i !== index),
    }));
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Thông tin cơ bản</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Tên chương trình *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên chương trình học"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange('description', e.target.value)
                }
                placeholder="Mô tả chi tiết về chương trình học"
                rows={4}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="age_group">Độ tuổi</Label>
                <Select
                  value={formData.age_group}
                  onValueChange={(value) =>
                    handleInputChange('age_group', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn độ tuổi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="toddler">Nhũ nhi (1-2 tuổi)</SelectItem>
                    <SelectItem value="preschool">
                      Mẫu giáo (3-4 tuổi)
                    </SelectItem>
                    <SelectItem value="kindergarten">
                      Mầm non (5-6 tuổi)
                    </SelectItem>
                    <SelectItem value="mixed">Hỗn hợp</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="duration_weeks">Thời gian (tuần)</Label>
                <Input
                  id="duration_weeks"
                  type="number"
                  value={formData.duration_weeks}
                  onChange={(e) =>
                    handleInputChange(
                      'duration_weeks',
                      parseInt(e.target.value) || 12,
                    )
                  }
                  min="1"
                  max="52"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="status">Trạng thái</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Bản nháp</SelectItem>
                  <SelectItem value="active">Đang hoạt động</SelectItem>
                  <SelectItem value="archived">Đã lưu trữ</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Mục tiêu học tập</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newObjective}
                onChange={(e) => setNewObjective(e.target.value)}
                placeholder="Thêm mục tiêu học tập"
                onKeyPress={(e) =>
                  e.key === 'Enter' && (e.preventDefault(), addObjective())
                }
              />
              <Button type="button" onClick={addObjective} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.learning_objectives.length > 0 && (
              <div className="space-y-2">
                {formData.learning_objectives.map((objective, index) => (
                  <div
                    key={index}
                    className="bg-muted flex items-center justify-between rounded-lg p-2"
                  >
                    <span className="text-sm">{objective}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeObjective(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Kỹ năng trọng tâm</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newSkill}
                onChange={(e) => setNewSkill(e.target.value)}
                placeholder="Thêm kỹ năng trọng tâm"
                onKeyPress={(e) =>
                  e.key === 'Enter' && (e.preventDefault(), addSkill())
                }
              />
              <Button type="button" onClick={addSkill} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.skills_focus.length > 0 && (
              <div className="space-y-2">
                {formData.skills_focus.map((skill, index) => (
                  <div
                    key={index}
                    className="bg-muted flex items-center justify-between rounded-lg p-2"
                  >
                    <span className="text-sm">{skill}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSkill(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {error && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              router.push(`/home/<USER>/education/curriculum/list`)
            }
          >
            Hủy
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Đang tạo...' : 'Tạo chương trình'}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default AddCurriculumForm;
