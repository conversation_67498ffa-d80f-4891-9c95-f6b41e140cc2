'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  Plus,
  Search,
  Eye,
  Edit,
  Calendar,
  MapPin,
  Users,
  Clock,
  Filter,
  Download,
  CheckCircle,
} from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface Props {
  account: string;
}

interface Event {
  id: string;
  title: string;
  description: string;
  event_type: string;
  start_datetime: string;
  end_datetime: string;
  location: string;
  max_participants?: number;
  registration_required: boolean;
  registration_deadline?: string;
  status: string;
  created_at: string;
  _count?: {
    registrations: number;
  };
}

export function EventsManagement({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;
    loadEvents();
  }, [currentAccount?.id, supabase]);

  const loadEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setEvents([]);
        return;
      }

      // Get events
      const { data: eventsData, error: eventsError } = await supabase
        .from('events')
        .select('*')
        .eq('account_id', accountId)
        .order('start_datetime', { ascending: true });

      if (eventsError) throw eventsError;

      // For now, add mock registration count since we don't have registrations table yet
      const processedEvents = eventsData?.map(event => ({
        ...event,
        _count: {
          registrations: Math.floor(Math.random() * (event.max_participants || 50)),
        }
      })) || [];

      setEvents(processedEvents);
    } catch (err: any) {
      console.error('Error loading events:', err);
      setError(err.message || 'Failed to load events');
    } finally {
      setLoading(false);
    }
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.location.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || event.status === statusFilter;
    const matchesType = typeFilter === 'all' || event.event_type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusBadge = (status: string) => {
    const statusMap = {
      scheduled: { label: t('events.status.scheduled', 'Scheduled'), variant: 'default' as const },
      ongoing: { label: t('events.status.ongoing', 'Ongoing'), variant: 'secondary' as const },
      completed: { label: t('events.status.completed', 'Completed'), variant: 'outline' as const },
      cancelled: { label: t('events.status.cancelled', 'Cancelled'), variant: 'destructive' as const },
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };
    
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    );
  };

  const getEventTypeText = (eventType: string) => {
    return t(`events.eventType.${eventType}`, eventType);
  };

  const formatDateTime = (dateTimeString: string) => {
    return new Date(dateTimeString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateTimeString: string) => {
    return new Date(dateTimeString).toLocaleDateString('vi-VN');
  };

  const isUpcoming = (startDateTime: string) => {
    return new Date(startDateTime) > new Date();
  };

  const calculateStats = () => {
    const total = events.length;
    const upcoming = events.filter(e => isUpcoming(e.start_datetime)).length;
    const completed = events.filter(e => e.status === 'completed').length;
    const totalRegistrations = events.reduce((sum, e) => sum + (e._count?.registrations || 0), 0);

    return { total, upcoming, completed, totalRegistrations };
  };

  const stats = calculateStats();

  if (loading) {
    return <div className="flex justify-center py-8">{t('common.loading')}</div>;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={loadEvents} variant="outline">
          {t('common.refresh')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 via-violet-600 to-indigo-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Calendar className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Quản lý sự kiện</h1>
                  <p className="text-white/90 text-lg">Tổ chức và theo dõi các hoạt động trường học</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.total}</div>
                      <div className="text-white/80 text-sm">Tổng sự kiện</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.upcoming}</div>
                      <div className="text-white/80 text-sm">Sắp diễn ra</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.completed}</div>
                      <div className="text-white/80 text-sm">Đã hoàn thành</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Users className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.totalRegistrations}</div>
                      <div className="text-white/80 text-sm">Người tham gia</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Search className="h-5 w-5 text-purple-600" />
            </div>
            Tìm kiếm và lọc sự kiện
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Tìm kiếm sự kiện..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64 h-12 bg-white border-2 border-gray-200 hover:border-purple-300 transition-colors"
                />
              </div>

              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="h-12 px-4 py-2 bg-white border-2 border-gray-200 hover:border-purple-300 rounded-md text-sm transition-colors"
              >
                <option value="all">Tất cả trạng thái</option>
                <option value="scheduled">Đã lên lịch</option>
                <option value="ongoing">Đang diễn ra</option>
                <option value="completed">Đã hoàn thành</option>
                <option value="cancelled">Đã hủy</option>
              </select>

              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="h-12 px-4 py-2 bg-white border-2 border-gray-200 hover:border-purple-300 rounded-md text-sm transition-colors"
              >
                <option value="all">Tất cả loại</option>
                <option value="academic">Học thuật</option>
                <option value="extracurricular">Ngoại khóa</option>
                <option value="parent_meeting">Họp phụ huynh</option>
                <option value="celebration">Lễ kỷ niệm</option>
                <option value="field_trip">Dã ngoại</option>
                <option value="other">Khác</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" className="h-12">
                <Download className="h-4 w-4 mr-2" />
                Xuất dữ liệu
              </Button>
              <Button asChild className="h-12 bg-purple-600 hover:bg-purple-700 transition-all duration-200">
                <Link href={`/home/<USER>/education/events/new`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm sự kiện mới
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>



      {/* Events Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('events.title')} ({filteredEvents.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('events.table.event', 'Event')}</TableHead>
                <TableHead>{t('events.table.type', 'Type')}</TableHead>
                <TableHead>{t('events.table.datetime', 'Date & Time')}</TableHead>
                <TableHead>{t('events.table.location', 'Location')}</TableHead>
                <TableHead>{t('events.table.participants', 'Participants')}</TableHead>
                <TableHead>{t('events.table.status', 'Status')}</TableHead>
                <TableHead>{t('events.table.actions', 'Actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEvents.map((event) => (
                <TableRow key={event.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{event.title}</div>
                      <div className="text-sm text-gray-500 line-clamp-2">
                        {event.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getEventTypeText(event.event_type)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1 text-sm">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span>{formatDate(event.start_datetime)}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>
                          {new Date(event.start_datetime).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })} - 
                          {new Date(event.end_datetime).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{event.location}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">
                        {event._count?.registrations || 0}
                        {event.max_participants && `/${event.max_participants}`}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(event.status)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/events/${event.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/events/${event.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredEvents.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                ? t('events.emptyState.noResults', 'No events found matching the filters.')
                : t('events.emptyState.noEvents', 'No events yet. Add your first event!')
              }
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
