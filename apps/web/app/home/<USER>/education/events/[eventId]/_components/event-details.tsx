'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { Calendar, Clock, DollarSign, Edit, MapPin, Users } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Separator } from '@kit/ui/separator';

interface Event {
  id: string;
  title: string;
  description: string;
  event_type: string;
  start_datetime: string;
  end_datetime: string;
  location: string;
  target_audience: string;
  program_ids: string[];
  registration_required: boolean;
  max_participants: number;
  current_participants: number;
  fee: number;
  images: string[];
  metadata: any;
  created_by: string;
  created_at: string;
}

interface Props {
  account: string;
  eventId: string;
}

function EventDetails({ account, eventId }: Props) {
  const { t } = useTranslation('education');
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  useEffect(() => {
    loadEvent();
  }, [eventId]);

  const loadEvent = async () => {
    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      const { data: event, error: eventError } = await supabase
        .from('events')
        .select('*')
        .eq('id', eventId)
        .eq('account_id', accountId)
        .single();

      if (eventError) throw eventError;

      setEvent(event);
    } catch (err: any) {
      console.error('Error loading event:', err);
      setError(err.message || 'Failed to load event');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/home/<USER>/education/events/${eventId}/edit`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-600">{error}</p>
        <Button onClick={loadEvent} className="mt-4">
          Thử lại
        </Button>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">Không tìm thấy sự kiện</p>
      </div>
    );
  }

  const getEventTypeText = (type: string) => {
    switch (type) {
      case 'meeting':
        return 'Họp phụ huynh';
      case 'performance':
        return 'Biểu diễn';
      case 'field_trip':
        return 'Dã ngoại';
      case 'workshop':
        return 'Hội thảo';
      case 'celebration':
        return 'Lễ kỷ niệm';
      default:
        return type;
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'meeting':
        return 'bg-blue-100 text-blue-800';
      case 'performance':
        return 'bg-purple-100 text-purple-800';
      case 'field_trip':
        return 'bg-green-100 text-green-800';
      case 'workshop':
        return 'bg-orange-100 text-orange-800';
      case 'celebration':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isUpcoming = new Date(event.start_datetime) > new Date();
  const isPast = new Date(event.end_datetime) < new Date();
  const isOngoing = !isUpcoming && !isPast;

  const getEventStatus = () => {
    if (isPast)
      return { text: 'Đã kết thúc', color: 'bg-gray-100 text-gray-800' };
    if (isOngoing)
      return { text: 'Đang diễn ra', color: 'bg-green-100 text-green-800' };
    return { text: 'Sắp diễn ra', color: 'bg-blue-100 text-blue-800' };
  };

  const eventStatus = getEventStatus();

  return (
    <div className="space-y-8">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-violet-600 via-purple-600 to-fuchsia-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Calendar className="h-6 w-6" />
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    🎉 {getEventTypeText(event.event_type)}
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    {eventStatus.text}
                  </Badge>
                </div>
              </div>
              <h1 className="text-4xl font-bold mb-2 leading-tight">{event.title}</h1>
              <div className="text-white/90 text-lg space-y-1">
                <p>📅 Ngày: <span className="font-semibold">{new Date(event.start_datetime).toLocaleDateString('vi-VN')}</span></p>
                <p>⏰ Thời gian: <span className="font-semibold">{new Date(event.start_datetime).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}</span></p>
                <p>📍 Địa điểm: <span className="font-semibold">{event.location || 'Chưa xác định'}</span></p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleEdit}
                variant="outline"
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm transition-all duration-200"
              >
                <Edit className="h-4 w-4 mr-2" />
                Chỉnh sửa
              </Button>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Info */}
        <div className="space-y-6 lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Mô tả sự kiện</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm whitespace-pre-wrap">
                {event.description || 'Chưa có mô tả'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Thời gian & Địa điểm</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                    Bắt đầu
                  </h4>
                  <p className="text-sm">
                    {new Date(event.start_datetime).toLocaleString('vi-VN')}
                  </p>
                </div>
                <div>
                  <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                    Kết thúc
                  </h4>
                  <p className="text-sm">
                    {event.end_datetime
                      ? new Date(event.end_datetime).toLocaleString('vi-VN')
                      : 'Chưa xác định'}
                  </p>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="text-muted-foreground mb-2 flex items-center space-x-1 text-sm font-medium">
                  <MapPin className="h-3 w-3" />
                  <span>Địa điểm</span>
                </h4>
                <p className="text-sm">{event.location || 'Chưa xác định'}</p>
              </div>

              {event.end_datetime && (
                <>
                  <Separator />
                  <div>
                    <h4 className="text-muted-foreground mb-2 flex items-center space-x-1 text-sm font-medium">
                      <Clock className="h-3 w-3" />
                      <span>Thời lượng</span>
                    </h4>
                    <p className="text-sm">
                      {Math.round(
                        (new Date(event.end_datetime).getTime() -
                          new Date(event.start_datetime).getTime()) /
                          (1000 * 60 * 60),
                      )}{' '}
                      giờ
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Images */}
          {event.images && event.images.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Hình ảnh</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
                  {event.images.map((image, index) => (
                    <div
                      key={index}
                      className="bg-muted aspect-square overflow-hidden rounded-lg"
                    >
                      <img
                        src={image}
                        alt={`Event image ${index + 1}`}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Thông tin tham gia</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {event.current_participants || 0}
                </div>
                <div className="text-muted-foreground text-sm">
                  / {event.max_participants || 'Không giới hạn'} người tham gia
                </div>
              </div>

              {event.max_participants && (
                <div className="bg-muted h-2 w-full rounded-full">
                  <div
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{
                      width: `${Math.min(100, ((event.current_participants || 0) / event.max_participants) * 100)}%`,
                    }}
                  ></div>
                </div>
              )}

              <Separator />

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Yêu cầu đăng ký:</span>
                  <span>{event.registration_required ? 'Có' : 'Không'}</span>
                </div>

                {event.target_audience && (
                  <div className="flex justify-between">
                    <span>Đối tượng:</span>
                    <span>{event.target_audience}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {event.fee > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4" />
                  <span>Phí tham gia</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-primary text-2xl font-bold">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                    }).format(event.fee)}
                  </div>
                  <div className="text-muted-foreground text-sm">mỗi người</div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Thông tin khác</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-2 text-sm">
                <Calendar className="text-muted-foreground h-4 w-4" />
                <span>
                  Tạo: {new Date(event.created_at).toLocaleDateString('vi-VN')}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default EventDetails;
