'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import {
  ArrowLeft,
  BarChart3,
  Calendar,
  DollarSign,
  Download,
  Printer,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { toast } from '@kit/ui/sonner';



interface Props {
  account: string;
}

interface MonthlyData {
  month: string;
  year: number;
  learners: {
    total: number;
    new: number;
    active: number;
    graduated: number;
  };
  attendance: {
    rate: number;
    totalSessions: number;
    presentCount: number;
  };
  fees: {
    totalAmount: number;
    paidAmount: number;
    pendingAmount: number;
    overdueAmount: number;
  };
  programs: {
    total: number;
    active: number;
    enrollmentRate: number;
  };
  events: {
    total: number;
    completed: number;
    participation: number;
  };
}

export function MonthlyReportView({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [reportData, setReportData] = useState<MonthlyData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState(
    new Date().toISOString().slice(0, 7),
  ); // YYYY-MM format

  const currentAccount = accounts?.find((acc) => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;
    loadMonthlyReport();
  }, [currentAccount?.id, selectedMonth, supabase]);

  const loadMonthlyReport = async () => {
    try {
      setLoading(true);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setReportData(null);
        return;
      }
      const [year, month] = selectedMonth.split('-').map(Number);
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);

      // Load data for the selected month
      const [
        learnersResult,
        attendanceResult,
        feesResult,
        programsResult,
        eventsResult,
      ] = await Promise.all([
        // Learners data
        supabase
          .from('learners')
          .select('id, status, created_at')
          .eq('account_id', accountId),

        // Attendance data
        supabase
          .from('attendance')
          .select('id, status, session_date, learners!inner(account_id)')
          .eq('learners.account_id', accountId)
          .gte('session_date', startDate.toISOString().split('T')[0])
          .lte('session_date', endDate.toISOString().split('T')[0]),

        // Fees data
        supabase
          .from('fees')
          .select('id, status, amount, due_date, payment_date')
          .eq('account_id', accountId),

        // Programs data
        supabase
          .from('programs')
          .select('id, status, capacity')
          .eq('account_id', accountId),

        // Events data (mock for now)
        Promise.resolve({ data: [], error: null }),
      ]);

      // Process the data
      const learners = learnersResult.data || [];
      const attendance = attendanceResult.data || [];
      const fees = feesResult.data || [];
      const programs = programsResult.data || [];

      // Calculate learner statistics
      const totalLearners = learners.length;
      const activeLearners = learners.filter(
        (l) => l.status === 'active',
      ).length;
      const newLearners = learners.filter((l) => {
        const createdDate = new Date(l.created_at);
        return createdDate >= startDate && createdDate <= endDate;
      }).length;

      // Calculate attendance statistics
      const totalSessions = attendance.length;
      const presentCount = attendance.filter(
        (a) => a.status === 'present',
      ).length;
      const attendanceRate =
        totalSessions > 0
          ? Math.round((presentCount / totalSessions) * 100)
          : 0;

      // Calculate fee statistics
      const totalAmount = fees.reduce((sum, f) => sum + (f.amount || 0), 0);
      const paidAmount = fees
        .filter((f) => f.status === 'paid')
        .reduce((sum, f) => sum + (f.amount || 0), 0);
      const pendingAmount = fees
        .filter((f) => f.status === 'pending')
        .reduce((sum, f) => sum + (f.amount || 0), 0);
      const overdueAmount = fees
        .filter((f) => f.status === 'overdue')
        .reduce((sum, f) => sum + (f.amount || 0), 0);

      // Calculate program statistics
      const totalPrograms = programs.length;
      const activePrograms = programs.filter(
        (p) => p.status === 'active',
      ).length;
      const totalCapacity = programs.reduce(
        (sum, p) => sum + (p.capacity || 0),
        0,
      );
      const enrollmentRate =
        totalCapacity > 0
          ? Math.round((activeLearners / totalCapacity) * 100)
          : 0;

      setReportData({
        month: new Date(year, month - 1).toLocaleDateString('vi-VN', {
          month: 'long',
        }),
        year,
        learners: {
          total: totalLearners,
          new: newLearners,
          active: activeLearners,
          graduated: 0, // Would need graduation data
        },
        attendance: {
          rate: attendanceRate,
          totalSessions,
          presentCount,
        },
        fees: {
          totalAmount,
          paidAmount,
          pendingAmount,
          overdueAmount,
        },
        programs: {
          total: totalPrograms,
          active: activePrograms,
          enrollmentRate,
        },
        events: {
          total: 5, // Mock data
          completed: 3,
          participation: 85,
        },
      });
    } catch (error: any) {
      console.error('Error loading monthly report:', error);
      toast.error('Failed to load monthly report');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'pdf' | 'excel') => {
    try {
      toast.success(
        t('reports.exportStarted', `${format.toUpperCase()} export started`),
      );

      // In real implementation, this would call an API to generate the report
      // For now, we'll simulate the export
      setTimeout(() => {
        toast.success(
          t(
            'reports.exportCompleted',
            `${format.toUpperCase()} export completed`,
          ),
        );
      }, 2000);
    } catch (error) {
      toast.error(t('reports.exportFailed', 'Export failed'));
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">{t('common.loading')}</div>
    );
  }

  if (!reportData) {
    return (
      <div className="py-8 text-center text-red-600">
        No data available for this month
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="no-print flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>{t('common.back')}</span>
          </Button>

          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <Input
              type="month"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className="w-auto"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            {t('reports.print', 'Print')}
          </Button>
          <Button variant="outline" onClick={() => handleExport('excel')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>
          <Button onClick={() => handleExport('pdf')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>
        </div>
      </div>

      {/* Report Header */}
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">
            {t('reports.monthlyReportTitle', 'Monthly Report')} -{' '}
            {reportData.month} {reportData.year}
          </CardTitle>
          <p className="text-gray-600">
            {t('reports.generatedOn', 'Generated on')}:{' '}
            {new Date().toLocaleDateString('vi-VN')}
          </p>
        </CardHeader>
      </Card>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium text-gray-600">
              <Users className="mr-2 h-4 w-4" />
              {t('reports.learners', 'Learners')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reportData.learners.total}
            </div>
            <div className="text-sm text-green-600">
              +{reportData.learners.new}{' '}
              {t('reports.newThisMonth', 'new this month')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium text-gray-600">
              <TrendingUp className="mr-2 h-4 w-4" />
              {t('reports.attendance', 'Attendance')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reportData.attendance.rate}%
            </div>
            <div className="text-sm text-gray-500">
              {reportData.attendance.presentCount}/
              {reportData.attendance.totalSessions}{' '}
              {t('reports.sessions', 'sessions')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium text-gray-600">
              <DollarSign className="mr-2 h-4 w-4" />
              {t('reports.revenue', 'Revenue')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(reportData.fees.paidAmount)}
            </div>
            <div className="text-sm text-gray-500">
              {Math.round(
                (reportData.fees.paidAmount / reportData.fees.totalAmount) *
                  100,
              )}
              % {t('reports.collected', 'collected')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium text-gray-600">
              <BarChart3 className="mr-2 h-4 w-4" />
              {t('reports.programs', 'Programs')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reportData.programs.active}
            </div>
            <div className="text-sm text-gray-500">
              {reportData.programs.enrollmentRate}%{' '}
              {t('reports.enrollment', 'enrollment')}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Sections */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Learner Details */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t('reports.learnerDetails', 'Learner Details')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>{t('reports.totalLearners', 'Total Learners')}</span>
                <span className="font-semibold">
                  {reportData.learners.total}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.activeLearners', 'Active Learners')}</span>
                <span className="font-semibold text-green-600">
                  {reportData.learners.active}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.newEnrollments', 'New Enrollments')}</span>
                <span className="font-semibold text-blue-600">
                  {reportData.learners.new}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.graduations', 'Graduations')}</span>
                <span className="font-semibold text-purple-600">
                  {reportData.learners.graduated}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial Summary */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t('reports.financialSummary', 'Financial Summary')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>{t('reports.totalFees', 'Total Fees')}</span>
                <span className="font-semibold">
                  {formatCurrency(reportData.fees.totalAmount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.paidAmount', 'Paid Amount')}</span>
                <span className="font-semibold text-green-600">
                  {formatCurrency(reportData.fees.paidAmount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.pendingAmount', 'Pending Amount')}</span>
                <span className="font-semibold text-yellow-600">
                  {formatCurrency(reportData.fees.pendingAmount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.overdueAmount', 'Overdue Amount')}</span>
                <span className="font-semibold text-red-600">
                  {formatCurrency(reportData.fees.overdueAmount)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Summary */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t('reports.attendanceSummary', 'Attendance Summary')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>{t('reports.attendanceRate', 'Attendance Rate')}</span>
                <span className="font-semibold">
                  {reportData.attendance.rate}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.totalSessions', 'Total Sessions')}</span>
                <span className="font-semibold">
                  {reportData.attendance.totalSessions}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.presentCount', 'Present Count')}</span>
                <span className="font-semibold text-green-600">
                  {reportData.attendance.presentCount}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.absentCount', 'Absent Count')}</span>
                <span className="font-semibold text-red-600">
                  {reportData.attendance.totalSessions -
                    reportData.attendance.presentCount}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Events Summary */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t('reports.eventsSummary', 'Events Summary')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>{t('reports.totalEvents', 'Total Events')}</span>
                <span className="font-semibold">{reportData.events.total}</span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.completedEvents', 'Completed Events')}</span>
                <span className="font-semibold text-green-600">
                  {reportData.events.completed}
                </span>
              </div>
              <div className="flex justify-between">
                <span>
                  {t('reports.participationRate', 'Participation Rate')}
                </span>
                <span className="font-semibold text-blue-600">
                  {reportData.events.participation}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('reports.upcomingEvents', 'Upcoming Events')}</span>
                <span className="font-semibold text-purple-600">
                  {reportData.events.total - reportData.events.completed}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
