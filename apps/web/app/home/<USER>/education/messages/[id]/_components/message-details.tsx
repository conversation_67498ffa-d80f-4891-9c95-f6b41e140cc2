'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from '@kit/ui/sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Avatar, AvatarFallback } from '@kit/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { 
  ArrowLeft, 
  Edit, 
  Send, 
  Trash2, 
  MessageSquare, 
  Users, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Mail,
  Smartphone,
  Bell,
} from 'lucide-react';

interface Props {
  account: string;
  messageId: string;
}

interface Message {
  id: string;
  title: string;
  content: string;
  message_type: string;
  recipient_type: string;
  priority: string;
  status: string;
  sent_at?: string;
  scheduled_at?: string;
  created_at: string;
  sender_name: string;
  delivery_channels: {
    sms: boolean;
    email: boolean;
    push: boolean;
  };
  stats: {
    total_recipients: number;
    delivered: number;
    read: number;
    failed: number;
  };
}

interface DeliveryRecord {
  id: string;
  recipient_name: string;
  recipient_type: string;
  status: string;
  delivered_at?: string;
  read_at?: string;
  channel: string;
}

export function MessageDetails({ account, messageId }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [message, setMessage] = useState<Message | null>(null);
  const [deliveryRecords, setDeliveryRecords] = useState<DeliveryRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    loadMessageDetails();
  }, [messageId]);

  const loadMessageDetails = async () => {
    try {
      setLoading(true);

      if (!currentAccount?.id) {
        setMessage(null);
        return;
      }

      // Load message from database
      const { data: messageData, error: messageError } = await supabase
        .from('messages')
        .select('*')
        .eq('id', messageId)
        .eq('account_id', currentAccount.id)
        .single();

      if (messageError) {
        throw new Error(messageError.message);
      }

      if (!messageData) {
        setMessage(null);
        return;
      }

      // Transform message data
      const transformedMessage: Message = {
        id: messageData.id,
        title: messageData.title,
        content: messageData.content,
        message_type: messageData.message_type,
        recipient_type: messageData.recipient_type,
        priority: messageData.priority,
        status: messageData.status,
        sent_at: messageData.sent_at,
        scheduled_at: messageData.scheduled_at,
        created_at: messageData.created_at,
        sender_name: messageData.sender_name || 'System',
        delivery_channels: messageData.delivery_channels || {
          sms: true,
          email: true,
          push: true,
        },
        stats: {
          total_recipients: 0, // Will be calculated from recipients
          delivered: 0,
          read: 0,
          failed: 0,
        },
      };

      setMessage(transformedMessage);

      // Load delivery records (mock for now since message_recipients might be empty)
      setDeliveryRecords([]);
    } catch (error: any) {
      console.error('Error loading message details:', error);
      toast.error(error.message || 'Failed to load message details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentAccount?.id || !message) return;

    const confirmed = window.confirm(
      t('messages.deleteConfirm', 'Are you sure you want to delete this message? This action cannot be undone.')
    );

    if (!confirmed) return;

    try {
      setDeleting(true);

      const { error: deleteError } = await supabase
        .from('messages')
        .delete()
        .eq('id', messageId)
        .eq('account_id', currentAccount.id);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      toast.success(t('messages.deleteSuccess', 'Message deleted successfully'));
      router.push(`/home/<USER>/education/messages`);
    } catch (error: any) {
      console.error('Error deleting message:', error);
      toast.error(error.message || 'Failed to delete message');
    } finally {
      setDeleting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'scheduled':
        return <Clock className="h-5 w-5 text-blue-600" />;
      case 'draft':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      sent: 'Đã gửi',
      scheduled: 'Đã lên lịch',
      draft: 'Bản nháp',
      failed: 'Thất bại',
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      sent: { label: t('messages.status.sent'), variant: 'default' as const },
      scheduled: { label: t('messages.status.scheduled'), variant: 'secondary' as const },
      draft: { label: t('messages.status.draft'), variant: 'outline' as const },
      failed: { label: t('messages.status.failed'), variant: 'destructive' as const },
      delivered: { label: t('messages.deliveryStatus.delivered', 'Delivered'), variant: 'default' as const },
      read: { label: t('messages.deliveryStatus.read', 'Read'), variant: 'default' as const },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };

    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'sms':
        return <Smartphone className="h-4 w-4" />;
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'push':
        return <Bell className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const formatDateTime = (dateTimeString?: string) => {
    if (!dateTimeString) return '-';
    return new Date(dateTimeString).toLocaleString('vi-VN');
  };

  if (loading) {
    return <div className="flex justify-center py-8">{t('common.loading')}</div>;
  }

  if (!message) {
    return <div className="text-center py-8 text-red-600">Message not found</div>;
  }

  return (
    <div className="space-y-8">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back')}</span>
        </Button>

        <div className="flex items-center space-x-2">
          {message.status === 'draft' && (
            <Button variant="outline" asChild>
              <Link href={`/home/<USER>/education/messages/${messageId}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                {t('messages.edit', 'Edit')}
              </Link>
            </Button>
          )}
          {message.status === 'draft' && (
            <Button>
              <Send className="h-4 w-4 mr-2" />
              {t('messages.send', 'Send')}
            </Button>
          )}
          <Button
            variant="outline"
            className="text-red-600 hover:text-red-700"
            onClick={handleDelete}
            disabled={deleting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {deleting ? t('common.deleting', 'Deleting...') : t('messages.delete', 'Delete')}
          </Button>
        </div>
      </div>

      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-cyan-600 to-teal-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <MessageSquare className="h-6 w-6" />
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    📧 {t(`messages.messageType.${message.message_type}`)}
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    {getStatusText(message.status)}
                  </Badge>
                </div>
              </div>
              <h1 className="text-4xl font-bold mb-2 leading-tight">{message.title}</h1>
              <div className="text-white/90 text-lg space-y-1">
                <p>👤 Người gửi: <span className="font-semibold">{message.sender_name}</span></p>
                <p>👥 Người nhận: <span className="font-semibold">{t(`messages.recipientType.${message.recipient_type}`)}</span></p>
                {message.sent_at && (
                  <p>📅 Đã gửi: <span className="font-semibold">{formatDateTime(message.sent_at)}</span></p>
                )}
                {message.scheduled_at && message.status === 'scheduled' && (
                  <p>⏰ Lên lịch: <span className="font-semibold">{formatDateTime(message.scheduled_at)}</span></p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Message Details */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-xl">{message.title}</CardTitle>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                <span>{t('messages.from')}: {message.sender_name}</span>
                <span>•</span>
                <span>{formatDateTime(message.sent_at || message.created_at)}</span>
                <span>•</span>
                <Badge variant="outline">{t(`messages.messageType.${message.message_type}`)}</Badge>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusIcon(message.status)}
              {getStatusBadge(message.status)}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">{t('messages.content', 'Content')}</h4>
              <div className="p-4 bg-gray-50 rounded-lg whitespace-pre-wrap">
                {message.content}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">{t('messages.recipients', 'Recipients')}</h4>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span>{t(`messages.recipientType.${message.recipient_type}`)}</span>
                  <Badge variant="secondary">{message.stats.total_recipients}</Badge>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">{t('messages.deliveryChannels', 'Delivery Channels')}</h4>
                <div className="flex items-center space-x-4">
                  {message.delivery_channels.sms && (
                    <div className="flex items-center space-x-1">
                      <Smartphone className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">SMS</span>
                    </div>
                  )}
                  {message.delivery_channels.email && (
                    <div className="flex items-center space-x-1">
                      <Mail className="h-4 w-4 text-green-600" />
                      <span className="text-sm">Email</span>
                    </div>
                  )}
                  {message.delivery_channels.push && (
                    <div className="flex items-center space-x-1">
                      <Bell className="h-4 w-4 text-purple-600" />
                      <span className="text-sm">Push</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Statistics */}
      {message.status === 'sent' && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{message.stats.total_recipients}</div>
                <div className="text-sm text-gray-500">{t('messages.stats.totalRecipients', 'Total Recipients')}</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{message.stats.delivered}</div>
                <div className="text-sm text-gray-500">{t('messages.stats.delivered', 'Delivered')}</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{message.stats.read}</div>
                <div className="text-sm text-gray-500">{t('messages.stats.read', 'Read')}</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{message.stats.failed}</div>
                <div className="text-sm text-gray-500">{t('messages.stats.failed', 'Failed')}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Delivery Records */}
      {message.status === 'sent' && (
        <Card>
          <CardHeader>
            <CardTitle>{t('messages.deliveryRecords', 'Delivery Records')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('messages.table.recipient', 'Recipient')}</TableHead>
                  <TableHead>{t('messages.table.channel', 'Channel')}</TableHead>
                  <TableHead>{t('messages.table.status', 'Status')}</TableHead>
                  <TableHead>{t('messages.table.deliveredAt', 'Delivered At')}</TableHead>
                  <TableHead>{t('messages.table.readAt', 'Read At')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {deliveryRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarFallback>
                            {record.recipient_name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{record.recipient_name}</div>
                          <div className="text-sm text-gray-500">{record.recipient_type}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getChannelIcon(record.channel)}
                        <span className="capitalize">{record.channel}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(record.status)}
                    </TableCell>
                    <TableCell>
                      {formatDateTime(record.delivered_at)}
                    </TableCell>
                    <TableCell>
                      {formatDateTime(record.read_at)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
