'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import { Edit, Eye, Filter, RefreshCw, Search, UserPlus, Users } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Avatar, AvatarFallback } from '@kit/ui/avatar';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';

interface Props {
  account: string;
}

interface Learner {
  id: string;
  learner_code: string;
  full_name: string;
  nickname?: string;
  date_of_birth: string;
  gender: string;
  status: string;
  enrollment_date: string;
  program?: {
    name: string;
    program_type: string;
  };
}

export function LearnersList({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [learners, setLearners] = useState<Learner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Get current organization
  const currentAccount = accounts?.find((acc) => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;

    loadLearners();
  }, [currentAccount?.id, supabase]);

  const loadLearners = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setLearners([]);
        return;
      }

      // Get learners with program info (left join to include learners without enrollments)
      const { data: learnersData, error: learnersError } = await supabase
        .from('learners')
        .select(
          `
          *,
          enrollments(
            program_id,
            status,
            enrollment_date,
            programs(
              id,
              name,
              program_type
            )
          )
        `,
        )
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (learnersError) throw learnersError;

      // Process the data to include program info
      const processedLearners =
        learnersData?.map((learner) => ({
          ...learner,
          program: learner.enrollments?.[0]?.programs || null,
        })) || [];

      setLearners(processedLearners);
    } catch (err: any) {
      console.error('Error loading learners:', err);
      setError(err.message || 'Failed to load learners');
    } finally {
      setLoading(false);
    }
  };

  const filteredLearners = learners.filter((learner) => {
    const matchesSearch =
      learner.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      learner.learner_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      learner.nickname?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || learner.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: {
        label: t('learners.status.active'),
        variant: 'default' as const,
      },
      inactive: {
        label: t('learners.status.inactive'),
        variant: 'secondary' as const,
      },
      graduated: {
        label: t('learners.status.graduated'),
        variant: 'outline' as const,
      },
      transferred: {
        label: t('learners.status.transferred'),
        variant: 'destructive' as const,
      },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || {
      label: status,
      variant: 'secondary' as const,
    };

    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const getGenderText = (gender: string) => {
    return t(`learners.gender.${gender}` as any) || gender;
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">{t('common.loading')}</div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="mb-4 text-red-600">{error}</p>
        <Button onClick={loadLearners} variant="outline">
          {t('common.refresh')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800 dark:from-blue-950/20 dark:to-indigo-950/20">
        <CardContent className="pt-6">
          <div className="flex flex-col items-start justify-between gap-6 lg:flex-row lg:items-center">
            <div className="flex flex-1 flex-col items-start space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              <div className="relative">
                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder={t('learners.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-72 border-gray-300 bg-white pl-10 focus:border-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:focus:border-blue-400"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:border-gray-600 dark:bg-gray-800 dark:focus:border-blue-400 dark:focus:ring-blue-800"
                >
                  <option value="all">
                    {t('learners.filters.allStatuses')}
                  </option>
                  <option value="active">{t('learners.filters.active')}</option>
                  <option value="inactive">
                    {t('learners.filters.inactive')}
                  </option>
                  <option value="graduated">
                    {t('learners.filters.graduated')}
                  </option>
                  <option value="transferred">
                    {t('learners.filters.transferred')}
                  </option>
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                onClick={loadLearners}
                variant="outline"
                size="lg"
                disabled={loading}
                className="flex items-center space-x-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                <span>{t('common.refresh', 'Refresh')}</span>
              </Button>

              <Button
                asChild
                size="lg"
                className="min-w-fit bg-blue-600 text-white shadow-lg transition-all duration-200 hover:bg-blue-700 hover:shadow-xl"
              >
                <Link
                  href={`/home/<USER>/education/learners/new`}
                  className="flex items-center space-x-2"
                >
                  <UserPlus className="h-5 w-5" />
                  <span>{t('learners.addLearner')}</span>
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500 transition-all duration-200 hover:shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('learners.stats.total')}
              </CardTitle>
              <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/50">
                <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {learners.length}
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500 transition-all duration-200 hover:shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('learners.stats.active')}
              </CardTitle>
              <div className="rounded-lg bg-green-100 p-2 dark:bg-green-900/50">
                <Users className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600 dark:text-green-400">
              {learners.filter((l) => l.status === 'active').length}
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500 transition-all duration-200 hover:shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('learners.stats.inactive')}
              </CardTitle>
              <div className="rounded-lg bg-orange-100 p-2 dark:bg-orange-900/50">
                <Users className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">
              {learners.filter((l) => l.status === 'inactive').length}
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500 transition-all duration-200 hover:shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('learners.stats.graduated')}
              </CardTitle>
              <div className="rounded-lg bg-purple-100 p-2 dark:bg-purple-900/50">
                <Users className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {learners.filter((l) => l.status === 'graduated').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Learners Table */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="rounded-t-lg bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/50">
                <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {t('learners.title')}
                </CardTitle>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  {filteredLearners.length} {t('learners.title').toLowerCase()}
                </p>
              </div>
            </div>
            <Badge
              variant="secondary"
              className="bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300"
            >
              {filteredLearners.length}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('learners.table.learner')}</TableHead>
                <TableHead>{t('learners.table.code')}</TableHead>
                <TableHead>{t('learners.table.age')}</TableHead>
                <TableHead>{t('learners.table.gender')}</TableHead>
                <TableHead>{t('learners.table.program')}</TableHead>
                <TableHead>{t('learners.table.status')}</TableHead>
                <TableHead>{t('learners.table.enrollmentDate')}</TableHead>
                <TableHead>{t('learners.table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLearners.map((learner) => (
                <TableRow key={learner.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarFallback>
                          {learner.full_name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{learner.full_name}</div>
                        {learner.nickname && (
                          <div className="text-sm text-gray-500">
                            {learner.nickname}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-sm">
                    {learner.learner_code}
                  </TableCell>
                  <TableCell>
                    {calculateAge(learner.date_of_birth)}{' '}
                    {t('learners.table.yearsOld')}
                  </TableCell>
                  <TableCell>{getGenderText(learner.gender)}</TableCell>
                  <TableCell>
                    {learner.program ? (
                      <div>
                        <div className="font-medium">
                          {learner.program.name}
                        </div>
                        <div className="text-sm text-gray-500 capitalize">
                          {learner.program.program_type.replace('_', ' ')}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">
                        {t('learners.table.noProgram')}
                      </span>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(learner.status)}</TableCell>
                  <TableCell>{formatDate(learner.enrollment_date)}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/20 dark:hover:text-blue-400"
                      >
                        <Link
                          href={`/home/<USER>/education/learners/${learner.id}`}
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="hover:bg-green-50 hover:text-green-600 dark:hover:bg-green-900/20 dark:hover:text-green-400"
                      >
                        <Link
                          href={`/home/<USER>/education/learners/${learner.id}/edit`}
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredLearners.length === 0 && (
            <div className="px-6 py-16 text-center">
              <div className="mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                <Users className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                {searchTerm || statusFilter !== 'all'
                  ? 'Không tìm thấy kết quả'
                  : 'Chưa có học viên nào'}
              </h3>
              <p className="mx-auto mb-6 max-w-md text-gray-500 dark:text-gray-400">
                {searchTerm || statusFilter !== 'all'
                  ? t('learners.emptyState.noResults')
                  : t('learners.emptyState.noLearners')}
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button asChild className="bg-blue-600 hover:bg-blue-700">
                  <Link href={`/home/<USER>/education/learners/new`}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    {t('learners.addLearner')}
                  </Link>
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
