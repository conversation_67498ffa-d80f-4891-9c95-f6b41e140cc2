'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from '@kit/ui/sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Plus, X, ArrowLeft } from 'lucide-react';

interface Props {
  account: string;
}

interface MenuItem {
  id: string;
  name: string;
  category: string;
  description: string;
  allergens: string[];
  nutritional_info: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
  cost_per_serving?: number;
}

export function AddMenuForm({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    date: '',
    meal_type: 'lunch',
    special_notes: '',
    cost_per_serving: 0,
    dietary_accommodations: [] as string[]
  });

  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [newItem, setNewItem] = useState({
    name: '',
    category: 'mon_chinh',
    description: '',
    allergens: '',
    calories: '',
    cost: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;
      const userId = workspace.user?.id;

      if (!accountId || !userId) {
        throw new Error('Account or user not found');
      }

      // Check if meal plan already exists for this date and meal type
      const { data: existingPlan, error: checkError } = await supabase
        .from('meal_plans')
        .select('id')
        .eq('account_id', accountId)
        .eq('date', formData.date)
        .eq('meal_type', formData.meal_type)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      let insertError;

      if (existingPlan) {
        // Update existing meal plan
        const { error } = await supabase
          .from('meal_plans')
          .update({
            menu_items: menuItems,
            special_notes: formData.special_notes || null,
            dietary_accommodations: formData.dietary_accommodations,
            cost_per_serving: formData.cost_per_serving || null,
          })
          .eq('id', existingPlan.id);

        insertError = error;
      } else {
        // Create new meal plan
        const { error } = await supabase
          .from('meal_plans')
          .insert({
            account_id: accountId,
            date: formData.date,
            meal_type: formData.meal_type,
            menu_items: menuItems,
            special_notes: formData.special_notes || null,
            dietary_accommodations: formData.dietary_accommodations,
            cost_per_serving: formData.cost_per_serving || null,
            created_by: userId,
          });

        insertError = error;
      }

      if (insertError) throw insertError;

      if (existingPlan) {
        toast.success('Menu da duoc cap nhat thanh cong');
      } else {
        toast.success('Menu da duoc tao thanh cong');
      }
      router.push(`/home/<USER>/education/meals`);
    } catch (err: any) {
      console.error('Error creating menu:', err);
      setError(err.message || 'Co loi xay ra khi tao menu');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addDietaryAccommodation = (accommodation: string) => {
    if (accommodation && !formData.dietary_accommodations.includes(accommodation)) {
      setFormData(prev => ({
        ...prev,
        dietary_accommodations: [...prev.dietary_accommodations, accommodation]
      }));
    }
  };

  const removeDietaryAccommodation = (accommodation: string) => {
    setFormData(prev => ({
      ...prev,
      dietary_accommodations: prev.dietary_accommodations.filter(a => a !== accommodation)
    }));
  };

  const addMenuItem = () => {
    if (!newItem.name.trim()) return;

    const menuItem: MenuItem = {
      id: Date.now().toString(),
      name: newItem.name,
      category: newItem.category,
      description: newItem.description,
      allergens: newItem.allergens ? newItem.allergens.split(',').map(a => a.trim()) : [],
      nutritional_info: {
        calories: newItem.calories ? parseInt(newItem.calories) : undefined,
      },
      cost_per_serving: newItem.cost ? parseFloat(newItem.cost) : undefined,
    };

    setMenuItems(prev => [...prev, menuItem]);
    setNewItem({
      name: '',
      category: 'mon_chinh',
      description: '',
      allergens: '',
      calories: '',
      cost: ''
    });
  };

  const removeMenuItem = (id: string) => {
    setMenuItems(prev => prev.filter(item => item.id !== id));
  };

  const getCategoryLabel = (category: string) => {
    const categories = {
      mon_chinh: 'Mon chinh',
      mon_phu: 'Mon phu',
      do_uong: 'Do uong',
      trang_mieng: 'Trang mieng',
      snack: 'Snack',
      trai_cay: 'Trai cay'
    };
    return categories[category as keyof typeof categories] || category;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lai</span>
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Thong tin co ban</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="date">Ngay ap dung *</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="meal_type">Loai bua an</Label>
                <Select value={formData.meal_type} onValueChange={(value) => handleInputChange('meal_type', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="breakfast">Sang</SelectItem>
                    <SelectItem value="lunch">Trua</SelectItem>
                    <SelectItem value="dinner">Toi</SelectItem>
                    <SelectItem value="snack">Snack</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="special_notes">Ghi chu dac biet</Label>
              <Textarea
                id="special_notes"
                value={formData.special_notes}
                onChange={(e) => handleInputChange('special_notes', e.target.value)}
                placeholder="Ghi chu ve menu, yeu cau dac biet..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="cost_per_serving">Gia moi suat (VND)</Label>
              <Input
                id="cost_per_serving"
                type="number"
                value={formData.cost_per_serving}
                onChange={(e) => handleInputChange('cost_per_serving', parseFloat(e.target.value) || 0)}
                min="0"
                step="1000"
                placeholder="Gia tien moi suat an"
              />
            </div>

            <div>
              <Label>Yeu cau che do an</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['Chay', 'Khong gluten', 'Khong lactose', 'Halal', 'Kosher'].map((accommodation) => (
                  <button
                    key={accommodation}
                    type="button"
                    onClick={() =>
                      formData.dietary_accommodations.includes(accommodation)
                        ? removeDietaryAccommodation(accommodation)
                        : addDietaryAccommodation(accommodation)
                    }
                    className={`px-3 py-1 text-sm rounded-full border ${
                      formData.dietary_accommodations.includes(accommodation)
                        ? 'bg-blue-100 text-blue-800 border-blue-300'
                        : 'bg-gray-100 text-gray-700 border-gray-300'
                    }`}
                  >
                    {accommodation}
                  </button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Mon an trong menu</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Add new item form */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <h4 className="font-medium mb-3">Them mon an moi</h4>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <Label htmlFor="item_name">Ten mon an</Label>
                  <Input
                    id="item_name"
                    value={newItem.name}
                    onChange={(e) => setNewItem(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Ten mon an"
                  />
                </div>
                <div>
                  <Label htmlFor="item_category">Loai mon</Label>
                  <Select value={newItem.category} onValueChange={(value) => setNewItem(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mon_chinh">Mon chinh</SelectItem>
                      <SelectItem value="mon_phu">Mon phu</SelectItem>
                      <SelectItem value="do_uong">Do uong</SelectItem>
                      <SelectItem value="trang_mieng">Trang mieng</SelectItem>
                      <SelectItem value="snack">Snack</SelectItem>
                      <SelectItem value="trai_cay">Trai cay</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <Label htmlFor="item_description">Mo ta</Label>
                  <Input
                    id="item_description"
                    value={newItem.description}
                    onChange={(e) => setNewItem(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Mo ta mon an"
                  />
                </div>
                <div>
                  <Label htmlFor="item_calories">Calories</Label>
                  <Input
                    id="item_calories"
                    type="number"
                    value={newItem.calories}
                    onChange={(e) => setNewItem(prev => ({ ...prev, calories: e.target.value }))}
                    placeholder="Calories"
                  />
                </div>
                <div>
                  <Label htmlFor="item_cost">Gia (VND)</Label>
                  <Input
                    id="item_cost"
                    type="number"
                    value={newItem.cost}
                    onChange={(e) => setNewItem(prev => ({ ...prev, cost: e.target.value }))}
                    placeholder="Gia tien"
                  />
                </div>
              </div>
              <div className="mb-4">
                <Label htmlFor="item_allergens">Di ung (cach nhau boi dau phay)</Label>
                <Input
                  id="item_allergens"
                  value={newItem.allergens}
                  onChange={(e) => setNewItem(prev => ({ ...prev, allergens: e.target.value }))}
                  placeholder="Vi du: sua, trung, dau phong"
                />
              </div>
              <Button type="button" onClick={addMenuItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Them mon an
              </Button>
            </div>

            {/* Menu items list */}
            {menuItems.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Danh sach mon an ({menuItems.length})</h4>
                {menuItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{item.name}</span>
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {getCategoryLabel(item.category)}
                        </span>
                      </div>
                      {item.description && (
                        <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                        {item.nutritional_info.calories && (
                          <span>{item.nutritional_info.calories} cal</span>
                        )}
                        {item.cost_per_serving && (
                          <span>{item.cost_per_serving.toLocaleString()} VND</span>
                        )}
                        {item.allergens.length > 0 && (
                          <span>Di ung: {item.allergens.join(', ')}</span>
                        )}
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMenuItem(item.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <div className="text-sm text-gray-600 mt-2">
                  Tong chi phi cac mon: {menuItems.reduce((sum, item) => sum + (item.cost_per_serving || 0), 0).toLocaleString()} VND
                  {formData.cost_per_serving > 0 && (
                    <span className="ml-4">Gia suat an: {formData.cost_per_serving.toLocaleString()} VND</span>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {error && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(`/home/<USER>/education/meals`)}
          >
            Huy
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Dang tao...' : 'Tao menu'}
          </Button>
        </div>
      </form>
    </div>
  );
}
