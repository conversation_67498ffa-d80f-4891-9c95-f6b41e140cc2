import { Suspense } from 'react';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { AddMenuForm } from './_components/add-menu-form';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

async function AddMenuPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-8">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:meals.menus.add.title', 'Add New Menu')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/meals`}
            >
              {i18n.t('education:breadcrumbs.meals')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:meals.menus.add.title')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-6xl px-4">
        <Suspense fallback={<div>Loading...</div>}>
          <AddMenuForm account={resolvedParams.account} />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(AddMenuPage);

export const metadata = {
  title: 'Add New Menu',
  description: 'Create a new meal menu for the education program',
};
