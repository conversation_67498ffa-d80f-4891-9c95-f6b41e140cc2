'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Input } from '@kit/ui/input';
import { 
  UtensilsCrossed, 
  Plus, 
  Search,
  Eye,
  Edit,
  Trash2,
  Clock,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface FoodItem {
  id: string;
  name: string;
  description: string;
  serving_size: string;
  preparation_time_minutes: number;
  cost_per_serving: number;
  status: string;
  allergens: string[];
  nutritional_info: {
    calories?: number;
    protein?: number;
    carbs?: number;
  };
  created_at: string;
  meal_categories?: {
    name: string;
    color_code: string;
  };
}

interface Props {
  account: string;
}

function FoodItemsList({ account }: Props) {
  const [foodItems, setFoodItems] = useState<FoodItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  useEffect(() => {
    loadFoodItems();
  }, []);

  const loadFoodItems = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      const { data, error } = await supabase
        .from('food_items')
        .select(`
          *,
          meal_categories(name, color_code)
        `)
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setFoodItems(data || []);
    } catch (error) {
      console.error('Error loading food items:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredFoodItems = foodItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Đang phục vụ</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">Ngừng phục vụ</Badge>;
      case 'seasonal':
        return <Badge className="bg-yellow-100 text-yellow-800">Theo mùa</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Danh sách món ăn</h1>
          <p className="text-muted-foreground">
            Quản lý thực đơn và món ăn cho học viên
          </p>
        </div>
        <Button asChild>
          <Link href={`/home/<USER>/education/meals/food-items/new`}>
            <Plus className="h-4 w-4 mr-2" />
            Thêm món ăn
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm món ăn..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">Tất cả trạng thái</option>
                <option value="active">Đang phục vụ</option>
                <option value="inactive">Ngừng phục vụ</option>
                <option value="seasonal">Theo mùa</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Food Items Grid */}
      {filteredFoodItems.length === 0 ? (
        <Card>
          <CardContent className="p-12">
            <div className="text-center">
              <UtensilsCrossed className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm || statusFilter !== 'all' ? 'Không tìm thấy món ăn' : 'Chưa có món ăn'}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm'
                  : 'Thêm món ăn đầu tiên vào thực đơn'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button asChild>
                  <Link href={`/home/<USER>/education/meals/food-items/new`}>
                    <Plus className="h-4 w-4 mr-2" />
                    Thêm món ăn đầu tiên
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFoodItems.map((item) => (
            <Card key={item.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{item.name}</CardTitle>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {getStatusBadge(item.status)}
                      {item.meal_categories && (
                        <Badge 
                          variant="outline"
                          style={{ 
                            backgroundColor: item.meal_categories.color_code + '20',
                            borderColor: item.meal_categories.color_code,
                            color: item.meal_categories.color_code
                          }}
                        >
                          {item.meal_categories.name}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <UtensilsCrossed className="h-8 w-8 text-orange-600 flex-shrink-0" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {item.description || 'Chưa có mô tả'}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Khẩu phần:</span>
                    <span className="font-medium">{item.serving_size || 'Chưa xác định'}</span>
                  </div>
                  
                  {item.preparation_time_minutes > 0 && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        Thời gian:
                      </span>
                      <span className="font-medium">{item.preparation_time_minutes} phút</span>
                    </div>
                  )}

                  {item.cost_per_serving > 0 && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground flex items-center">
                        <DollarSign className="h-3 w-3 mr-1" />
                        Giá thành:
                      </span>
                      <span className="font-medium">{formatCurrency(item.cost_per_serving)}</span>
                    </div>
                  )}

                  {item.nutritional_info?.calories && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Calories:</span>
                      <span className="font-medium">{item.nutritional_info.calories} kcal</span>
                    </div>
                  )}

                  {item.allergens && item.allergens.length > 0 && (
                    <div>
                      <p className="text-sm text-muted-foreground mb-1">Chất gây dị ứng:</p>
                      <div className="flex flex-wrap gap-1">
                        {item.allergens.slice(0, 3).map((allergen, index) => (
                          <Badge key={index} variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">
                            {allergen}
                          </Badge>
                        ))}
                        {item.allergens.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{item.allergens.length - 3} khác
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/home/<USER>/education/meals/food-items/${item.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/home/<USER>/education/meals/food-items/${item.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                    <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Results count */}
      {filteredFoodItems.length > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          Hiển thị {filteredFoodItems.length} trong tổng số {foodItems.length} món ăn
        </div>
      )}
    </div>
  );
}

export default FoodItemsList;
