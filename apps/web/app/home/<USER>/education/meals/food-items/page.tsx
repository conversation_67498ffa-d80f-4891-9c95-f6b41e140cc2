import { redirect } from 'next/navigation';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import FoodItemsList from './_components/food-items-list';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

export const metadata = {
  title: 'Danh sách món ăn',
};

async function FoodItemsPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-36">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title="Danh sách món ăn"
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/meals`}>
              {i18n.t('education:breadcrumbs.meals')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              Món ăn
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-7xl">
        <FoodItemsList account={resolvedParams.account} />
      </div>
    </div>
  );
}

export default FoodItemsPage;
