import { ZnsEventType } from '@kit/zns/types';

// Education-specific ZNS event types configuration
export const EDUCATION_ZNS_EVENT_TYPES: ZnsEventType[] = [
  // Attendance Events
  {
    id: 'attendance_absent',
    name: 'Họ<PERSON> sinh vắng mặt',
    description: 'Thông báo khi học sinh vắng mặt không phép',
    icon: '❌',
    categories: ['attendance', 'urgent'],
    suggestedKeys: [
      'parent_name',
      'child_name', 
      'date',
      'school_name',
      'teacher_name',
      'teacher_phone'
    ],
    example: '<PERSON>n chào <parent_name>, <child_name> vắng mặt ngày <date> tại <school_name>.',
    recipientPath: 'guardian.phone',
    priority: 'high'
  },
  
  {
    id: 'attendance_late',
    name: '<PERSON>ọc sinh đến muộn',
    description: 'Thông báo khi học sinh đến muộn',
    icon: '⏰',
    categories: ['attendance', 'info'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'date', 
      'time',
      'school_name',
      'class_time'
    ],
    example: '<PERSON><PERSON> chào <parent_name>, <child_name> đến muộn ngày <date> lúc <time>.',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  },

  // Payment Events
  {
    id: 'payment_due',
    name: 'Nhắc nhở học phí',
    description: 'Nhắc nhở phụ huynh về học phí sắp đến hạn',
    icon: '💰',
    categories: ['payment', 'reminder'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'month',
      'amount',
      'due_date',
      'payment_link'
    ],
    example: 'Học phí tháng <month> của <child_name> sẽ đến hạn vào <due_date>. Số tiền: <amount>.',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  },

  {
    id: 'payment_overdue',
    name: 'Học phí quá hạn',
    description: 'Thông báo học phí đã quá hạn thanh toán',
    icon: '🚨',
    categories: ['payment', 'urgent'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'month',
      'amount',
      'overdue_days',
      'payment_link'
    ],
    example: 'Học phí tháng <month> của <child_name> đã quá hạn <overdue_days> ngày.',
    recipientPath: 'guardian.phone',
    priority: 'high'
  },

  {
    id: 'payment_success',
    name: 'Xác nhận thanh toán',
    description: 'Xác nhận thanh toán học phí thành công',
    icon: '✅',
    categories: ['payment', 'confirmation'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'month',
      'amount',
      'transaction_id',
      'payment_date'
    ],
    example: 'Học phí tháng <month> của <child_name> đã được thanh toán thành công.',
    recipientPath: 'guardian.phone',
    priority: 'low'
  },

  // Health Events
  {
    id: 'health_emergency',
    name: 'Cảnh báo sức khỏe khẩn cấp',
    description: 'Thông báo khẩn cấp về tình trạng sức khỏe học sinh',
    icon: '🚨',
    categories: ['health', 'emergency'],
    suggestedKeys: [
      'child_name',
      'school_name',
      'condition',
      'contact_phone',
      'location'
    ],
    example: 'KHẨN CẤP: <child_name> cần được chăm sóc y tế tại <school_name>.',
    recipientPath: 'guardian.phone',
    priority: 'critical'
  },

  {
    id: 'health_medication',
    name: 'Thông báo uống thuốc',
    description: 'Thông báo về việc cho học sinh uống thuốc',
    icon: '💊',
    categories: ['health', 'info'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'medication',
      'time',
      'dosage'
    ],
    example: '<child_name> đã được uống thuốc <medication> lúc <time>.',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  },

  // Event Notifications
  {
    id: 'event_reminder',
    name: 'Nhắc nhở sự kiện',
    description: 'Nhắc nhở về sự kiện sắp diễn ra',
    icon: '📅',
    categories: ['event', 'reminder'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'event_name',
      'date',
      'time',
      'location'
    ],
    example: 'Sự kiện "<event_name>" sẽ diễn ra vào <date> tại <location>.',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  },

  {
    id: 'event_registration',
    name: 'Xác nhận đăng ký sự kiện',
    description: 'Xác nhận đăng ký tham gia sự kiện',
    icon: '✅',
    categories: ['event', 'confirmation'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'event_name',
      'date',
      'registration_time'
    ],
    example: '<child_name> đã được đăng ký tham gia "<event_name>" vào <date>.',
    recipientPath: 'guardian.phone',
    priority: 'low'
  },

  // Report Notifications
  {
    id: 'report_available',
    name: 'Báo cáo học tập mới',
    description: 'Thông báo có báo cáo học tập mới',
    icon: '📊',
    categories: ['report', 'info'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'report_type',
      'date',
      'report_link'
    ],
    example: 'Báo cáo <report_type> của <child_name> ngày <date> đã sẵn sàng.',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  },

  {
    id: 'report_milestone',
    name: 'Cột mốc phát triển',
    description: 'Thông báo về cột mốc phát triển quan trọng',
    icon: '🎯',
    categories: ['report', 'milestone'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'milestone',
      'achievement',
      'date'
    ],
    example: '<child_name> đã đạt được cột mốc: <milestone>.',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  },

  // Transportation Events
  {
    id: 'transport_delay',
    name: 'Xe đưa đón chậm',
    description: 'Thông báo xe đưa đón bị chậm',
    icon: '🚌',
    categories: ['transport', 'delay'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'route_name',
      'delay_minutes',
      'new_time'
    ],
    example: 'Xe đưa đón tuyến <route_name> bị chậm <delay_minutes> phút.',
    recipientPath: 'guardian.phone',
    priority: 'high'
  },

  {
    id: 'transport_incident',
    name: 'Sự cố xe đưa đón',
    description: 'Thông báo sự cố trong quá trình đưa đón',
    icon: '⚠️',
    categories: ['transport', 'incident'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'incident_type',
      'status',
      'contact_phone'
    ],
    example: 'Có sự cố <incident_type> trên xe đưa đón. Vui lòng liên hệ <contact_phone>.',
    recipientPath: 'guardian.phone',
    priority: 'high'
  },

  // General Notifications
  {
    id: 'announcement',
    name: 'Thông báo chung',
    description: 'Thông báo chung từ nhà trường',
    icon: '📢',
    categories: ['general', 'announcement'],
    suggestedKeys: [
      'parent_name',
      'school_name',
      'announcement_title',
      'announcement_content',
      'date'
    ],
    example: 'Thông báo từ <school_name>: <announcement_title>',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  },

  {
    id: 'reminder',
    name: 'Nhắc nhở chung',
    description: 'Nhắc nhở về các việc cần làm',
    icon: '⏰',
    categories: ['general', 'reminder'],
    suggestedKeys: [
      'parent_name',
      'child_name',
      'reminder_content',
      'due_date',
      'action_required'
    ],
    example: 'Nhắc nhở: <reminder_content> cho <child_name>.',
    recipientPath: 'guardian.phone',
    priority: 'medium'
  }
];

// Default ZNS mappings for education events
export const EDUCATION_ZNS_MAPPINGS = [
  {
    module: 'education',
    event_type: 'attendance_absent',
    template_key: 'attendance_absent',
    conditions: {
      status: 'absent',
      notify_guardian: true
    },
    enabled: true,
    priority: 'high'
  },
  
  {
    module: 'education', 
    event_type: 'attendance_late',
    template_key: 'attendance_late',
    conditions: {
      status: 'late',
      notify_guardian: true
    },
    enabled: true,
    priority: 'medium'
  },

  {
    module: 'education',
    event_type: 'payment_due',
    template_key: 'payment_due',
    conditions: {
      days_before_due: 3,
      status: 'pending'
    },
    enabled: true,
    priority: 'medium'
  },

  {
    module: 'education',
    event_type: 'payment_success',
    template_key: 'payment_success', 
    conditions: {
      status: 'paid'
    },
    enabled: true,
    priority: 'low'
  },

  {
    module: 'education',
    event_type: 'health_emergency',
    template_key: 'health_emergency',
    conditions: {
      is_emergency: true
    },
    enabled: true,
    priority: 'critical'
  },

  {
    module: 'education',
    event_type: 'report_available',
    template_key: 'report_available',
    conditions: {
      notify_guardian: true
    },
    enabled: true,
    priority: 'medium'
  }
];

// ZNS usage categories for billing
export const EDUCATION_ZNS_CATEGORIES = {
  CRITICAL: {
    name: 'Khẩn cấp',
    priority: 1,
    cost_multiplier: 1.5,
    events: ['health_emergency', 'transport_incident']
  },
  
  HIGH: {
    name: 'Quan trọng',
    priority: 2, 
    cost_multiplier: 1.2,
    events: ['attendance_absent', 'payment_overdue', 'transport_delay']
  },
  
  MEDIUM: {
    name: 'Bình thường',
    priority: 3,
    cost_multiplier: 1.0,
    events: ['attendance_late', 'payment_due', 'event_reminder', 'report_available']
  },
  
  LOW: {
    name: 'Thông tin',
    priority: 4,
    cost_multiplier: 0.8,
    events: ['payment_success', 'event_registration', 'announcement']
  }
};

// Shared ZNS pool configuration for education
export const EDUCATION_SHARED_ZNS_CONFIG = {
  pools: [
    {
      id: 'education_pool_1',
      name: 'Education Pool 1',
      oa_id: 'shared_education_oa_1',
      monthly_quota: 10000,
      cost_per_message: 500, // VND
      assigned_accounts: [],
      priority_events: ['health_emergency', 'transport_incident']
    },
    {
      id: 'education_pool_2', 
      name: 'Education Pool 2',
      oa_id: 'shared_education_oa_2',
      monthly_quota: 10000,
      cost_per_message: 500,
      assigned_accounts: [],
      priority_events: ['attendance_absent', 'payment_overdue']
    }
  ],
  
  load_balancing: {
    algorithm: 'least_usage',
    fallback_enabled: true,
    retry_attempts: 3
  },
  
  cost_optimization: {
    batch_similar_messages: true,
    avoid_peak_hours: true,
    deduplicate_messages: true,
    smart_scheduling: true
  }
};

// Education-specific ZNS settings
export const EDUCATION_ZNS_SETTINGS = {
  // Default sending preferences
  default_sending_mode: 'production',
  
  // Retry configuration
  retry_config: {
    max_retries: 3,
    retry_delay: 5000, // 5 seconds
    exponential_backoff: true
  },
  
  // Rate limiting
  rate_limits: {
    per_account_per_minute: 50,
    per_account_per_hour: 1000,
    per_account_per_day: 5000
  },
  
  // Notification preferences
  notification_preferences: {
    emergency_immediate: true,
    batch_non_urgent: true,
    respect_quiet_hours: true,
    quiet_hours: {
      start: '22:00',
      end: '07:00'
    }
  },
  
  // Analytics and reporting
  analytics: {
    track_delivery_status: true,
    track_read_status: true,
    generate_daily_reports: true,
    generate_monthly_reports: true
  }
};
