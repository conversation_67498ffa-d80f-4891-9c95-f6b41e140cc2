/**
 * Education-specific ZNS Events Definition
 * Định nghĩa các events cho education module để tích hợp với ZNS system
 */

// Education Event Types - sẽ được register với ZNS system
export const EDUCATION_EVENTS = {
  // Attendance Events
  ATTENDANCE_ABSENT: 'education.attendance.absent',
  ATTENDANCE_LATE: 'education.attendance.late', 
  ATTENDANCE_EARLY_PICKUP: 'education.attendance.early_pickup',
  ATTENDANCE_SUMMARY_DAILY: 'education.attendance.summary.daily',
  
  // Payment Events
  PAYMENT_DUE_REMINDER: 'education.payment.due_reminder',
  PAYMENT_OVERDUE: 'education.payment.overdue',
  PAYMENT_SUCCESS: 'education.payment.success',
  PAYMENT_FAILED: 'education.payment.failed',
  PAYMENT_REFUND: 'education.payment.refund',
  
  // Health Events
  HEALTH_EMERGENCY: 'education.health.emergency',
  HEALTH_MEDICATION_GIVEN: 'education.health.medication_given',
  HEALTH_INCIDENT: 'education.health.incident',
  HEALTH_CHECKUP_DUE: 'education.health.checkup_due',
  
  // Academic Events
  REPORT_AVAILABLE: 'education.report.available',
  REPORT_MILESTONE: 'education.report.milestone',
  HOMEWORK_ASSIGNED: 'education.homework.assigned',
  HOMEWORK_OVERDUE: 'education.homework.overdue',
  
  // Event & Activity Events
  EVENT_REMINDER: 'education.event.reminder',
  EVENT_REGISTRATION_CONFIRMED: 'education.event.registration_confirmed',
  EVENT_CANCELLED: 'education.event.cancelled',
  EVENT_RESCHEDULED: 'education.event.rescheduled',
  
  // Transportation Events
  TRANSPORT_DELAY: 'education.transport.delay',
  TRANSPORT_INCIDENT: 'education.transport.incident',
  TRANSPORT_ROUTE_CHANGE: 'education.transport.route_change',
  TRANSPORT_PICKUP_REMINDER: 'education.transport.pickup_reminder',
  
  // Communication Events
  MESSAGE_FROM_TEACHER: 'education.message.from_teacher',
  ANNOUNCEMENT_GENERAL: 'education.announcement.general',
  ANNOUNCEMENT_URGENT: 'education.announcement.urgent',
  SCHEDULE_CHANGE: 'education.schedule.change',
  
  // Administrative Events
  ENROLLMENT_CONFIRMED: 'education.enrollment.confirmed',
  ENROLLMENT_PENDING: 'education.enrollment.pending',
  DOCUMENT_REQUIRED: 'education.document.required',
  MEETING_SCHEDULED: 'education.meeting.scheduled',
} as const;

// Event Data Interfaces
export interface AttendanceEventData {
  learner_id: string;
  learner_name: string;
  program_name: string;
  session_date: string;
  session_time: string;
  status: 'absent' | 'late' | 'early_pickup';
  check_in_time?: string;
  check_out_time?: string;
  notes?: string;
  teacher_name: string;
  teacher_phone: string;
  guardian_phones: string[];
}

export interface PaymentEventData {
  fee_id: string;
  learner_id: string;
  learner_name: string;
  amount: number;
  due_date: string;
  month: string;
  fee_type: string;
  payment_method?: string;
  transaction_id?: string;
  payment_date?: string;
  guardian_phones: string[];
}

export interface HealthEventData {
  learner_id: string;
  learner_name: string;
  incident_type: 'emergency' | 'medication' | 'incident' | 'checkup_due';
  description: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  medication_name?: string;
  dosage?: string;
  time_given?: string;
  contact_phone: string;
  guardian_phones: string[];
}

export interface ReportEventData {
  report_id: string;
  learner_id: string;
  learner_name: string;
  report_type: string;
  report_date: string;
  teacher_name: string;
  milestone?: string;
  achievement?: string;
  report_url: string;
  guardian_phones: string[];
}

export interface EventActivityData {
  event_id: string;
  event_name: string;
  event_date: string;
  event_time: string;
  location: string;
  event_type: 'reminder' | 'registration' | 'cancelled' | 'rescheduled';
  learner_ids: string[];
  learner_names: string[];
  registration_deadline?: string;
  guardian_phones: string[];
}

export interface TransportEventData {
  route_id: string;
  route_name: string;
  incident_type: 'delay' | 'incident' | 'route_change' | 'pickup_reminder';
  description: string;
  estimated_delay?: number;
  new_pickup_time?: string;
  contact_phone: string;
  affected_learner_ids: string[];
  guardian_phones: string[];
}

export interface CommunicationEventData {
  message_type: 'teacher_message' | 'announcement' | 'schedule_change';
  title: string;
  content: string;
  sender_name: string;
  sender_role: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  target_audience: 'all' | 'class' | 'individual';
  learner_ids?: string[];
  guardian_phones: string[];
}

// Event Priority Mapping
export const EVENT_PRIORITIES = {
  [EDUCATION_EVENTS.HEALTH_EMERGENCY]: 'critical',
  [EDUCATION_EVENTS.TRANSPORT_INCIDENT]: 'high',
  [EDUCATION_EVENTS.ATTENDANCE_ABSENT]: 'high',
  [EDUCATION_EVENTS.PAYMENT_OVERDUE]: 'high',
  [EDUCATION_EVENTS.ANNOUNCEMENT_URGENT]: 'high',
  
  [EDUCATION_EVENTS.ATTENDANCE_LATE]: 'medium',
  [EDUCATION_EVENTS.PAYMENT_DUE_REMINDER]: 'medium',
  [EDUCATION_EVENTS.EVENT_REMINDER]: 'medium',
  [EDUCATION_EVENTS.REPORT_AVAILABLE]: 'medium',
  [EDUCATION_EVENTS.TRANSPORT_DELAY]: 'medium',
  [EDUCATION_EVENTS.MESSAGE_FROM_TEACHER]: 'medium',
  
  [EDUCATION_EVENTS.PAYMENT_SUCCESS]: 'low',
  [EDUCATION_EVENTS.EVENT_REGISTRATION_CONFIRMED]: 'low',
  [EDUCATION_EVENTS.ENROLLMENT_CONFIRMED]: 'low',
  [EDUCATION_EVENTS.HEALTH_MEDICATION_GIVEN]: 'low',
} as const;

// Event Categories for billing and analytics
export const EVENT_CATEGORIES = {
  CRITICAL: {
    events: [EDUCATION_EVENTS.HEALTH_EMERGENCY],
    cost_multiplier: 2.0,
    immediate_send: true
  },
  HIGH: {
    events: [
      EDUCATION_EVENTS.TRANSPORT_INCIDENT,
      EDUCATION_EVENTS.ATTENDANCE_ABSENT,
      EDUCATION_EVENTS.PAYMENT_OVERDUE,
      EDUCATION_EVENTS.ANNOUNCEMENT_URGENT
    ],
    cost_multiplier: 1.5,
    immediate_send: true
  },
  MEDIUM: {
    events: [
      EDUCATION_EVENTS.ATTENDANCE_LATE,
      EDUCATION_EVENTS.PAYMENT_DUE_REMINDER,
      EDUCATION_EVENTS.EVENT_REMINDER,
      EDUCATION_EVENTS.REPORT_AVAILABLE,
      EDUCATION_EVENTS.TRANSPORT_DELAY,
      EDUCATION_EVENTS.MESSAGE_FROM_TEACHER
    ],
    cost_multiplier: 1.0,
    immediate_send: false
  },
  LOW: {
    events: [
      EDUCATION_EVENTS.PAYMENT_SUCCESS,
      EDUCATION_EVENTS.EVENT_REGISTRATION_CONFIRMED,
      EDUCATION_EVENTS.ENROLLMENT_CONFIRMED,
      EDUCATION_EVENTS.HEALTH_MEDICATION_GIVEN
    ],
    cost_multiplier: 0.8,
    immediate_send: false
  }
} as const;

// Default ZNS Templates for Education Events
export const EDUCATION_ZNS_TEMPLATES = {
  // Attendance Templates
  'education.attendance.absent': {
    name: 'Thông báo vắng mặt',
    content: 'Xin chào {{guardian_name}}, {{learner_name}} vắng mặt ngày {{session_date}} tại {{school_name}}. Vui lòng liên hệ cô {{teacher_name}} ({{teacher_phone}}) để biết thêm chi tiết.',
    variables: ['guardian_name', 'learner_name', 'session_date', 'school_name', 'teacher_name', 'teacher_phone']
  },
  
  'education.attendance.late': {
    name: 'Thông báo đến muộn',
    content: 'Xin chào {{guardian_name}}, {{learner_name}} đến muộn ngày {{session_date}} lúc {{check_in_time}}. Giờ học bắt đầu: {{session_time}}.',
    variables: ['guardian_name', 'learner_name', 'session_date', 'check_in_time', 'session_time']
  },
  
  // Payment Templates
  'education.payment.due_reminder': {
    name: 'Nhắc nhở học phí',
    content: 'Xin chào {{guardian_name}}, học phí {{fee_type}} tháng {{month}} của {{learner_name}} sẽ đến hạn vào {{due_date}}. Số tiền: {{amount}}đ. Thanh toán tại: {{payment_url}}',
    variables: ['guardian_name', 'fee_type', 'month', 'learner_name', 'due_date', 'amount', 'payment_url']
  },
  
  'education.payment.success': {
    name: 'Xác nhận thanh toán',
    content: 'Cảm ơn {{guardian_name}}! Học phí {{fee_type}} tháng {{month}} của {{learner_name}} đã được thanh toán thành công. Số tiền: {{amount}}đ. Mã GD: {{transaction_id}}',
    variables: ['guardian_name', 'fee_type', 'month', 'learner_name', 'amount', 'transaction_id']
  },
  
  // Health Templates
  'education.health.emergency': {
    name: 'Cảnh báo sức khỏe khẩn cấp',
    content: 'KHẨN CẤP: {{learner_name}} cần được chăm sóc y tế tại {{school_name}}. Tình trạng: {{description}}. Vui lòng liên hệ ngay: {{contact_phone}}',
    variables: ['learner_name', 'school_name', 'description', 'contact_phone']
  },
  
  'education.health.medication_given': {
    name: 'Thông báo uống thuốc',
    content: 'Xin chào {{guardian_name}}, {{learner_name}} đã được uống thuốc {{medication_name}} ({{dosage}}) lúc {{time_given}} theo chỉ định.',
    variables: ['guardian_name', 'learner_name', 'medication_name', 'dosage', 'time_given']
  },
  
  // Report Templates
  'education.report.available': {
    name: 'Báo cáo học tập mới',
    content: 'Xin chào {{guardian_name}}, báo cáo {{report_type}} của {{learner_name}} ngày {{report_date}} đã sẵn sàng. Xem tại: {{report_url}}',
    variables: ['guardian_name', 'report_type', 'learner_name', 'report_date', 'report_url']
  },
  
  // Event Templates
  'education.event.reminder': {
    name: 'Nhắc nhở sự kiện',
    content: 'Nhắc nhở: Sự kiện "{{event_name}}" sẽ diễn ra vào {{event_date}} lúc {{event_time}} tại {{location}}. {{learner_name}} đã đăng ký tham gia.',
    variables: ['event_name', 'event_date', 'event_time', 'location', 'learner_name']
  },
  
  // Transport Templates
  'education.transport.delay': {
    name: 'Xe đưa đón chậm',
    content: 'Thông báo: Xe đưa đón tuyến {{route_name}} bị chậm {{estimated_delay}} phút. Thời gian đón mới: {{new_pickup_time}}. Liên hệ: {{contact_phone}}',
    variables: ['route_name', 'estimated_delay', 'new_pickup_time', 'contact_phone']
  },
  
  // Communication Templates
  'education.message.from_teacher': {
    name: 'Tin nhắn từ giáo viên',
    content: 'Tin nhắn từ {{sender_name}}: {{title}}\n\n{{content}}\n\nTrân trọng,\n{{sender_name}} - {{sender_role}}',
    variables: ['sender_name', 'title', 'content', 'sender_role']
  },
  
  'education.announcement.general': {
    name: 'Thông báo chung',
    content: 'Thông báo từ {{school_name}}:\n\n{{title}}\n\n{{content}}\n\nChi tiết liên hệ: {{contact_phone}}',
    variables: ['school_name', 'title', 'content', 'contact_phone']
  }
} as const;

// Event-to-Template Mappings
export const EDUCATION_EVENT_MAPPINGS = [
  // Attendance mappings
  {
    event_type: EDUCATION_EVENTS.ATTENDANCE_ABSENT,
    template_key: 'education.attendance.absent',
    conditions: { status: 'absent' },
    enabled: true,
    priority: 'high'
  },
  {
    event_type: EDUCATION_EVENTS.ATTENDANCE_LATE,
    template_key: 'education.attendance.late', 
    conditions: { status: 'late' },
    enabled: true,
    priority: 'medium'
  },
  
  // Payment mappings
  {
    event_type: EDUCATION_EVENTS.PAYMENT_DUE_REMINDER,
    template_key: 'education.payment.due_reminder',
    conditions: { days_before_due: 3 },
    enabled: true,
    priority: 'medium'
  },
  {
    event_type: EDUCATION_EVENTS.PAYMENT_SUCCESS,
    template_key: 'education.payment.success',
    conditions: { status: 'paid' },
    enabled: true,
    priority: 'low'
  },
  
  // Health mappings
  {
    event_type: EDUCATION_EVENTS.HEALTH_EMERGENCY,
    template_key: 'education.health.emergency',
    conditions: { severity: 'critical' },
    enabled: true,
    priority: 'critical'
  },
  {
    event_type: EDUCATION_EVENTS.HEALTH_MEDICATION_GIVEN,
    template_key: 'education.health.medication_given',
    conditions: { notify_guardian: true },
    enabled: true,
    priority: 'low'
  },
  
  // Report mappings
  {
    event_type: EDUCATION_EVENTS.REPORT_AVAILABLE,
    template_key: 'education.report.available',
    conditions: { notify_guardian: true },
    enabled: true,
    priority: 'medium'
  },
  
  // Event mappings
  {
    event_type: EDUCATION_EVENTS.EVENT_REMINDER,
    template_key: 'education.event.reminder',
    conditions: { days_before: 1 },
    enabled: true,
    priority: 'medium'
  },
  
  // Transport mappings
  {
    event_type: EDUCATION_EVENTS.TRANSPORT_DELAY,
    template_key: 'education.transport.delay',
    conditions: { delay_minutes: { gte: 15 } },
    enabled: true,
    priority: 'medium'
  },
  
  // Communication mappings
  {
    event_type: EDUCATION_EVENTS.MESSAGE_FROM_TEACHER,
    template_key: 'education.message.from_teacher',
    conditions: { urgency: { in: ['medium', 'high', 'urgent'] } },
    enabled: true,
    priority: 'medium'
  },
  {
    event_type: EDUCATION_EVENTS.ANNOUNCEMENT_GENERAL,
    template_key: 'education.announcement.general',
    conditions: { target_audience: 'all' },
    enabled: true,
    priority: 'medium'
  }
] as const;
