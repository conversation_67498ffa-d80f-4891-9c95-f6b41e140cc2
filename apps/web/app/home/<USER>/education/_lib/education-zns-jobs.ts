/**
 * Education ZNS Jobs
 * Scheduled jobs để gửi ZNS notifications tự động
 */

import { createClient } from '@kit/supabase/server-client';
import { educationZnsService } from './education-zns-service';

export class EducationZnsJobs {
  private supabase = createClient();

  /**
   * Daily Payment Due Reminders
   * Chạy hàng ngày lúc 9:00 AM để nhắc nhở học phí sắp đến hạn
   */
  async sendDailyPaymentReminders() {
    console.log('Starting daily payment reminders job...');

    try {
      // Get fees due in 3 days
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

      const { data: upcomingFees } = await this.supabase
        .from('fees')
        .select(`
          id,
          account_id,
          due_date,
          status,
          learner:learners(id, full_name)
        `)
        .eq('status', 'pending')
        .lte('due_date', threeDaysFromNow.toISOString().split('T')[0])
        .gte('due_date', new Date().toISOString().split('T')[0]);

      if (!upcomingFees?.length) {
        console.log('No upcoming fees found');
        return;
      }

      // Send reminders
      for (const fee of upcomingFees) {
        await educationZnsService.emitPaymentEvent(
          fee.account_id,
          'due_reminder',
          { fee_id: fee.id }
        );
      }

      console.log(`Sent ${upcomingFees.length} payment reminders`);
    } catch (error) {
      console.error('Error in daily payment reminders job:', error);
    }
  }

  /**
   * Weekly Overdue Payment Notifications
   * Chạy hàng tuần để thông báo học phí quá hạn
   */
  async sendWeeklyOverdueNotifications() {
    console.log('Starting weekly overdue notifications job...');

    try {
      const today = new Date().toISOString().split('T')[0];

      const { data: overdueFees } = await this.supabase
        .from('fees')
        .select(`
          id,
          account_id,
          due_date,
          status
        `)
        .eq('status', 'pending')
        .lt('due_date', today);

      if (!overdueFees?.length) {
        console.log('No overdue fees found');
        return;
      }

      // Send overdue notifications
      for (const fee of overdueFees) {
        await educationZnsService.emitPaymentEvent(
          fee.account_id,
          'overdue',
          { fee_id: fee.id }
        );
      }

      console.log(`Sent ${overdueFees.length} overdue notifications`);
    } catch (error) {
      console.error('Error in weekly overdue notifications job:', error);
    }
  }

  /**
   * Daily Event Reminders
   * Chạy hàng ngày để nhắc nhở sự kiện sắp diễn ra
   */
  async sendDailyEventReminders() {
    console.log('Starting daily event reminders job...');

    try {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowStr = tomorrow.toISOString().split('T')[0];

      const { data: upcomingEvents } = await this.supabase
        .from('events')
        .select(`
          id,
          account_id,
          title,
          start_datetime,
          event_registrations!inner(
            learner_id
          )
        `)
        .gte('start_datetime', tomorrowStr)
        .lt('start_datetime', tomorrowStr + 'T23:59:59');

      if (!upcomingEvents?.length) {
        console.log('No upcoming events found');
        return;
      }

      // Send event reminders
      for (const event of upcomingEvents) {
        const learnerIds = event.event_registrations.map(reg => reg.learner_id);
        
        if (learnerIds.length > 0) {
          await educationZnsService.emitEventActivityEvent(
            event.account_id,
            'reminder',
            {
              event_id: event.id,
              learner_ids: learnerIds
            }
          );
        }
      }

      console.log(`Sent reminders for ${upcomingEvents.length} events`);
    } catch (error) {
      console.error('Error in daily event reminders job:', error);
    }
  }

  /**
   * Weekly Attendance Summary
   * Chạy hàng tuần để gửi tổng kết điểm danh
   */
  async sendWeeklyAttendanceSummary() {
    console.log('Starting weekly attendance summary job...');

    try {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      const weekAgoStr = oneWeekAgo.toISOString().split('T')[0];
      const todayStr = new Date().toISOString().split('T')[0];

      // Get accounts with attendance data
      const { data: accounts } = await this.supabase
        .from('accounts')
        .select('id, name');

      if (!accounts?.length) return;

      for (const org of accounts) {
        // Get attendance summary for this account
        const { data: attendanceData } = await this.supabase
          .from('attendance')
          .select(`
            status,
            learner:learners!inner(
              id,
              full_name,
              account_id,
              learner_guardians!inner(
                guardian:guardians(
                  phone,
                  user:auth.users(phone)
                )
              )
            )
          `)
          .eq('learners.account_id', org.id)
          .gte('session_date', weekAgoStr)
          .lte('session_date', todayStr);

        if (!attendanceData?.length) continue;

        // Calculate attendance statistics
        const totalSessions = attendanceData.length;
        const presentSessions = attendanceData.filter(a => a.status === 'present').length;
        const attendanceRate = Math.round((presentSessions / totalSessions) * 100);

        // Group by learner
        const learnerStats = attendanceData.reduce((acc, record) => {
          const learnerId = record.learner.id;
          if (!acc[learnerId]) {
            acc[learnerId] = {
              learner: record.learner,
              total: 0,
              present: 0,
              absent: 0,
              late: 0
            };
          }
          acc[learnerId].total++;
          acc[learnerId][record.status]++;
          return acc;
        }, {} as any);

        // Send summary to guardians with low attendance
        for (const [learnerId, stats] of Object.entries(learnerStats)) {
          const learnerAttendanceRate = Math.round((stats.present / stats.total) * 100);
          
          if (learnerAttendanceRate < 80) { // Send notification if attendance < 80%
            await educationZnsService.emitCommunicationEvent(
              org.id,
              'announcement',
              {
                title: 'Tổng kết điểm danh tuần',
                content: `Tỷ lệ điểm danh của ${stats.learner.full_name} tuần qua: ${learnerAttendanceRate}% (${stats.present}/${stats.total} buổi). Vui lòng chú ý đưa con đến lớp đầy đủ.`,
                sender_name: org.name,
                sender_role: 'Hệ thống',
                urgency: 'medium',
                target_audience: 'individual',
                learner_ids: [learnerId]
              }
            );
          }
        }
      }

      console.log('Completed weekly attendance summary job');
    } catch (error) {
      console.error('Error in weekly attendance summary job:', error);
    }
  }

  /**
   * Daily Health Checkup Reminders
   * Nhắc nhở kiểm tra sức khỏe định kỳ
   */
  async sendHealthCheckupReminders() {
    console.log('Starting health checkup reminders job...');

    try {
      // Get learners who need health checkups (every 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const { data: learnersNeedingCheckup } = await this.supabase
        .from('learners')
        .select(`
          id,
          full_name,
          account_id,
          health_records(
            record_date,
            record_type
          )
        `)
        .order('health_records.record_date', { ascending: false });

      if (!learnersNeedingCheckup?.length) return;

      for (const learner of learnersNeedingCheckup) {
        const lastCheckup = learner.health_records
          ?.find(record => record.record_type === 'checkup')?.record_date;

        if (!lastCheckup || new Date(lastCheckup) < sixMonthsAgo) {
          await educationZnsService.emitHealthEvent(
            learner.account_id,
            'checkup_due',
            {
              learner_id: learner.id,
              description: 'Đã đến thời gian kiểm tra sức khỏe định kỳ (6 tháng)',
              severity: 'medium'
            }
          );
        }
      }

      console.log('Completed health checkup reminders job');
    } catch (error) {
      console.error('Error in health checkup reminders job:', error);
    }
  }

  /**
   * Transportation Pickup Reminders
   * Nhắc nhở đón trả học sinh
   */
  async sendTransportPickupReminders() {
    console.log('Starting transport pickup reminders job...');

    try {
      const now = new Date();
      const in30Minutes = new Date(now.getTime() + 30 * 60000); // 30 minutes from now
      const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
      const reminderTime = in30Minutes.toTimeString().slice(0, 5);

      // Get routes with pickup times in the next 30 minutes
      const { data: routes } = await this.supabase
        .from('transportation_routes')
        .select(`
          id,
          account_id,
          route_name,
          schedule,
          stops
        `)
        .eq('is_active', true);

      if (!routes?.length) return;

      for (const route of routes) {
        const schedule = route.schedule;
        const departureTime = schedule?.departure_time;

        if (departureTime && departureTime >= currentTime && departureTime <= reminderTime) {
          // Get learners on this route
          const learnerIds = route.stops?.flatMap((stop: any) => stop.learner_ids || []) || [];

          if (learnerIds.length > 0) {
            await educationZnsService.emitTransportEvent(
              route.account_id,
              'pickup_reminder',
              {
                route_id: route.id,
                description: `Xe đưa đón sẽ khởi hành lúc ${departureTime}. Vui lòng chuẩn bị sẵn sàng.`,
                affected_learner_ids: learnerIds
              }
            );
          }
        }
      }

      console.log('Completed transport pickup reminders job');
    } catch (error) {
      console.error('Error in transport pickup reminders job:', error);
    }
  }

  /**
   * Monthly Usage Report
   * Báo cáo sử dụng ZNS hàng tháng
   */
  async generateMonthlyUsageReport() {
    console.log('Starting monthly usage report job...');

    try {
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      const monthStr = lastMonth.toISOString().slice(0, 7); // YYYY-MM

      // Get accounts
      const { data: accounts } = await this.supabase
        .from('accounts')
        .select('id, name');

      if (!accounts?.length) return;

      for (const org of accounts) {
        // Get ZNS usage data for last month
        const { data: usageData } = await this.supabase
          .from('zns_usage')
          .select('*')
          .eq('account_id', org.id)
          .like('sent_at', `${monthStr}%`);

        if (!usageData?.length) continue;

        const totalMessages = usageData.reduce((sum, usage) => sum + usage.message_count, 0);
        const totalCost = usageData.reduce((sum, usage) => sum + usage.cost, 0);

        // Send usage report to account admins
        await educationZnsService.emitCommunicationEvent(
          org.id,
          'announcement',
          {
            title: `Báo cáo sử dụng ZNS tháng ${monthStr}`,
            content: `Tổng số tin nhắn: ${totalMessages}\nTổng chi phí: ${totalCost.toLocaleString('vi-VN')} VND\nChi phí trung bình/tin: ${Math.round(totalCost / totalMessages).toLocaleString('vi-VN')} VND`,
            sender_name: 'Hệ thống',
            sender_role: 'Báo cáo tự động',
            urgency: 'low',
            target_audience: 'all'
          }
        );
      }

      console.log('Completed monthly usage report job');
    } catch (error) {
      console.error('Error in monthly usage report job:', error);
    }
  }
}

// Export singleton instance
export const educationZnsJobs = new EducationZnsJobs();

// Job scheduler configuration
export const ZNS_JOB_SCHEDULES = {
  // Daily jobs
  dailyPaymentReminders: {
    schedule: '0 9 * * *', // 9:00 AM daily
    handler: () => educationZnsJobs.sendDailyPaymentReminders(),
    description: 'Send daily payment due reminders'
  },
  
  dailyEventReminders: {
    schedule: '0 18 * * *', // 6:00 PM daily
    handler: () => educationZnsJobs.sendDailyEventReminders(),
    description: 'Send daily event reminders'
  },
  
  healthCheckupReminders: {
    schedule: '0 10 * * 1', // 10:00 AM every Monday
    handler: () => educationZnsJobs.sendHealthCheckupReminders(),
    description: 'Send health checkup reminders'
  },
  
  transportPickupReminders: {
    schedule: '*/30 6-18 * * 1-5', // Every 30 minutes from 6 AM to 6 PM, weekdays
    handler: () => educationZnsJobs.sendTransportPickupReminders(),
    description: 'Send transport pickup reminders'
  },
  
  // Weekly jobs
  weeklyOverdueNotifications: {
    schedule: '0 9 * * 1', // 9:00 AM every Monday
    handler: () => educationZnsJobs.sendWeeklyOverdueNotifications(),
    description: 'Send weekly overdue payment notifications'
  },
  
  weeklyAttendanceSummary: {
    schedule: '0 17 * * 5', // 5:00 PM every Friday
    handler: () => educationZnsJobs.sendWeeklyAttendanceSummary(),
    description: 'Send weekly attendance summary'
  },
  
  // Monthly jobs
  monthlyUsageReport: {
    schedule: '0 8 1 * *', // 8:00 AM on 1st of every month
    handler: () => educationZnsJobs.generateMonthlyUsageReport(),
    description: 'Generate monthly ZNS usage report'
  }
};
