'use server';

import { randomUUID } from 'crypto';
import sharp from 'sharp';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

const MEDIA_BUCKET = 'media-library';

function sanitizeFileName(fileName: string): string {
  return fileName.toLowerCase().replace(/[^a-z0-9.]/g, '-');
}

async function convertToWebp(file: File): Promise<Buffer> {
  const buffer = Buffer.from(await file.arrayBuffer());
  return sharp(buffer).webp({ quality: 80 }).toBuffer();
}

function getFileType(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  return 'document';
}

function generateThumbnail(file: File): Promise<Buffer | null> {
  if (!file.type.startsWith('image/')) return Promise.resolve(null);
  
  return convertToWebp(file).then(buffer => 
    sharp(buffer).resize(300, 300, { fit: 'cover' }).webp({ quality: 70 }).toBuffer()
  );
}

export async function uploadMediaFile(
  file: File,
  accountId: string,
  albumId?: string,
): Promise<{
  url: string;
  thumbnailUrl?: string;
  tempPath?: string;
  fileType: string;
  fileSize: number;
  mimeType: string;
}> {
  console.log('uploadMediaFile called with:', { fileName: file.name, accountId, albumId });

  if (!accountId) throw new Error('Account ID is required');

  const supabase = getSupabaseServerClient();
  const fileType = getFileType(file.type);
  const isImage = fileType === 'image';
  const sanitizedName = sanitizeFileName(file.name);
  const webpName = isImage ? sanitizedName.replace(/\.[^/.]+$/, '.webp') : sanitizedName;

  console.log('File processing:', { fileType, isImage, sanitizedName, webpName });
  
  const isTempUpload = !albumId;
  const tempFolder = randomUUID();
  const tempPath = isTempUpload
    ? `temp/${tempFolder}/${accountId}/${randomUUID()}-${webpName}`
    : undefined;
  const finalPath = isTempUpload
    ? tempPath
    : `${accountId}/${albumId}/${webpName}`;

  console.log('Uploading to bucket:', MEDIA_BUCKET, 'Path:', finalPath);

  // Convert images to WebP, keep other files as-is
  const fileBuffer = isImage 
    ? await convertToWebp(file)
    : Buffer.from(await file.arrayBuffer());

  const contentType = isImage ? 'image/webp' : file.type;

  const { error } = await supabase.storage
    .from(MEDIA_BUCKET)
    .upload(finalPath!, fileBuffer, {
      contentType,
      upsert: true,
    });

  if (error) {
    console.error('Upload error:', error);
    throw error;
  }

  const {
    data: { publicUrl },
  } = supabase.storage.from(MEDIA_BUCKET).getPublicUrl(finalPath!);

  // Generate thumbnail for images
  let thumbnailUrl: string | undefined;
  if (isImage) {
    try {
      const thumbnailBuffer = await generateThumbnail(file);
      if (thumbnailBuffer) {
        const thumbnailPath = finalPath!.replace(/\.[^/.]+$/, '-thumb.webp');
        
        const { error: thumbError } = await supabase.storage
          .from(MEDIA_BUCKET)
          .upload(thumbnailPath, thumbnailBuffer, {
            contentType: 'image/webp',
            upsert: true,
          });

        if (!thumbError) {
          const { data: { publicUrl: thumbPublicUrl } } = supabase.storage
            .from(MEDIA_BUCKET)
            .getPublicUrl(thumbnailPath);
          thumbnailUrl = thumbPublicUrl;
        }
      }
    } catch (error) {
      console.error('Failed to generate thumbnail:', error);
      // Continue without thumbnail
    }
  }

  // Save temp file metadata if it's a temp upload
  if (isTempUpload) {
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // Expires after 24 hours
    const { error: dbError } = await supabase.from('temp_media_files').insert({
      temp_path: finalPath,
      url: publicUrl,
      thumbnail_url: thumbnailUrl,
      file_type: fileType,
      file_size: file.size,
      mime_type: file.type,
      original_filename: file.name,
      expires_at: expiresAt.toISOString(),
      account_id: accountId,
    });

    if (dbError) {
      console.error('Failed to save temp media metadata:', dbError);
      // Clean up uploaded files
      await supabase.storage.from(MEDIA_BUCKET).remove([finalPath!]);
      if (thumbnailUrl) {
        const thumbnailPath = finalPath!.replace(/\.[^/.]+$/, '-thumb.webp');
        await supabase.storage.from(MEDIA_BUCKET).remove([thumbnailPath]);
      }
      throw new Error('Failed to save temp media metadata');
    }
  }

  return {
    url: publicUrl,
    thumbnailUrl,
    tempPath: isTempUpload ? finalPath : undefined,
    fileType,
    fileSize: file.size,
    mimeType: file.type,
  };
}

export async function moveTempMediaFile(
  tempPath: string,
  albumId: string,
  accountId: string,
): Promise<{ url: string; thumbnailUrl?: string }> {
  const supabase = getSupabaseServerClient();
  const fileName = tempPath.split('/').pop()!;
  const newPath = `${accountId}/${albumId}/${fileName}`;

  const { error } = await supabase.storage
    .from(MEDIA_BUCKET)
    .move(tempPath, newPath);

  if (error) {
    console.error('Move error:', error);
    throw error;
  }

  // Move thumbnail if exists
  let thumbnailUrl: string | undefined;
  const thumbnailTempPath = tempPath.replace(/\.[^/.]+$/, '-thumb.webp');
  const thumbnailNewPath = newPath.replace(/\.[^/.]+$/, '-thumb.webp');
  
  const { error: thumbMoveError } = await supabase.storage
    .from(MEDIA_BUCKET)
    .move(thumbnailTempPath, thumbnailNewPath);

  if (!thumbMoveError) {
    const { data: { publicUrl: thumbPublicUrl } } = supabase.storage
      .from(MEDIA_BUCKET)
      .getPublicUrl(thumbnailNewPath);
    thumbnailUrl = thumbPublicUrl;
  }

  // Update temp file metadata
  const { error: dbError } = await supabase
    .from('temp_media_files')
    .update({ is_moved: true })
    .eq('temp_path', tempPath);

  if (dbError) {
    console.error('Failed to update temp media metadata:', dbError);
  }

  const {
    data: { publicUrl },
  } = supabase.storage.from(MEDIA_BUCKET).getPublicUrl(newPath);
  
  return { url: publicUrl, thumbnailUrl };
}

export async function deleteMediaFile(url: string) {
  const supabase = getSupabaseServerClient();
  const urlPath = new URL(url).pathname;
  const filePath = urlPath.split('/').slice(2).join('/');
  if (!filePath) return;

  // Delete thumbnail if exists
  const thumbnailPath = filePath.replace(/\.[^/.]+$/, '-thumb.webp');
  await supabase.storage.from(MEDIA_BUCKET).remove([thumbnailPath]);

  // Delete temp metadata
  await supabase.from('temp_media_files').delete().eq('url', url);
  
  // Delete main file
  return supabase.storage.from(MEDIA_BUCKET).remove([filePath]);
}

export async function createMediaFileRecord(
  accountId: string,
  albumId: string,
  fileData: {
    title: string;
    description?: string;
    url: string;
    thumbnailUrl?: string;
    fileType: string;
    fileSize: number;
    mimeType: string;
    originalFilename: string;
    tags?: string[];
    privacyLevel?: string;
  }
) {
  const supabase = getSupabaseServerClient();
  
  const { data, error } = await supabase
    .from('media_files')
    .insert({
      account_id: accountId,
      album_id: albumId,
      title: fileData.title,
      description: fileData.description,
      file_url: fileData.url,
      thumbnail_url: fileData.thumbnailUrl,
      file_type: fileData.fileType,
      file_size: fileData.fileSize,
      mime_type: fileData.mimeType,
      original_filename: fileData.originalFilename,
      tags: fileData.tags || [],
      privacy_level: fileData.privacyLevel || 'staff_only',
    })
    .select()
    .single();

  if (error) {
    console.error('Failed to create media file record:', error);
    throw error;
  }

  return data;
}
