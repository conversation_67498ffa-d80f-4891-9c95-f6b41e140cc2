'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Folder, 
  Plus,
  Image,
  Video,
  FileText,
  Download,
  Share2,
  Eye,
  Calendar,
  Users,
  Lock,
  Globe,
  Shield
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Label } from '@kit/ui/label';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';

interface Props {
  account: string;
  albumId: string;
}

function AlbumDetails({ account, albumId }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteOption, setDeleteOption] = useState<'delete_files' | 'move_files' | 'unorganized'>('unorganized');
  const [availableAlbums, setAvailableAlbums] = useState<any[]>([]);
  const [selectedTargetAlbum, setSelectedTargetAlbum] = useState<string>('');
  const [album, setAlbum] = useState<any>(null);
  const [mediaFiles, setMediaFiles] = useState<any[]>([]);

  useEffect(() => {
    loadAlbumDetails();
  }, [albumId]);

  const loadAlbumDetails = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      // Load album details
      const { data: albumData, error: albumError } = await supabase
        .from('media_albums')
        .select('*')
        .eq('id', albumId)
        .eq('account_id', accountId)
        .single();

      if (albumError) throw albumError;

      // Load media files
      const { data: filesData, error: filesError } = await supabase
        .from('media_files')
        .select('*')
        .eq('album_id', albumId)
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (filesError) throw filesError;

      setAlbum(albumData);
      setMediaFiles(filesData || []);
    } catch (error) {
      console.error('Error loading album details:', error);
      toast.error('Có lỗi xảy ra khi tải thông tin album');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = async () => {
    // Load available albums for moving files
    try {
      const accountId = workspace.account.id;
      const { data: albumsData, error } = await supabase
        .from('media_albums')
        .select('id, name, album_type')
        .eq('account_id', accountId)
        .neq('id', albumId) // Exclude current album
        .order('name');

      if (!error && albumsData) {
        setAvailableAlbums(albumsData);
      }
    } catch (error) {
      console.error('Error loading albums:', error);
    }

    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      setDeleting(true);
      setShowDeleteDialog(false);
      const accountId = workspace.account.id;

      // Handle files based on user choice
      if (deleteOption === 'move_files' && selectedTargetAlbum) {
        // Move files to selected album
        const { error: moveError } = await supabase
          .from('media_files')
          .update({ album_id: selectedTargetAlbum })
          .eq('album_id', albumId)
          .eq('account_id', accountId);

        if (moveError) throw moveError;
      } else if (deleteOption === 'unorganized') {
        // Set files to unorganized (album_id = null)
        const { error: unorganizeError } = await supabase
          .from('media_files')
          .update({ album_id: null })
          .eq('album_id', albumId)
          .eq('account_id', accountId);

        if (unorganizeError) throw unorganizeError;
      } else if (deleteOption === 'delete_files') {
        // Get all files in the album first to delete from storage
        const { data: filesToDelete, error: fetchError } = await supabase
          .from('media_files')
          .select('file_url, thumbnail_url')
          .eq('album_id', albumId)
          .eq('account_id', accountId);

        if (fetchError) throw fetchError;

        // Delete files from storage
        if (filesToDelete && filesToDelete.length > 0) {
          const filePaths: string[] = [];

          filesToDelete.forEach((file) => {
            // Extract file path from URL for main file
            if (file.file_url) {
              const mainPath = extractStoragePath(file.file_url);
              if (mainPath) filePaths.push(mainPath);
            }

            // Extract file path from URL for thumbnail
            if (file.thumbnail_url) {
              const thumbPath = extractStoragePath(file.thumbnail_url);
              if (thumbPath) filePaths.push(thumbPath);
            }
          });

          // Delete from storage bucket
          if (filePaths.length > 0) {
            const { error: storageError } = await supabase.storage
              .from('media-library')
              .remove(filePaths);

            if (storageError) {
              console.error('Error deleting files from storage:', storageError);
              // Continue with database deletion even if storage deletion fails
            }
          }
        }

        // Delete files from database
        const { error: deleteFilesError } = await supabase
          .from('media_files')
          .delete()
          .eq('album_id', albumId)
          .eq('account_id', accountId);

        if (deleteFilesError) throw deleteFilesError;
      }

      // Delete the album
      const { error } = await supabase
        .from('media_albums')
        .delete()
        .eq('id', albumId)
        .eq('account_id', accountId);

      if (error) throw error;

      const fileAction = deleteOption === 'delete_files'
        ? 'và tất cả files đã bị xóa'
        : deleteOption === 'move_files'
        ? 'và files đã được chuyển sang album khác'
        : 'và files đã được chuyển vào mục "Chưa phân loại"';

      toast.success(`Xóa album thành công ${fileAction}!`);

      // Force refresh the library dashboard by redirecting with a timestamp
      const timestamp = Date.now();
      router.push(`/home/<USER>/education/library?refresh=${timestamp}`);
    } catch (error) {
      console.error('Error deleting album:', error);
      toast.error('Có lỗi xảy ra khi xóa album');
    } finally {
      setDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setDeleteOption('unorganized');
    setSelectedTargetAlbum('');
  };

  // Helper function to extract storage path from URL
  const extractStoragePath = (url: string): string | null => {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      // Remove empty parts and find the path after 'object'
      const cleanParts = pathParts.filter(part => part !== '');
      const objectIndex = cleanParts.findIndex(part => part === 'object');
      if (objectIndex !== -1 && objectIndex < cleanParts.length - 1) {
        return cleanParts.slice(objectIndex + 2).join('/'); // Skip 'object' and bucket name
      }
      return null;
    } catch (error) {
      console.error('Error extracting storage path:', error);
      return null;
    }
  };

  const getAlbumTypeText = (type: string) => {
    const types = {
      general: 'Chung',
      event: 'Sự kiện',
      class: 'Lớp học',
      activity: 'Hoạt động'
    };
    return types[type as keyof typeof types] || type;
  };

  const getPrivacyText = (privacy: string) => {
    const privacies = {
      public: 'Công khai',
      parents_only: 'Chỉ phụ huynh',
      staff_only: 'Chỉ nhân viên',
      private: 'Riêng tư'
    };
    return privacies[privacy as keyof typeof privacies] || privacy;
  };

  const getPrivacyIcon = (privacy: string) => {
    switch (privacy) {
      case 'public': return <Globe className="h-4 w-4" />;
      case 'parents_only': return <Users className="h-4 w-4" />;
      case 'staff_only': return <Shield className="h-4 w-4" />;
      default: return <Lock className="h-4 w-4" />;
    }
  };

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'image': return <Image className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  if (loading) {
    return (
      <div className="container mx-auto max-w-7xl space-y-6 px-4 py-6">
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  if (!album) {
    return (
      <div className="container mx-auto max-w-7xl space-y-6 px-4 py-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Album không tồn tại</h2>
          <p className="text-gray-600 mb-4">Album bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
          <Button asChild>
            <Link href={`/home/<USER>/education/library/albums`}>
              Quay lại danh sách album
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  const fileTypeCounts = mediaFiles.reduce((counts, file) => {
    if (file.file_type === 'image') counts.images++;
    else if (file.file_type === 'video') counts.videos++;
    else counts.documents++;
    return counts;
  }, { images: 0, videos: 0, documents: 0 });

  return (
    <div className="space-y-8">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lại</span>
        </Button>

        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/home/<USER>/education/library/albums/${albumId}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Link>
          </Button>
          <Button
            variant="outline"
            className="text-red-600 hover:text-red-700"
            onClick={handleDeleteClick}
            disabled={deleting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {deleting ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </div>
      </div>

      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600 via-purple-600 to-violet-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Folder className="h-6 w-6" />
                </div>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant="outline" 
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    📁 {getAlbumTypeText(album.album_type)}
                  </Badge>
                  <Badge 
                    variant="outline" 
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    {getPrivacyIcon(album.privacy_level)}
                    <span className="ml-1">{getPrivacyText(album.privacy_level)}</span>
                  </Badge>
                </div>
              </div>
              <h1 className="text-4xl font-bold mb-2 leading-tight">{album.name}</h1>
              <div className="text-white/90 text-lg space-y-1">
                {album.description && (
                  <p>📝 Mô tả: <span className="font-semibold">{album.description}</span></p>
                )}
                <p>📊 Tổng file: <span className="font-semibold">{mediaFiles.length} file</span></p>
                <p>📅 Tạo lúc: <span className="font-semibold">{formatDateTime(album.created_at)}</span></p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Image className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{fileTypeCounts.images}</div>
                <div className="text-gray-600">Hình ảnh</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-green-100 rounded-lg">
                <Video className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{fileTypeCounts.videos}</div>
                <div className="text-gray-600">Video</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-purple-100 rounded-lg">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{fileTypeCounts.documents}</div>
                <div className="text-gray-600">Tài liệu</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Media Files */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Folder className="h-5 w-5 text-indigo-600" />
              </div>
              Media Files ({mediaFiles.length})
            </CardTitle>
            <Button asChild>
              <Link href={`/home/<USER>/education/library/upload?albumId=${albumId}`}>
                <Plus className="h-4 w-4 mr-2" />
                Thêm file
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {mediaFiles.length === 0 ? (
            <div className="text-center py-12">
              <Folder className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Chưa có file nào</h3>
              <p className="text-gray-600 mb-4">Album này chưa có file media nào.</p>
              <Button asChild>
                <Link href={`/home/<USER>/education/library/upload?albumId=${albumId}`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm file đầu tiên
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {mediaFiles.map((file) => (
                <div key={file.id} className="group relative bg-gray-50 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                  <div className="aspect-square bg-gray-100 flex items-center justify-center">
                    {file.thumbnail_url ? (
                      <img
                        src={file.thumbnail_url}
                        alt={file.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        {getFileTypeIcon(file.file_type)}
                      </div>
                    )}
                  </div>
                  <div className="p-3">
                    <h4 className="font-medium text-sm text-gray-900 truncate">{file.title}</h4>
                    <p className="text-xs text-gray-500 mt-1">{file.file_type}</p>
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex items-center gap-1">
                      <Button variant="secondary" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button variant="secondary" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 className="h-5 w-5" />
              Xác nhận xóa album
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              Bạn có chắc chắn muốn xóa album <strong>"{album?.name}"</strong> không?
              <br />
              Album này hiện có <strong>{mediaFiles.length} files</strong>. Bạn muốn xử lý các files này như thế nào?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <RadioGroup value={deleteOption} onValueChange={(value: any) => setDeleteOption(value)}>
              <div className="space-y-3">
                <div className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                  <RadioGroupItem value="unorganized" id="unorganized" className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor="unorganized" className="font-medium cursor-pointer">
                      Chuyển vào "Chưa phân loại" (Khuyến nghị)
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">
                      Files sẽ được giữ lại và có thể tái tổ chức sau
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                  <RadioGroupItem value="move_files" id="move_files" className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor="move_files" className="font-medium cursor-pointer">
                      Chuyển sang album khác
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">
                      Chọn album đích để chuyển tất cả files
                    </p>
                    {deleteOption === 'move_files' && (
                      <div className="mt-3">
                        <Select value={selectedTargetAlbum} onValueChange={setSelectedTargetAlbum}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Chọn album đích..." />
                          </SelectTrigger>
                          <SelectContent>
                            {availableAlbums.map((album) => (
                              <SelectItem key={album.id} value={album.id}>
                                {album.name} ({album.album_type})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                  <RadioGroupItem value="delete_files" id="delete_files" className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor="delete_files" className="font-medium cursor-pointer text-red-600">
                      Xóa tất cả files
                    </Label>
                    <p className="text-sm text-red-600 mt-1">
                      ⚠️ Không thể hoàn tác! Tất cả files sẽ bị xóa vĩnh viễn
                    </p>
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>

          <DialogFooter className="flex gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={deleting}
            >
              Hủy
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleting || (deleteOption === 'move_files' && !selectedTargetAlbum)}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Đang xóa...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Xóa album
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export { AlbumDetails };
