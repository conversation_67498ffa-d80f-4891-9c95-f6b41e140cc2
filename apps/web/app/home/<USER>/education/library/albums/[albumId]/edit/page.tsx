import { redirect } from 'next/navigation';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import { EditAlbumForm } from './_components/edit-album-form';

interface Props {
  params: Promise<{
    account: string;
    albumId: string;
  }>;
}

export const metadata = {
  title: 'Chỉnh sửa Album - Thư viện Media',
  description: 'Chỉnh sửa thông tin album',
};

async function EditAlbumPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-36">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title="Chỉnh sửa Album"
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/library`}
            >
              Thư viện Media
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/library/albums`}
            >
              Albums
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>Chỉnh sửa</AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-4xl">
        <EditAlbumForm account={resolvedParams.account} albumId={resolvedParams.albumId} />
      </div>
    </div>
  );
}

export default EditAlbumPage;
