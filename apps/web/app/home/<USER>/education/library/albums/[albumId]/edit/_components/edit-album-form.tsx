'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Folder, Save, X } from 'lucide-react';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';

interface Props {
  account: string;
  albumId: string;
}

function EditAlbumForm({ account, albumId }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    album_type: 'general',
    privacy_level: 'staff_only',
  });

  useEffect(() => {
    loadAlbumData();
  }, [albumId]);

  const loadAlbumData = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      const { data, error } = await supabase
        .from('media_albums')
        .select('*')
        .eq('id', albumId)
        .eq('account_id', accountId)
        .single();

      if (error) throw error;

      setFormData({
        name: data.name || '',
        description: data.description || '',
        album_type: data.album_type || 'general',
        privacy_level: data.privacy_level || 'staff_only',
      });
    } catch (error) {
      console.error('Error loading album:', error);
      toast.error('Có lỗi xảy ra khi tải thông tin album');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Vui lòng nhập tên album');
      return;
    }

    try {
      setSaving(true);
      const accountId = workspace.account.id;

      const { error } = await supabase
        .from('media_albums')
        .update({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          album_type: formData.album_type,
          privacy_level: formData.privacy_level,
          updated_at: new Date().toISOString(),
        })
        .eq('id', albumId)
        .eq('account_id', accountId);

      if (error) throw error;

      toast.success('Cập nhật album thành công!');
      router.push(`/home/<USER>/education/library/albums/${albumId}`);
    } catch (error) {
      console.error('Error updating album:', error);
      toast.error('Có lỗi xảy ra khi cập nhật album');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (loading) {
    return (
      <div className="container mx-auto max-w-4xl space-y-6 px-4 py-6">
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl space-y-8 px-4 py-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lại</span>
        </Button>
      </div>

      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600 via-violet-600 to-purple-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
              <Folder className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Chỉnh sửa Album</h1>
              <p className="text-white/90 text-lg">Cập nhật thông tin album của bạn</p>
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Form */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <Folder className="h-5 w-5 text-indigo-600" />
            </div>
            Thông tin album
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Album Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                  Tên album *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Nhập tên album..."
                  className="h-12 bg-white border-2 border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors"
                  required
                />
              </div>

              {/* Album Type */}
              <div className="space-y-2">
                <Label htmlFor="album_type" className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                  Loại album
                </Label>
                <Select value={formData.album_type} onValueChange={(value) => handleInputChange('album_type', value)}>
                  <SelectTrigger className="h-12 bg-white border-2 border-gray-200 hover:border-indigo-300 transition-colors">
                    <SelectValue placeholder="Chọn loại album" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">Chung</SelectItem>
                    <SelectItem value="event">Sự kiện</SelectItem>
                    <SelectItem value="class">Lớp học</SelectItem>
                    <SelectItem value="activity">Hoạt động</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Privacy Level */}
              <div className="space-y-2">
                <Label htmlFor="privacy_level" className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                  Quyền riêng tư
                </Label>
                <Select value={formData.privacy_level} onValueChange={(value) => handleInputChange('privacy_level', value)}>
                  <SelectTrigger className="h-12 bg-white border-2 border-gray-200 hover:border-indigo-300 transition-colors">
                    <SelectValue placeholder="Chọn quyền riêng tư" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Công khai</SelectItem>
                    <SelectItem value="parents_only">Chỉ phụ huynh</SelectItem>
                    <SelectItem value="staff_only">Chỉ nhân viên</SelectItem>
                    <SelectItem value="private">Riêng tư</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                Mô tả
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Nhập mô tả album..."
                rows={4}
                className="bg-white border-2 border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors resize-none"
              />
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end gap-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={saving}
              >
                <X className="h-4 w-4 mr-2" />
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={saving || !formData.name.trim()}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export { EditAlbumForm };
