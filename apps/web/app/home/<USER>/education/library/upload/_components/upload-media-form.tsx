'use client';

import { use<PERSON><PERSON>back, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArrowLeft, Upload, Save, X, Plus, Trash2 } from 'lucide-react';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';
import { Badge } from '@kit/ui/badge';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';

import { createMediaFileRecord } from '../../_lib/server/upload-media-file';

interface Props {
  account: string;
}

interface UploadedFile {
  file: File;
  url: string;
  thumbnailUrl?: string;
  tempPath?: string;
  fileType: string;
  fileSize: number;
  mimeType: string;
  title: string;
  description: string;
  tags: string[];
  privacyLevel: string;
}

function UploadMediaForm({ account }: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(false);
  const [albums, setAlbums] = useState<any[]>([]);
  const [selectedAlbumId, setSelectedAlbumId] = useState<string>('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  // Get albumId from URL params if provided
  const preselectedAlbumId = searchParams.get('albumId');

  useEffect(() => {
    loadAlbums();
    if (preselectedAlbumId) {
      setSelectedAlbumId(preselectedAlbumId);
    }
  }, [preselectedAlbumId]);

  const loadAlbums = async () => {
    try {
      const accountId = workspace.account.id;
      const { data, error } = await supabase
        .from('media_albums')
        .select('id, name, album_type')
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAlbums(data || []);
    } catch (error) {
      console.error('Error loading albums:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách album');
    }
  };

  const handleFileSelect = useCallback(async (files: FileList) => {
    if (!files.length) return;

    try {
      setLoading(true);
      const accountId = workspace.account.id;
      const newFiles: UploadedFile[] = [];

      for (const file of Array.from(files)) {
        // Validate file size (max 50MB)
        if (file.size > 50 * 1024 * 1024) {
          toast.error(`File ${file.name} quá lớn. Kích thước tối đa là 50MB.`);
          continue;
        }

        try {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('accountId', accountId);

          const response = await fetch('/api/media/upload', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error('Upload failed');
          }

          const uploadResult = await response.json();

          newFiles.push({
            file,
            url: uploadResult.url,
            thumbnailUrl: uploadResult.thumbnailUrl,
            tempPath: uploadResult.tempPath,
            fileType: uploadResult.fileType,
            fileSize: uploadResult.fileSize,
            mimeType: uploadResult.mimeType,
            title: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
            description: '',
            tags: [],
            privacyLevel: 'staff_only',
          });
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error);
          toast.error(`Không thể upload file ${file.name}`);
        }
      }

      setUploadedFiles(prev => [...prev, ...newFiles]);
      toast.success(`Đã upload ${newFiles.length} file thành công!`);
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Có lỗi xảy ra khi upload file');
    } finally {
      setLoading(false);
    }
  }, [workspace.account.id]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const updateFileData = (index: number, field: string, value: any) => {
    setUploadedFiles(prev => prev.map((file, i) => 
      i === index ? { ...file, [field]: value } : file
    ));
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const addTag = (index: number, tag: string) => {
    if (!tag.trim()) return;
    
    updateFileData(index, 'tags', [
      ...uploadedFiles[index].tags,
      tag.trim()
    ]);
  };

  const removeTag = (index: number, tagIndex: number) => {
    updateFileData(index, 'tags', 
      uploadedFiles[index].tags.filter((_, i) => i !== tagIndex)
    );
  };

  const handleSubmit = async () => {
    if (!selectedAlbumId) {
      toast.error('Vui lòng chọn album');
      return;
    }

    if (uploadedFiles.length === 0) {
      toast.error('Vui lòng upload ít nhất một file');
      return;
    }

    try {
      setLoading(true);
      const accountId = workspace.account.id;

      for (const fileData of uploadedFiles) {
        if (!fileData.title.trim()) {
          toast.error('Vui lòng nhập tiêu đề cho tất cả file');
          return;
        }

        await createMediaFileRecord(accountId, selectedAlbumId, {
          title: fileData.title,
          description: fileData.description,
          url: fileData.url,
          thumbnailUrl: fileData.thumbnailUrl,
          fileType: fileData.fileType,
          fileSize: fileData.fileSize,
          mimeType: fileData.mimeType,
          originalFilename: fileData.file.name,
          tags: fileData.tags,
          privacyLevel: fileData.privacyLevel,
        });
      }

      toast.success('Đã lưu tất cả file thành công!');
      router.push(`/home/<USER>/education/library/albums/${selectedAlbumId}`);
    } catch (error) {
      console.error('Error saving files:', error);
      toast.error('Có lỗi xảy ra khi lưu file');
    } finally {
      setLoading(false);
    }
  };

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'image': return '🖼️';
      case 'video': return '🎥';
      default: return '📄';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="container mx-auto max-w-6xl space-y-8 px-4 py-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lại</span>
        </Button>
      </div>

      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
              <Upload className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Upload Media Files</h1>
              <p className="text-white/90 text-lg">Thêm hình ảnh, video và tài liệu vào thư viện</p>
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Album Selection */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Upload className="h-5 w-5 text-blue-600" />
            </div>
            Chọn Album
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="album" className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
              Album đích *
            </Label>
            <Select value={selectedAlbumId} onValueChange={setSelectedAlbumId}>
              <SelectTrigger className="h-12 bg-white border-2 border-gray-200 hover:border-blue-300 transition-colors">
                <SelectValue placeholder="Chọn album để lưu file..." />
              </SelectTrigger>
              <SelectContent>
                {albums.map((album) => (
                  <SelectItem key={album.id} value={album.id}>
                    {album.name} ({album.album_type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* File Upload Area */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <Upload className="h-5 w-5 text-indigo-600" />
            </div>
            Upload Files
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-blue-400'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Kéo thả file vào đây hoặc click để chọn
            </h3>
            <p className="text-gray-600 mb-4">
              Hỗ trợ hình ảnh, video và tài liệu. Kích thước tối đa 50MB mỗi file.
            </p>
            <input
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.doc,.docx,.txt"
              onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
              className="hidden"
              id="file-upload"
            />
            <Button asChild>
              <label htmlFor="file-upload" className="cursor-pointer">
                <Plus className="h-4 w-4 mr-2" />
                Chọn Files
              </label>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Save className="h-5 w-5 text-green-600" />
              </div>
              Files đã upload ({uploadedFiles.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {uploadedFiles.map((fileData, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">
                        {getFileTypeIcon(fileData.fileType)}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{fileData.file.name}</h4>
                        <p className="text-sm text-gray-500">
                          {formatFileSize(fileData.fileSize)} • {fileData.mimeType}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-semibold text-gray-700">
                        Tiêu đề *
                      </Label>
                      <Input
                        value={fileData.title}
                        onChange={(e) => updateFileData(index, 'title', e.target.value)}
                        placeholder="Nhập tiêu đề file..."
                        className="h-10"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-semibold text-gray-700">
                        Quyền riêng tư
                      </Label>
                      <Select 
                        value={fileData.privacyLevel} 
                        onValueChange={(value) => updateFileData(index, 'privacyLevel', value)}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="public">Công khai</SelectItem>
                          <SelectItem value="parents_only">Chỉ phụ huynh</SelectItem>
                          <SelectItem value="staff_only">Chỉ nhân viên</SelectItem>
                          <SelectItem value="private">Riêng tư</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-semibold text-gray-700">
                      Mô tả
                    </Label>
                    <Textarea
                      value={fileData.description}
                      onChange={(e) => updateFileData(index, 'description', e.target.value)}
                      placeholder="Nhập mô tả file..."
                      rows={2}
                      className="resize-none"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-semibold text-gray-700">
                      Tags
                    </Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {fileData.tags.map((tag, tagIndex) => (
                        <Badge key={tagIndex} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => removeTag(index, tagIndex)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Thêm tag..."
                        className="h-8"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            addTag(index, e.currentTarget.value);
                            e.currentTarget.value = '';
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex items-center justify-end gap-4 pt-6 border-t mt-6">
              <Button
                variant="outline"
                onClick={() => setUploadedFiles([])}
                disabled={loading}
              >
                <X className="h-4 w-4 mr-2" />
                Xóa tất cả
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={loading || !selectedAlbumId || uploadedFiles.length === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Đang lưu...' : `Lưu ${uploadedFiles.length} file`}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export { UploadMediaForm };
