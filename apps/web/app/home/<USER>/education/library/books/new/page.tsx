import { Suspense } from 'react';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { AddBookForm } from './_components/add-book-form';
import { AddBookFormSkeleton } from './_components/add-book-form-skeleton';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

async function AddBookPage({ params }: Props) {
  const { account: accountSlug } = await params;
  const i18n = await createI18nServerInstance();
  const { user, account } = await loadTeamWorkspace(accountSlug);

  return (
    <div className="flex flex-col space-y-6 p-4 md:p-6">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:library.addBook', 'Add New Book')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/library`}>
              {i18n.t('education:breadcrumbs.library', 'Library')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:library.addBook', 'Add Book')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-4xl">
        <Suspense fallback={<AddBookFormSkeleton />}>
          <AddBookForm account={accountSlug} />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(AddBookPage);

export const metadata = {
  title: 'Add New Book',
  description: 'Add a new book to the library catalog',
};
