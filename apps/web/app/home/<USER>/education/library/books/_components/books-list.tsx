'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { BookOpen, Plus, Search, Edit, Trash2, Eye } from 'lucide-react';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface Props {
  account: string;
}

function BooksList({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  
  const [loading, setLoading] = useState(true);
  const [books, setBooks] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      // Load books with categories
      const { data: booksData, error: booksError } = await supabase
        .from('books')
        .select(`
          id,
          isbn,
          title,
          author,
          publisher,
          publication_year,
          description,
          language,
          pages,
          age_range,
          reading_level,
          location,
          total_copies,
          available_copies,
          condition,
          status,
          book_categories(name, color_code)
        `)
        .eq('account_id', accountId)
        .order('title');

      if (booksError) throw booksError;

      // Load categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('book_categories')
        .select('id, name')
        .eq('account_id', accountId)
        .order('name');

      if (categoriesError) throw categoriesError;

      setBooks(booksData || []);
      setCategories(categoriesData || []);
    } catch (error) {
      console.error('Error loading books:', error);
      toast.error(t('education:library.loadError', 'Failed to load books'));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBook = async (bookId: string) => {
    if (!confirm(t('education:library.confirmDelete', 'Are you sure you want to delete this book?'))) {
      return;
    }

    try {
      const { error } = await supabase
        .from('books')
        .delete()
        .eq('id', bookId)
        .eq('account_id', workspace.account.id);

      if (error) throw error;

      toast.success(t('education:library.bookDeleted', 'Book deleted successfully'));
      loadData(); // Reload the list
    } catch (error: any) {
      console.error('Error deleting book:', error);
      toast.error(error.message || t('education:library.deleteError', 'Failed to delete book'));
    }
  };

  const filteredBooks = books.filter((book) => {
    const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (book.isbn && book.isbn.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || 
                           (book.book_categories && book.book_categories.name === selectedCategory);
    
    const matchesStatus = selectedStatus === 'all' || book.status === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getStatusBadge = (status: string, availableCopies: number, totalCopies: number) => {
    if (status === 'available' && availableCopies > 0) {
      return <Badge className="bg-green-100 text-green-800">Available</Badge>;
    } else if (availableCopies === 0) {
      return <Badge className="bg-red-100 text-red-800">All Borrowed</Badge>;
    } else {
      return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {t('education:library.booksManagement', 'Books Management')}
          </h1>
          <p className="text-gray-600">
            {t('education:library.booksDescription', 'Manage your library book catalog')}
          </p>
        </div>
        <Button asChild>
          <Link href={`/home/<USER>/education/library/books/new`}>
            <Plus className="h-4 w-4 mr-2" />
            {t('education:library.addBook', 'Add Book')}
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={t('education:library.searchBooks', 'Search books...')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder={t('education:library.allCategories', 'All Categories')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('education:library.allCategories', 'All Categories')}</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder={t('education:library.allStatuses', 'All Statuses')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('education:library.allStatuses', 'All Statuses')}</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="checked_out">Checked Out</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="lost">Lost</SelectItem>
              </SelectContent>
            </Select>

            <div className="text-sm text-gray-600 flex items-center">
              {t('education:library.totalBooks', 'Total: {{count}} books', { count: filteredBooks.length })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Books Grid */}
      {filteredBooks.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <BookOpen className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('education:library.noBooksFound', 'No books found')}
            </h3>
            <p className="text-gray-500 mb-6">
              {searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all'
                ? t('education:library.noMatchingBooks', 'No books match your current filters.')
                : t('education:library.noBooksYet', 'No books have been added to the library yet.')
              }
            </p>
            {!searchTerm && selectedCategory === 'all' && selectedStatus === 'all' && (
              <Button asChild>
                <Link href={`/home/<USER>/education/library/books/new`}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('education:library.addFirstBook', 'Add your first book')}
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBooks.map((book) => (
            <Card key={book.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg line-clamp-2">{book.title}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">by {book.author}</p>
                    {book.book_categories && (
                      <Badge 
                        variant="outline" 
                        className="mt-2"
                        style={{ 
                          borderColor: book.book_categories.color_code,
                          color: book.book_categories.color_code 
                        }}
                      >
                        {book.book_categories.name}
                      </Badge>
                    )}
                  </div>
                  {getStatusBadge(book.status, book.available_copies, book.total_copies)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  {book.isbn && (
                    <p><span className="font-medium">ISBN:</span> {book.isbn}</p>
                  )}
                  {book.publisher && (
                    <p><span className="font-medium">Publisher:</span> {book.publisher}</p>
                  )}
                  {book.publication_year && (
                    <p><span className="font-medium">Year:</span> {book.publication_year}</p>
                  )}
                  <p><span className="font-medium">Language:</span> {book.language}</p>
                  {book.age_range && (
                    <p><span className="font-medium">Age Range:</span> {book.age_range}</p>
                  )}
                  <p><span className="font-medium">Copies:</span> {book.available_copies}/{book.total_copies}</p>
                  {book.location && (
                    <p><span className="font-medium">Location:</span> {book.location}</p>
                  )}
                </div>

                {book.description && (
                  <p className="text-sm text-gray-700 mt-3 line-clamp-2">
                    {book.description}
                  </p>
                )}

                <div className="flex justify-end space-x-2 mt-4">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/home/<USER>/education/library/books/${book.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/home/<USER>/education/library/books/${book.id}/edit`}>
                      <Edit className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDeleteBook(book.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export { BooksList };
