'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import { AlertTriangle, Heart, Pill, Plus, Shield, FileText, Activity } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface Props {
  account: string;
}

function HealthSafetyDashboard({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalMedicalRecords: 0,
    activeMedications: 0,
    incidentReports: 0,
    safetyProtocols: 0,
  });
  const [recentIncidents, setRecentIncidents] = useState<any[]>([]);
  const [medicationAlerts, setMedicationAlerts] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      // Load medical records count
      const { data: medicalRecordsData, error: medicalRecordsError } =
        await supabase
          .from('health_records')
          .select('id')
          .eq('account_id', accountId);

      if (medicalRecordsError) throw medicalRecordsError;

      // Mock data for features not yet implemented
      const medicationsData: any[] = [];
      const incidentsData: any[] = [];
      const protocolsData: any[] = [];
      const medicationScheduleData: any[] = [];

      // TODO: Implement these tables when needed:
      // - medication_schedule
      // - incident_reports
      // - safety_protocols

      // Update stats
      setStats({
        totalMedicalRecords: medicalRecordsData?.length || 0,
        activeMedications: 0, // Will be implemented later
        incidentReports: 0,   // Will be implemented later
        safetyProtocols: 0,   // Will be implemented later
      });

      // Set empty arrays for features not yet implemented
      setRecentIncidents([]);
      setMedicationAlerts([]);
    } catch (error) {
      console.error('Error loading health dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto max-w-7xl space-y-6 px-4 py-6">
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl space-y-8 px-4 py-6">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-red-600 via-pink-600 to-rose-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Heart className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Sức khỏe & An toàn</h1>
                  <p className="text-white/90 text-lg">Quản lý sức khỏe và an toàn học viên</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.totalMedicalRecords}</div>
                      <div className="text-white/80 text-sm">Hồ sơ y tế</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Shield className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.incidentReports}</div>
                      <div className="text-white/80 text-sm">Sự cố an toàn</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Activity className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.activeMedications}</div>
                      <div className="text-white/80 text-sm">Thuốc đang dùng</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.safetyProtocols}</div>
                      <div className="text-white/80 text-sm">Quy trình an toàn</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm transition-all duration-200">
                <Link href={`/home/<USER>/education/health/medical-records`}>
                  <Heart className="mr-2 h-4 w-4" />
                  Xem hồ sơ y tế
                </Link>
              </Button>
              <Button asChild className="bg-white text-red-600 hover:bg-white/90 transition-all duration-200">
                <Link
                  href={`/home/<USER>/education/health/medical-records/new`}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Thêm hồ sơ y tế
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" asChild>
          <Link href={`/home/<USER>/education/health/medical-records`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-muted-foreground text-sm font-medium">
                    Hồ sơ y tế
                  </p>
                  <p className="text-2xl font-bold">
                    {stats.totalMedicalRecords}
                  </p>
                  <p className="text-xs text-blue-600 mt-1">Xem tất cả →</p>
                </div>
                <Heart className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Link>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow opacity-60">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Thuốc đang dùng
                </p>
                <p className="text-2xl font-bold">{stats.activeMedications}</p>
                <p className="text-xs text-gray-400 mt-1">Sắp có</p>
              </div>
              <Pill className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow opacity-60">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Báo cáo sự cố
                </p>
                <p className="text-2xl font-bold">{stats.incidentReports}</p>
                <p className="text-xs text-gray-400 mt-1">Sắp có</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow opacity-60">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Quy trình an toàn
                </p>
                <p className="text-2xl font-bold">{stats.safetyProtocols}</p>
                <p className="text-xs text-gray-400 mt-1">Sắp có</p>
              </div>
              <Shield className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Tổng quan</TabsTrigger>
          <TabsTrigger value="medical">Hồ sơ y tế</TabsTrigger>
          <TabsTrigger value="medications">Thuốc men</TabsTrigger>
          <TabsTrigger value="incidents">Sự cố</TabsTrigger>
          <TabsTrigger value="safety">An toàn</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Recent Incidents */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5" />
                  <span>Sự cố gần đây</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentIncidents.length > 0 ? (
                  <div className="space-y-4">
                    {recentIncidents.map((incident) => (
                      <div
                        key={incident.id}
                        className="flex items-start space-x-3 rounded-lg border p-3"
                      >
                        <div
                          className={`mt-2 h-3 w-3 rounded-full ${
                            incident.severity === 'low'
                              ? 'bg-green-500'
                              : incident.severity === 'medium'
                                ? 'bg-yellow-500'
                                : 'bg-red-500'
                          }`}
                        ></div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{incident.student}</h4>
                            <Badge variant="outline">{incident.type}</Badge>
                          </div>
                          <p className="text-muted-foreground mt-1 text-sm">
                            {incident.description}
                          </p>
                          <div className="mt-2 flex items-center space-x-2">
                            <span className="text-muted-foreground text-xs">
                              {incident.date}
                            </span>
                            <Badge
                              className={
                                incident.status === 'resolved'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }
                            >
                              {incident.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <AlertTriangle className="mx-auto mb-2 h-8 w-8 text-gray-300" />
                    <p className="text-gray-500 text-sm">Chưa có báo cáo sự cố nào</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Medication Alerts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Pill className="h-5 w-5" />
                  <span>Lịch uống thuốc</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {medicationAlerts.length > 0 ? (
                  <div className="space-y-4">
                    {medicationAlerts.map((alert) => (
                      <div
                        key={alert.id}
                        className="flex items-center justify-between rounded-lg border p-3"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                            <Pill className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-medium">{alert.student}</h4>
                            <p className="text-muted-foreground text-sm">
                              {alert.medication}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{alert.time}</div>
                          <Badge
                            className={
                              alert.status === 'due'
                                ? 'bg-orange-100 text-orange-800'
                                : 'bg-green-100 text-green-800'
                            }
                          >
                            {alert.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <Pill className="mx-auto mb-2 h-8 w-8 text-gray-300" />
                    <p className="text-gray-500 text-sm">Chưa có lịch uống thuốc nào</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="medical">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <Heart className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Hồ sơ y tế
                </h3>
                <p className="mb-6 text-gray-500">
                  Quản lý hồ sơ sức khỏe toàn diện cho tất cả học viên.
                </p>
                <div className="flex justify-center space-x-4">
                  <Button variant="outline" asChild>
                    <Link href={`/home/<USER>/education/health/medical-records`}>
                      <Heart className="mr-2 h-4 w-4" />
                      Xem danh sách
                    </Link>
                  </Button>
                  <Button asChild>
                    <Link
                      href={`/home/<USER>/education/health/medical-records/new`}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Thêm hồ sơ mới
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medications">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <Pill className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Quản lý thuốc men
                </h3>
                <p className="mb-6 text-gray-500">
                  Theo dõi lịch uống thuốc và nhật ký dùng thuốc.
                </p>
                <Button disabled>
                  <Plus className="mr-2 h-4 w-4" />
                  Sắp có
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="incidents">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <AlertTriangle className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Báo cáo sự cố
                </h3>
                <p className="mb-6 text-gray-500">
                  Ghi nhận và theo dõi các sự cố an toàn và tai nạn.
                </p>
                <Button disabled>
                  <Plus className="mr-2 h-4 w-4" />
                  Sắp có
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="safety">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <Shield className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Quy trình an toàn
                </h3>
                <p className="mb-6 text-gray-500">
                  Quản lý quy trình khẩn cấp và các biện pháp an toàn.
                </p>
                <Button disabled>
                  <Plus className="mr-2 h-4 w-4" />
                  Sắp có
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default HealthSafetyDashboard;
