import { redirect } from 'next/navigation';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import HealthSafetyDashboard from './_components/health-safety-dashboard';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

export const metadata = {
  title: 'Health & Safety',
};

async function HealthSafetyPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-36">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:health.title', 'Health & Safety')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:health.title', 'Health & Safety')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-7xl">
        <HealthSafetyDashboard account={resolvedParams.account} />
      </div>
    </div>
  );
}

export default HealthSafetyPage;
