'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';

import {
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  Gamepad2,
  Plus,
  Target,
  Trash2,
  Users,
  XCircle,
} from 'lucide-react';

interface Props {
  account: string;
}

interface Assignment {
  id: string;
  title: string;
  instructions: string;
  assigned_to_type: string;
  assigned_to_id: string;
  due_date: string;
  max_attempts: number;
  min_score: number;
  is_required: boolean;
  is_active: boolean;
  created_at: string;
  game: {
    id: string;
    title: string;
    game_type: string;
    difficulty_level: number;
  };
  completion_stats?: {
    total_assigned: number;
    completed: number;
    in_progress: number;
    not_started: number;
  };
}

function GameAssignments({ account }: Props) {
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(true);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    loadAssignments();
  }, [activeTab]);

  const loadAssignments = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      const { data: assignmentsData, error } = await supabase
        .from('game_assignments')
        .select(`
          id,
          title,
          instructions,
          assigned_to_type,
          assigned_to_id,
          due_date,
          max_attempts,
          min_score,
          is_required,
          is_active,
          created_at,
          games(id, title, game_type, difficulty_level)
        `)
        .eq('account_id', accountId)
        .eq('is_active', activeTab === 'active')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Format assignments with completion stats (simplified)
      const formattedAssignments = assignmentsData?.map(assignment => ({
        ...assignment,
        game: assignment.games,
        completion_stats: {
          total_assigned: 1, // Would calculate from actual assignments
          completed: 0,
          in_progress: 0,
          not_started: 1,
        },
      })) || [];

      setAssignments(formattedAssignments);
    } catch (error) {
      console.error('Error loading assignments:', error);
      toast.error('Không thể tải danh sách bài tập');
    } finally {
      setLoading(false);
    }
  };

  const toggleAssignmentStatus = async (assignmentId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('game_assignments')
        .update({ is_active: !currentStatus })
        .eq('id', assignmentId);

      if (error) throw error;

      toast.success(currentStatus ? 'Đã tạm dừng bài tập' : 'Đã kích hoạt bài tập');
      loadAssignments();
    } catch (error) {
      console.error('Error updating assignment:', error);
      toast.error('Không thể cập nhật bài tập');
    }
  };

  const deleteAssignment = async (assignmentId: string) => {
    if (!confirm('Bạn có chắc chắn muốn xóa bài tập này?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('game_assignments')
        .delete()
        .eq('id', assignmentId);

      if (error) throw error;

      toast.success('Đã xóa bài tập');
      loadAssignments();
    } catch (error) {
      console.error('Error deleting assignment:', error);
      toast.error('Không thể xóa bài tập');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-green-100 text-green-800';
      case 2: return 'bg-blue-100 text-blue-800';
      case 3: return 'bg-yellow-100 text-yellow-800';
      case 4: return 'bg-orange-100 text-orange-800';
      case 5: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyText = (level: number) => {
    switch (level) {
      case 1: return 'Dễ';
      case 2: return 'Trung bình';
      case 3: return 'Khó';
      case 4: return 'Rất khó';
      case 5: return 'Chuyên gia';
      default: return 'Không xác định';
    }
  };

  const getCompletionColor = (stats: any) => {
    const percentage = (stats.completed / stats.total_assigned) * 100;
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải bài tập...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl space-y-8 px-4 py-6">
      {/* Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Calendar className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Game Assignments</h1>
                  <p className="text-white/90 text-lg">Quản lý bài tập games cho học sinh</p>
                </div>
              </div>
              
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Target className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{assignments.filter(a => a.is_active).length}</div>
                      <div className="text-white/80 text-sm">Đang hoạt động</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">
                        {assignments.filter(a => a.due_date && isOverdue(a.due_date)).length}
                      </div>
                      <div className="text-white/80 text-sm">Quá hạn</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">
                        {assignments.reduce((acc, a) => acc + (a.completion_stats?.completed || 0), 0)}
                      </div>
                      <div className="text-white/80 text-sm">Hoàn thành</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" asChild className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm">
                <Link href={`/home/<USER>/education/games/manage`}>
                  <Gamepad2 className="mr-2 h-4 w-4" />
                  Quản lý games
                </Link>
              </Button>
              <Button asChild className="bg-white text-blue-600 hover:bg-white/90">
                <Link href={`/home/<USER>/education/games/assignments/new`}>
                  <Plus className="mr-2 h-4 w-4" />
                  Tạo bài tập
                </Link>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="active">Đang hoạt động</TabsTrigger>
          <TabsTrigger value="inactive">Đã tạm dừng</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-6">
          {assignments.length === 0 ? (
            <Card className="border-0 shadow-lg">
              <CardContent className="p-12 text-center">
                <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {activeTab === 'active' ? 'Chưa có bài tập nào đang hoạt động' : 'Chưa có bài tập nào bị tạm dừng'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {activeTab === 'active' 
                    ? 'Tạo bài tập mới để giao games cho học sinh.'
                    : 'Các bài tập bị tạm dừng sẽ hiển thị ở đây.'
                  }
                </p>
                {activeTab === 'active' && (
                  <Button asChild>
                    <Link href={`/home/<USER>/education/games/assignments/new`}>
                      <Plus className="h-4 w-4 mr-2" />
                      Tạo bài tập đầu tiên
                    </Link>
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {assignments.map((assignment) => (
                <Card key={assignment.id} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="line-clamp-2 mb-2">
                          {assignment.title || assignment.game?.title}
                        </CardTitle>
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline" className="text-xs">
                            {assignment.game?.title}
                          </Badge>
                          <Badge className={`text-xs ${getDifficultyColor(assignment.game?.difficulty_level || 1)}`}>
                            {getDifficultyText(assignment.game?.difficulty_level || 1)}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            <span className="capitalize">{assignment.assigned_to_type}</span>
                          </div>
                          {assignment.due_date && (
                            <div className={`flex items-center gap-1 ${isOverdue(assignment.due_date) ? 'text-red-600' : ''}`}>
                              <Clock className="h-4 w-4" />
                              <span>{formatDate(assignment.due_date)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {assignment.is_required && (
                          <Badge variant="destructive" className="text-xs">
                            Bắt buộc
                          </Badge>
                        )}
                        {isOverdue(assignment.due_date) && (
                          <Badge variant="destructive" className="text-xs">
                            Quá hạn
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {assignment.instructions && (
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {assignment.instructions}
                      </p>
                    )}
                    
                    {/* Completion Stats */}
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Tiến độ hoàn thành</span>
                        <span className={`font-semibold ${getCompletionColor(assignment.completion_stats!)}`}>
                          {assignment.completion_stats?.completed}/{assignment.completion_stats?.total_assigned}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ 
                            width: `${((assignment.completion_stats?.completed || 0) / (assignment.completion_stats?.total_assigned || 1)) * 100}%` 
                          }}
                        ></div>
                      </div>
                    </div>

                    {/* Assignment Settings */}
                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                      <div>
                        <span className="font-medium">Số lần thử:</span> {assignment.max_attempts}
                      </div>
                      <div>
                        <span className="font-medium">Điểm tối thiểu:</span> {assignment.min_score}%
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/home/<USER>/education/games/assignments/${assignment.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          Xem
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/home/<USER>/education/games/assignments/${assignment.id}/edit`}>
                          <Edit className="h-4 w-4 mr-1" />
                          Sửa
                        </Link>
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => toggleAssignmentStatus(assignment.id, assignment.is_active)}
                      >
                        {assignment.is_active ? (
                          <>
                            <XCircle className="h-4 w-4 mr-1" />
                            Tạm dừng
                          </>
                        ) : (
                          <>
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Kích hoạt
                          </>
                        )}
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => deleteAssignment(assignment.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export { GameAssignments };
