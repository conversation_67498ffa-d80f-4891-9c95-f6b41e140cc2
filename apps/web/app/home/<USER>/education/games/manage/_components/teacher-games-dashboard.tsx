'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

import {
  BarChart3,
  BookOpen,
  Calendar,
  Clock,
  FileText,
  Gamepad2,
  Plus,
  Settings,
  Target,
  Trophy,
  Users,
  Zap,
} from 'lucide-react';

interface Props {
  account: string;
}

interface GameStats {
  totalGames: number;
  totalSessions: number;
  totalStudents: number;
  avgScore: number;
  totalAssignments: number;
  activeAssignments: number;
}

interface RecentSession {
  id: string;
  student_name: string;
  game_title: string;
  score: number;
  max_score: number;
  completed_at: string;
  duration_seconds: number;
}

interface Assignment {
  id: string;
  title: string;
  game_title: string;
  assigned_to_type: string;
  due_date: string;
  is_required: boolean;
  completion_count: number;
  total_assigned: number;
}

function TeacherGamesDashboard({ account }: Props) {
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<GameStats>({
    totalGames: 0,
    totalSessions: 0,
    totalStudents: 0,
    avgScore: 0,
    totalAssignments: 0,
    activeAssignments: 0,
  });
  const [recentSessions, setRecentSessions] = useState<RecentSession[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      // Load games count
      const { count: gamesCount } = await supabase
        .from('games')
        .select('*', { count: 'exact', head: true })
        .eq('account_id', accountId)
        .eq('is_active', true);

      // Load sessions count and avg score
      const { data: sessionsData, count: sessionsCount } = await supabase
        .from('game_sessions')
        .select('score, max_score', { count: 'exact' })
        .eq('account_id', accountId)
        .eq('is_completed', true);

      // Load unique students count
      const { data: studentsData } = await supabase
        .from('game_sessions')
        .select('student_id')
        .eq('account_id', accountId);

      const uniqueStudents = new Set(studentsData?.map(s => s.student_id) || []).size;

      // Calculate average score
      const avgScore = sessionsData?.length > 0 
        ? sessionsData.reduce((acc, session) => acc + (session.score / session.max_score * 100), 0) / sessionsData.length
        : 0;

      // Load assignments count
      const { count: assignmentsCount } = await supabase
        .from('game_assignments')
        .select('*', { count: 'exact', head: true })
        .eq('account_id', accountId);

      const { count: activeAssignmentsCount } = await supabase
        .from('game_assignments')
        .select('*', { count: 'exact', head: true })
        .eq('account_id', accountId)
        .eq('is_active', true);

      setStats({
        totalGames: gamesCount || 0,
        totalSessions: sessionsCount || 0,
        totalStudents: uniqueStudents,
        avgScore: Math.round(avgScore),
        totalAssignments: assignmentsCount || 0,
        activeAssignments: activeAssignmentsCount || 0,
      });

      // Load recent sessions with student and game info
      const { data: recentSessionsData } = await supabase
        .from('game_sessions')
        .select(`
          id,
          score,
          max_score,
          completed_at,
          duration_seconds,
          games(title),
          student_id
        `)
        .eq('account_id', accountId)
        .eq('is_completed', true)
        .order('completed_at', { ascending: false })
        .limit(10);

      // Format recent sessions (simplified - in real app would join with users table)
      const formattedSessions = recentSessionsData?.map(session => ({
        id: session.id,
        student_name: `Student ${session.student_id.slice(0, 8)}`, // Simplified
        game_title: session.games?.title || 'Unknown Game',
        score: session.score,
        max_score: session.max_score,
        completed_at: session.completed_at,
        duration_seconds: session.duration_seconds,
      })) || [];

      setRecentSessions(formattedSessions);

      // Load assignments with game info
      const { data: assignmentsData } = await supabase
        .from('game_assignments')
        .select(`
          id,
          title,
          assigned_to_type,
          due_date,
          is_required,
          games(title)
        `)
        .eq('account_id', accountId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(10);

      const formattedAssignments = assignmentsData?.map(assignment => ({
        id: assignment.id,
        title: assignment.title || assignment.games?.title || 'Untitled Assignment',
        game_title: assignment.games?.title || 'Unknown Game',
        assigned_to_type: assignment.assigned_to_type,
        due_date: assignment.due_date,
        is_required: assignment.is_required,
        completion_count: 0, // Would calculate from sessions
        total_assigned: 1, // Would calculate from assigned students
      })) || [];

      setAssignments(formattedAssignments);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl space-y-8 px-4 py-6">
      {/* Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Settings className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Teacher Games Dashboard</h1>
                  <p className="text-white/90 text-lg">Quản lý games và theo dõi tiến độ học sinh</p>
                </div>
              </div>
              
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Gamepad2 className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.totalGames}</div>
                      <div className="text-white/80 text-sm">Tổng games</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Users className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.totalStudents}</div>
                      <div className="text-white/80 text-sm">Học sinh</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Target className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.avgScore}%</div>
                      <div className="text-white/80 text-sm">Điểm TB</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.activeAssignments}</div>
                      <div className="text-white/80 text-sm">Bài tập</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" asChild className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm">
                <Link href={`/home/<USER>/education/games/assignments`}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Bài tập
                </Link>
              </Button>
              <Button asChild className="bg-white text-indigo-600 hover:bg-white/90">
                <Link href={`/home/<USER>/education/games/create`}>
                  <Plus className="mr-2 h-4 w-4" />
                  Tạo game
                </Link>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Tổng quan</TabsTrigger>
          <TabsTrigger value="sessions">Phiên chơi</TabsTrigger>
          <TabsTrigger value="assignments">Bài tập</TabsTrigger>
          <TabsTrigger value="analytics">Phân tích</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Sessions */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Zap className="h-5 w-5 text-blue-600" />
                  </div>
                  Phiên chơi gần đây
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentSessions.length === 0 ? (
                  <div className="text-center py-8">
                    <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Chưa có phiên chơi</h3>
                    <p className="text-gray-600">Học sinh chưa chơi game nào.</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentSessions.slice(0, 5).map((session) => (
                      <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{session.student_name}</p>
                          <p className="text-sm text-gray-600">{session.game_title}</p>
                          <p className="text-xs text-gray-500">{formatDate(session.completed_at)}</p>
                        </div>
                        <div className="text-right">
                          <p className={`font-bold ${getScoreColor(session.score, session.max_score)}`}>
                            {session.score}/{session.max_score}
                          </p>
                          <p className="text-xs text-gray-500">{formatDuration(session.duration_seconds)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Active Assignments */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Calendar className="h-5 w-5 text-green-600" />
                  </div>
                  Bài tập đang hoạt động
                </CardTitle>
              </CardHeader>
              <CardContent>
                {assignments.length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Chưa có bài tập</h3>
                    <p className="text-gray-600 mb-4">Tạo bài tập để giao cho học sinh.</p>
                    <Button asChild>
                      <Link href={`/home/<USER>/education/games/assignments/new`}>
                        <Plus className="h-4 w-4 mr-2" />
                        Tạo bài tập
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {assignments.slice(0, 5).map((assignment) => (
                      <div key={assignment.id} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">{assignment.title}</p>
                            <p className="text-sm text-gray-600">{assignment.game_title}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs capitalize">
                                {assignment.assigned_to_type}
                              </Badge>
                              {assignment.is_required && (
                                <Badge variant="destructive" className="text-xs">
                                  Bắt buộc
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="text-right text-sm">
                            <p className="text-gray-600">Hạn: {formatDate(assignment.due_date)}</p>
                            <p className="text-gray-500">{assignment.completion_count}/{assignment.total_assigned}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-purple-600" />
                </div>
                Tất cả phiên chơi
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recentSessions.length === 0 ? (
                <div className="text-center py-12">
                  <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Chưa có dữ liệu</h3>
                  <p className="text-gray-600">Học sinh chưa chơi game nào.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {recentSessions.map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{session.student_name}</p>
                        <p className="text-sm text-gray-600">{session.game_title}</p>
                        <p className="text-xs text-gray-500">
                          {formatDate(session.completed_at)} • {formatDuration(session.duration_seconds)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={`text-lg font-bold ${getScoreColor(session.score, session.max_score)}`}>
                          {Math.round((session.score / session.max_score) * 100)}%
                        </p>
                        <p className="text-sm text-gray-500">
                          {session.score}/{session.max_score}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignments" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold">Quản lý bài tập</h3>
            <Button asChild>
              <Link href={`/home/<USER>/education/games/assignments/new`}>
                <Plus className="h-4 w-4 mr-2" />
                Tạo bài tập mới
              </Link>
            </Button>
          </div>

          <Card className="border-0 shadow-lg">
            <CardContent className="p-6">
              {assignments.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Chưa có bài tập nào</h3>
                  <p className="text-gray-600 mb-6">Tạo bài tập để giao games cho học sinh.</p>
                  <Button asChild>
                    <Link href={`/home/<USER>/education/games/assignments/new`}>
                      <Plus className="h-4 w-4 mr-2" />
                      Tạo bài tập đầu tiên
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {assignments.map((assignment) => (
                    <div key={assignment.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{assignment.title}</h4>
                          <p className="text-sm text-gray-600">{assignment.game_title}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs capitalize">
                            {assignment.assigned_to_type}
                          </Badge>
                          {assignment.is_required && (
                            <Badge variant="destructive" className="text-xs">
                              Bắt buộc
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          <p>Hạn: {formatDate(assignment.due_date)}</p>
                          <p>Hoàn thành: {assignment.completion_count}/{assignment.total_assigned}</p>
                        </div>
                        <Button size="sm" variant="outline" className="w-full">
                          Xem chi tiết
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Trophy className="h-5 w-5 text-orange-600" />
                </div>
                Phân tích học tập
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Trophy className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Đang phát triển</h3>
                <p className="text-gray-600 mb-6">Tính năng phân tích chi tiết sẽ có trong phiên bản tiếp theo.</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-semibold text-blue-900">Tiến độ học tập</h4>
                    <p className="text-sm text-blue-700">Theo dõi tiến độ từng học sinh</p>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-semibold text-green-900">Báo cáo chi tiết</h4>
                    <p className="text-sm text-green-700">Xuất báo cáo PDF/Excel</p>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-semibold text-purple-900">Đề xuất cá nhân</h4>
                    <p className="text-sm text-purple-700">AI đề xuất games phù hợp</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export { TeacherGamesDashboard };
