'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

import {
  ArrowLeft,
  BookOpen,
  Brain,
  Calculator,
  Clock,
  FlaskConical,
  Gamepad2,
  Globe,
  Play,
  Star,
  Users,
} from 'lucide-react';

interface Props {
  account: string;
  categoryId: string;
}

interface GameCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  age_group: string;
}

interface Game {
  id: string;
  title: string;
  description: string;
  game_type: string;
  difficulty_level: number;
  estimated_duration: number;
  min_age: number;
  max_age: number;
  subject: string;
}

function CategoryGames({ account, categoryId }: Props) {
  const router = useRouter();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(true);
  const [category, setCategory] = useState<GameCategory | null>(null);
  const [games, setGames] = useState<Game[]>([]);

  useEffect(() => {
    loadCategoryGames();
  }, [categoryId]);

  const loadCategoryGames = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      // Load category
      const { data: categoryData, error: categoryError } = await supabase
        .from('game_categories')
        .select('*')
        .eq('id', categoryId)
        .eq('account_id', accountId)
        .single();

      if (categoryError) throw categoryError;

      // Load games in category
      const { data: gamesData, error: gamesError } = await supabase
        .from('games')
        .select('*')
        .eq('category_id', categoryId)
        .eq('account_id', accountId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (gamesError) throw gamesError;

      setCategory(categoryData);
      setGames(gamesData || []);
    } catch (error) {
      console.error('Error loading category games:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, any> = {
      calculator: Calculator,
      'book-open': BookOpen,
      globe: Globe,
      'flask-conical': FlaskConical,
      brain: Brain,
      gamepad2: Gamepad2,
    };
    const IconComponent = iconMap[iconName] || Gamepad2;
    return <IconComponent className="h-8 w-8" />;
  };

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-green-100 text-green-800';
      case 2: return 'bg-blue-100 text-blue-800';
      case 3: return 'bg-yellow-100 text-yellow-800';
      case 4: return 'bg-orange-100 text-orange-800';
      case 5: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyText = (level: number) => {
    switch (level) {
      case 1: return 'Dễ';
      case 2: return 'Trung bình';
      case 3: return 'Khó';
      case 4: return 'Rất khó';
      case 5: return 'Chuyên gia';
      default: return 'Không xác định';
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes} phút`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải games...</p>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Danh mục không tồn tại</p>
        <Button onClick={() => router.push(`/home/<USER>/education/games`)} className="mt-4">
          Quay lại Games
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl space-y-8 px-4 py-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.push(`/home/<USER>/education/games`)}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Quay lại Games
        </Button>
      </div>

      {/* Category Header */}
      <div className={`relative overflow-hidden rounded-2xl bg-gradient-to-br from-${category.color}-500 via-${category.color}-600 to-${category.color}-700 p-8 text-white`}>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
              {getIconComponent(category.icon)}
            </div>
            <div>
              <h1 className="text-3xl font-bold">{category.name}</h1>
              <p className="text-white/90 text-lg">{category.description}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="bg-white/20 text-white border-white/30">
              {games.length} games
            </Badge>
            <Badge variant="outline" className="bg-white/20 text-white border-white/30 capitalize">
              {category.age_group}
            </Badge>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Games Grid */}
      {games.length === 0 ? (
        <Card className="border-0 shadow-lg">
          <CardContent className="p-12 text-center">
            <Gamepad2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Chưa có game nào</h3>
            <p className="text-gray-600 mb-6">Danh mục này chưa có games. Hãy tạo games mới cho danh mục này.</p>
            <Button asChild>
              <Link href={`/home/<USER>/education/games/create?category=${categoryId}`}>
                Tạo game đầu tiên
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {games.map((game) => (
            <Link
              key={game.id}
              href={`/home/<USER>/education/games/play/${game.id}`}
              className="group block"
            >
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="group-hover:text-purple-600 transition-colors line-clamp-2">
                        {game.title}
                      </CardTitle>
                      <p className="text-sm text-gray-600 mt-1 capitalize">
                        {game.subject}
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg bg-${category.color}-100`}>
                      {getIconComponent(category.icon)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {game.description}
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(game.difficulty_level)}`}>
                        {getDifficultyText(game.difficulty_level)}
                      </span>
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Clock className="h-4 w-4" />
                        {formatDuration(game.estimated_duration)}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        Tuổi: {game.min_age}-{game.max_age}
                      </span>
                      <Button size="sm" className="group-hover:bg-purple-600">
                        <Play className="h-4 w-4 mr-1" />
                        Chơi ngay
                      </Button>
                    </div>
                    <div className="pt-2 border-t">
                      <Badge variant="outline" className="text-xs capitalize">
                        {game.game_type}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}

      {/* Quick Stats */}
      {games.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">{games.length}</div>
              <div className="text-gray-600">Tổng games</div>
            </CardContent>
          </Card>
          <Card className="border-0 shadow-lg">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {Math.round(games.reduce((acc, game) => acc + game.estimated_duration, 0) / 60)}
              </div>
              <div className="text-gray-600">Phút chơi</div>
            </CardContent>
          </Card>
          <Card className="border-0 shadow-lg">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {Math.round(games.reduce((acc, game) => acc + game.difficulty_level, 0) / games.length * 10) / 10}
              </div>
              <div className="text-gray-600">Độ khó TB</div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

export { CategoryGames };
