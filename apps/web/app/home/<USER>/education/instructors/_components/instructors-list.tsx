'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { 
  Plus,
  Search,
  Eye,
  Edit,
  Mail,
  Phone,
  Calendar,
  Users,
  GraduationCap,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface Props {
  account: string;
}

interface Instructor {
  id: string;
  employee_code: string;
  full_name: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  hire_date: string;
  status: string;
  qualifications: string[];
  specializations: string[];
  profile_picture_url?: string;
  created_at: string;
  _count?: {
    classes: number;
    learners: number;
  };
}

export function InstructorsList({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;
    loadInstructors();
  }, [currentAccount?.id, supabase]);

  const loadInstructors = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setInstructors([]);
        return;
      }

      // Get instructors
      const { data: instructorsData, error: instructorsError } = await supabase
        .from('instructors')
        .select('*')
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (instructorsError) throw instructorsError;

      // For now, add mock count data since we don't have class assignments yet
      const processedInstructors = instructorsData?.map(instructor => ({
        ...instructor,
        _count: {
          classes: Math.floor(Math.random() * 5) + 1,
          learners: Math.floor(Math.random() * 30) + 5,
        }
      })) || [];

      setInstructors(processedInstructors);
    } catch (err: any) {
      console.error('Error loading instructors:', err);
      setError(err.message || 'Failed to load instructors');
    } finally {
      setLoading(false);
    }
  };

  const filteredInstructors = instructors.filter(instructor => {
    const matchesSearch = instructor.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         instructor.employee_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         instructor.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || instructor.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { label: t('instructors.status.active', 'Active'), variant: 'default' as const },
      inactive: { label: t('instructors.status.inactive', 'Inactive'), variant: 'secondary' as const },
      on_leave: { label: t('instructors.status.onLeave', 'On Leave'), variant: 'outline' as const },
      terminated: { label: t('instructors.status.terminated', 'Terminated'), variant: 'destructive' as const },
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };
    
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    );
  };

  const getPositionText = (position: string) => {
    return t(`instructors.position.${position}`, position);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  if (loading) {
    return <div className="flex justify-center py-8">{t('common.loading')}</div>;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={loadInstructors} variant="outline">
          {t('common.refresh')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={t('instructors.searchPlaceholder', 'Search instructors...')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">{t('instructors.filters.allStatuses', 'All Statuses')}</option>
            <option value="active">{t('instructors.filters.active', 'Active')}</option>
            <option value="inactive">{t('instructors.filters.inactive', 'Inactive')}</option>
            <option value="on_leave">{t('instructors.filters.onLeave', 'On Leave')}</option>
            <option value="terminated">{t('instructors.filters.terminated', 'Terminated')}</option>
          </select>
        </div>

        <Button asChild>
          <Link href={`/home/<USER>/education/instructors/new`}>
            <Plus className="h-4 w-4 mr-2" />
            {t('instructors.addInstructor', 'Add Instructor')}
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('instructors.stats.total', 'Total Instructors')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{instructors.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('instructors.stats.active', 'Active')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {instructors.filter(i => i.status === 'active').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('instructors.stats.totalClasses', 'Total Classes')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {instructors.reduce((sum, i) => sum + (i._count?.classes || 0), 0)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('instructors.stats.totalLearners', 'Total Learners')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {instructors.reduce((sum, i) => sum + (i._count?.learners || 0), 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Instructors Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('instructors.title')} ({filteredInstructors.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('instructors.table.instructor', 'Instructor')}</TableHead>
                <TableHead>{t('instructors.table.code', 'Code')}</TableHead>
                <TableHead>{t('instructors.table.position', 'Position')}</TableHead>
                <TableHead>{t('instructors.table.contact', 'Contact')}</TableHead>
                <TableHead>{t('instructors.table.classes', 'Classes')}</TableHead>
                <TableHead>{t('instructors.table.status', 'Status')}</TableHead>
                <TableHead>{t('instructors.table.hireDate', 'Hire Date')}</TableHead>
                <TableHead>{t('instructors.table.actions', 'Actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInstructors.map((instructor) => (
                <TableRow key={instructor.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={instructor.profile_picture_url} />
                        <AvatarFallback>
                          {instructor.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{instructor.full_name}</div>
                        <div className="text-sm text-gray-500">{instructor.department}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-sm">
                    {instructor.employee_code}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <GraduationCap className="h-4 w-4 text-gray-400" />
                      <span>{getPositionText(instructor.position)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1 text-sm">
                        <Mail className="h-3 w-3 text-gray-400" />
                        <span>{instructor.email}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-sm">
                        <Phone className="h-3 w-3 text-gray-400" />
                        <span>{instructor.phone}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-center">
                      <div className="text-sm font-medium">{instructor._count?.classes || 0}</div>
                      <div className="text-xs text-gray-500">
                        {instructor._count?.learners || 0} {t('instructors.learners', 'learners')}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(instructor.status)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1 text-sm">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span>{formatDate(instructor.hire_date)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/instructors/${instructor.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/instructors/${instructor.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredInstructors.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || statusFilter !== 'all' 
                ? t('instructors.emptyState.noResults', 'No instructors found matching the filters.')
                : t('instructors.emptyState.noInstructors', 'No instructors yet. Add your first instructor!')
              }
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
