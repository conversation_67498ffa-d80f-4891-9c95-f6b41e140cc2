'use client';

import { useEffect, useState } from 'react';
import { Calendar, MapPin, Clock, Users, CheckCircle, XCircle, Filter } from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { But<PERSON> } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface Props {
  account: string;
}

interface EventRecord {
  id: string;
  title: string;
  description: string;
  start_datetime: string;
  end_datetime: string;
  location: string;
  event_type: string;
  max_participants?: number;
  current_participants: number;
  registration_deadline?: string;
  is_registered: boolean;
  registration_status: string;
  created_at: string;
}

export function GuardianEvents({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const [events, setEvents] = useState<EventRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  useEffect(() => {
    const loadEventsData = async () => {
      if (!workspace?.account?.id) return;

      try {
        setLoading(true);
        setError(null);

        // Use account directly
        const accountId = workspace.account.id;

        // Get events for this account
        const { data: eventsData, error: eventsError } = await supabase
          .from('events')
          .select('*')
          .eq('account_id', accountId)
          .order('start_datetime', { ascending: true });

        if (eventsError) {
          console.error('Error loading events:', eventsError);
          setError('Failed to load events data');
          return;
        }

        // Transform events data
        const transformedEvents: EventRecord[] = (eventsData || []).map((event) => ({
          id: event.id,
          title: event.title,
          description: event.description || '',
          start_datetime: event.start_datetime,
          end_datetime: event.end_datetime,
          location: event.location || '',
          event_type: event.event_type || 'general',
          max_participants: event.max_participants,
          current_participants: 0, // TODO: Get from registrations table
          registration_deadline: event.registration_deadline,
          is_registered: false, // TODO: Check user registration
          registration_status: 'open', // TODO: Calculate based on deadline and capacity
          created_at: event.created_at,
        }));

        setEvents(transformedEvents);
      } catch (err) {
        console.error('Error loading events data:', err);
        setError('Failed to load events data');
      } finally {
        setLoading(false);
      }
    };

    loadEventsData();
  }, [workspace?.account?.id, supabase]);

  if (loading) {
    return <div className="flex justify-center py-8">{t('common.loading')}</div>;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="mb-4 text-red-600">{error}</p>
        <Button onClick={() => window.location.reload()} variant="outline">
          {t('common.refresh')}
        </Button>
      </div>
    );
  }

  const filteredEvents = events.filter(event => {
    const typeMatch = selectedType === 'all' || event.event_type === selectedType;
    const statusMatch = selectedStatus === 'all' || 
      (selectedStatus === 'upcoming' && new Date(event.start_datetime) > new Date()) ||
      (selectedStatus === 'past' && new Date(event.start_datetime) < new Date()) ||
      (selectedStatus === 'registered' && event.is_registered);
    return typeMatch && statusMatch;
  });

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' });
  };

  const getEventTypeText = (eventType: string) => {
    const typeMap: Record<string, string> = {
      meeting: 'Họp phụ huynh',
      performance: 'Biểu diễn',
      field_trip: 'Dã ngoại',
      workshop: 'Hội thảo',
      sports: 'Thể thao',
      festival: 'Lễ hội',
      general: 'Chung',
    };
    return typeMap[eventType] || eventType;
  };

  const getEventTypeBadge = (eventType: string) => {
    const typeMap = {
      meeting: { variant: 'default' as const, color: 'bg-blue-500' },
      performance: { variant: 'secondary' as const, color: 'bg-purple-500' },
      field_trip: { variant: 'outline' as const, color: 'bg-green-500' },
      workshop: { variant: 'default' as const, color: 'bg-orange-500' },
      sports: { variant: 'secondary' as const, color: 'bg-red-500' },
      festival: { variant: 'outline' as const, color: 'bg-pink-500' },
      general: { variant: 'secondary' as const, color: 'bg-gray-500' },
    };
    
    const typeInfo = typeMap[eventType as keyof typeof typeMap] || typeMap.general;
    
    return (
      <Badge variant={typeInfo.variant}>
        {getEventTypeText(eventType)}
      </Badge>
    );
  };

  const getRegistrationStatus = (event: EventRecord) => {
    const now = new Date();
    const eventDate = new Date(event.start_datetime);
    const registrationDeadline = event.registration_deadline ? new Date(event.registration_deadline) : null;

    if (eventDate < now) {
      return { text: 'Đã kết thúc', variant: 'secondary' as const, disabled: true };
    }

    if (registrationDeadline && registrationDeadline < now) {
      return { text: 'Hết hạn đăng ký', variant: 'destructive' as const, disabled: true };
    }

    if (event.max_participants && event.current_participants >= event.max_participants) {
      return { text: 'Đã đầy', variant: 'destructive' as const, disabled: true };
    }

    if (event.is_registered) {
      return { text: 'Đã đăng ký', variant: 'default' as const, disabled: false };
    }

    return { text: 'Đăng ký', variant: 'outline' as const, disabled: false };
  };

  const upcomingEvents = events.filter(event => new Date(event.start_datetime) > new Date());
  const registeredEvents = events.filter(event => event.is_registered);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Sự kiện sắp tới</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{upcomingEvents.length}</div>
            <p className="text-xs text-gray-500">Sự kiện trong tương lai</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Đã đăng ký</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{registeredEvents.length}</div>
            <p className="text-xs text-gray-500">Sự kiện đã đăng ký</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Tổng sự kiện</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{events.length}</div>
            <p className="text-xs text-gray-500">Tất cả sự kiện</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Bộ lọc</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Loại sự kiện</label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn loại sự kiện" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="meeting">Họp phụ huynh</SelectItem>
                  <SelectItem value="performance">Biểu diễn</SelectItem>
                  <SelectItem value="field_trip">Dã ngoại</SelectItem>
                  <SelectItem value="workshop">Hội thảo</SelectItem>
                  <SelectItem value="sports">Thể thao</SelectItem>
                  <SelectItem value="festival">Lễ hội</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Trạng thái</label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="upcoming">Sắp tới</SelectItem>
                  <SelectItem value="past">Đã qua</SelectItem>
                  <SelectItem value="registered">Đã đăng ký</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Events List */}
      <div className="space-y-4">
        {filteredEvents.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Calendar className="h-16 w-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Chưa có sự kiện
              </h3>
              <p className="text-gray-500">
                Không có sự kiện nào cho bộ lọc đã chọn.
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredEvents.map((event) => {
            const registrationStatus = getRegistrationStatus(event);
            const isUpcoming = new Date(event.start_datetime) > new Date();

            return (
              <Card key={event.id} className={`${!isUpcoming ? 'opacity-75' : ''}`}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <CardTitle className="text-lg">{event.title}</CardTitle>
                        {getEventTypeBadge(event.event_type)}
                        {event.is_registered && (
                          <Badge variant="default">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Đã đăng ký
                          </Badge>
                        )}
                      </div>
                      {event.description && (
                        <p className="text-gray-600 mb-3">{event.description}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <Button 
                        variant={registrationStatus.variant}
                        disabled={registrationStatus.disabled}
                        size="sm"
                      >
                        {registrationStatus.text}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <div>
                        <div className="text-sm font-medium">
                          {formatDate(event.start_datetime)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatTime(event.start_datetime)} - {formatTime(event.end_datetime)}
                        </div>
                      </div>
                    </div>
                    
                    {event.location && (
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <div>
                          <div className="text-sm font-medium">Địa điểm</div>
                          <div className="text-xs text-gray-500">{event.location}</div>
                        </div>
                      </div>
                    )}

                    {event.max_participants && (
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <div>
                          <div className="text-sm font-medium">Số lượng</div>
                          <div className="text-xs text-gray-500">
                            {event.current_participants}/{event.max_participants} người
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {event.registration_deadline && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm text-yellow-800">
                          Hạn đăng ký: {formatDateTime(event.registration_deadline)}
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
}
