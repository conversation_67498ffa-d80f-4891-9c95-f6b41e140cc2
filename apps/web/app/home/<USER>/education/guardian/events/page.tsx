import { Suspense } from 'react';

import { TeamAccountLayoutPageHeader } from '../../../_components/team-account-layout-page-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { GuardianEvents } from './_components/guardian-events';
import { GuardianEventsSkeleton } from './_components/guardian-events-skeleton';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

async function GuardianEventsPage({ params }: Props) {
  const { account: accountSlug } = await params;
  const i18n = await createI18nServerInstance();
  const { user, account } = await loadTeamWorkspace(accountSlug);

  return (
    <div className="flex flex-col space-y-6 p-4 md:p-6">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:guardian.events.title')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/guardian`}>
              {i18n.t('education:breadcrumbs.guardian')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>{i18n.t('education:guardian.events.title')}</AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-7xl">
        <Suspense fallback={<GuardianEventsSkeleton />}>
          <GuardianEvents account={accountSlug} />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(GuardianEventsPage);

export const metadata = {
  title: 'Guardian Events',
  description: 'Sự kiện và hoạt động của trường',
};
