'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Avatar, AvatarFallback } from '@kit/ui/avatar';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

import {
  Bell,
  Calendar,
  CheckCircle,
  ClipboardCheck,
  Clock,
  DollarSign,
  Heart,
  MessageCircle,
  User,
  XCircle,
  BookOpen,
  Star,
  TrendingUp,
  AlertCircle,
  Phone,
  Mail,
  MapPin,
  Users,
  Home,
  LogOut,
} from 'lucide-react';

interface Child {
  id: string;
  learner_code: string;
  full_name: string;
  nickname: string;
  date_of_birth: string;
  program: {
    name: string;
    program_type: string;
  };
  recent_attendance: Array<{
    date: string;
    status: string;
    check_in_time?: string;
    check_out_time?: string;
  }>;
  pending_fees: Array<{
    id: string;
    description: string;
    amount: number;
    due_date: string;
  }>;
}

interface ParentData {
  children: Child[];
  unread_messages: number;
  upcoming_events_count: number;
  total_pending_fees: number;
}

interface Props {
  account: string;
}

export function ParentPortal({ account }: Props) {
  const { t } = useTranslation('education');
  const [parentData, setParentData] = useState<ParentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  const loadParentData = async () => {
    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      // Get children data
      const { data: learners, error: learnersError } = await supabase
        .from('learners')
        .select(`
          *,
          enrollments!inner (
            programs (
              id,
              name,
              program_type
            )
          )
        `)
        .eq('account_id', accountId)
        .eq('status', 'active');

      if (learnersError) {
        console.error('Error loading children:', learnersError);
        setError('Failed to load children data');
        return;
      }

      // Get attendance data for each child
      const learnersWithAttendance = await Promise.all(
        (learners || []).map(async (learner) => {
          const { data: attendance } = await supabase
            .from('attendance')
            .select('*')
            .eq('learner_id', learner.id)
            .order('session_date', { ascending: false })
            .limit(5);

          return {
            ...learner,
            recent_attendance: attendance || [],
          };
        })
      );

      // Get pending fees
      const { data: fees } = await supabase
        .from('fees')
        .select('*')
        .eq('account_id', accountId)
        .eq('status', 'pending');

      // Get unread messages count
      const { count: unreadCount } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('account_id', accountId)
        .eq('is_read', false);

      // Get upcoming events
      const { data: events } = await supabase
        .from('events')
        .select('*')
        .eq('account_id', accountId)
        .gte('start_datetime', new Date().toISOString())
        .order('start_datetime', { ascending: true })
        .limit(5);

      // Transform data
      const transformedChildren: Child[] = learnersWithAttendance.map((learner) => ({
        id: learner.id,
        learner_code: learner.learner_code,
        full_name: learner.full_name,
        nickname: learner.nickname,
        date_of_birth: learner.date_of_birth,
        program: {
          name: learner.enrollments?.[0]?.programs?.name || 'No program',
          program_type: learner.enrollments?.[0]?.programs?.program_type || 'unknown',
        },
        recent_attendance: learner.recent_attendance.map((att: any) => ({
          date: att.session_date,
          status: att.status,
          check_in_time: att.check_in_time,
          check_out_time: att.check_out_time,
        })),
        pending_fees: (fees || [])
          .filter((fee: any) => fee.learner_id === learner.id)
          .map((fee: any) => ({
            id: fee.id,
            description: fee.description,
            amount: fee.amount,
            due_date: fee.due_date,
          })),
      }));

      const totalPendingFees = (fees || []).reduce((sum: number, fee: any) => sum + fee.amount, 0);

      setParentData({
        children: transformedChildren,
        unread_messages: unreadCount || 0,
        upcoming_events_count: (events || []).length,
        total_pending_fees: totalPendingFees,
      });
    } catch (err: any) {
      console.error('Error loading parent data:', err);
      setError(err.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadParentData();
  }, []);

  const handleLogout = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải thông tin...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadParentData}>Thử lại</Button>
        </div>
      </div>
    );
  }

  if (!parentData) {
    return null;
  }

  return (
    <div className="min-h-screen">
      {/* Parent Portal Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-pink-100 rounded-lg">
                <Heart className="h-6 w-6 text-pink-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Cổng Phụ Huynh</h1>
                <p className="text-sm text-gray-600">Theo dõi con em học tập</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/home/<USER>
                  <Home className="h-4 w-4 mr-2" />
                  Trang chủ
                </Link>
              </Button>
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Đăng xuất
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <Users className="h-8 w-8" />
                <div>
                  <div className="text-2xl font-bold">{parentData.children.length}</div>
                  <div className="text-blue-100 text-sm">Con em</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <DollarSign className="h-8 w-8" />
                <div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                      maximumFractionDigits: 0,
                    }).format(parentData.total_pending_fees)}
                  </div>
                  <div className="text-orange-100 text-sm">Học phí chưa thanh toán</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <Calendar className="h-8 w-8" />
                <div>
                  <div className="text-2xl font-bold">{parentData.upcoming_events_count}</div>
                  <div className="text-green-100 text-sm">Sự kiện sắp tới</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <Bell className="h-8 w-8" />
                <div>
                  <div className="text-2xl font-bold">{parentData.unread_messages}</div>
                  <div className="text-purple-100 text-sm">Thông báo mới</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Children Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {parentData.children.map((child) => (
            <Card key={child.id} className="border-0 shadow-lg bg-white overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500"></div>
              
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-4">
                  <div className="relative">
                    <Avatar className="h-12 w-12 border-2 border-pink-200">
                      <AvatarFallback className="bg-pink-100 text-pink-700 text-lg font-semibold">
                        {child.full_name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <div className="flex-1">
                    <div className="font-bold text-lg text-gray-900">{child.full_name}</div>
                    {child.nickname && (
                      <div className="text-sm text-gray-600 font-medium">
                        "{child.nickname}"
                      </div>
                    )}
                    <div className="text-xs text-gray-500 mt-1">
                      Mã học viên: {child.learner_code}
                    </div>
                  </div>
                </CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Program Info */}
                <div className="p-4 bg-blue-50 rounded-xl border border-blue-100">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <BookOpen className="h-4 w-4 text-blue-600" />
                    </div>
                    <h4 className="text-blue-900 font-semibold text-sm uppercase tracking-wide">
                      Chương trình học
                    </h4>
                  </div>
                  <p className="text-blue-800 font-medium">{child.program.name}</p>
                  <p className="text-blue-600 text-sm mt-1 capitalize">{child.program.program_type}</p>
                </div>

                {/* Recent Attendance */}
                <div className="p-4 bg-green-50 rounded-xl border border-green-100">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <ClipboardCheck className="h-4 w-4 text-green-600" />
                    </div>
                    <h4 className="text-green-900 font-semibold text-sm uppercase tracking-wide">
                      Điểm danh gần đây
                    </h4>
                  </div>
                  {child.recent_attendance.length > 0 ? (
                    <div className="space-y-2">
                      {child.recent_attendance.slice(0, 3).map((attendance, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-white rounded-lg border border-green-100"
                        >
                          <div className="flex items-center gap-2">
                            {attendance.status === 'present' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : attendance.status === 'absent' ? (
                              <XCircle className="h-4 w-4 text-red-600" />
                            ) : (
                              <Clock className="h-4 w-4 text-yellow-600" />
                            )}
                            <span className="text-sm font-medium">
                              {new Date(attendance.date).toLocaleDateString('vi-VN')}
                            </span>
                          </div>
                          <Badge 
                            variant="outline"
                            className={`text-xs ${
                              attendance.status === 'present'
                                ? 'bg-green-100 text-green-800 border-green-200'
                                : attendance.status === 'absent'
                                ? 'bg-red-100 text-red-800 border-red-200'
                                : 'bg-yellow-100 text-yellow-800 border-yellow-200'
                            }`}
                          >
                            {attendance.status === 'present'
                              ? 'Có mặt'
                              : attendance.status === 'absent'
                              ? 'Vắng mặt'
                              : 'Muộn'}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <ClipboardCheck className="h-8 w-8 text-green-300 mx-auto mb-2" />
                      <p className="text-green-700 text-sm">Chưa có dữ liệu điểm danh</p>
                    </div>
                  )}
                </div>

                {/* Pending Fees */}
                {child.pending_fees.length > 0 && (
                  <div className="p-4 bg-orange-50 rounded-xl border border-orange-100">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-orange-100 rounded-lg">
                        <DollarSign className="h-4 w-4 text-orange-600" />
                      </div>
                      <h4 className="text-orange-900 font-semibold text-sm uppercase tracking-wide">
                        Học phí chưa thanh toán
                      </h4>
                    </div>
                    <div className="space-y-2">
                      {child.pending_fees.slice(0, 2).map((fee) => (
                        <div
                          key={fee.id}
                          className="flex items-center justify-between p-2 bg-white rounded-lg border border-orange-100"
                        >
                          <div>
                            <div className="font-medium text-sm text-gray-900">{fee.description}</div>
                            <div className="text-xs text-gray-600">
                              Hạn: {new Date(fee.due_date).toLocaleDateString('vi-VN')}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-orange-800">
                              {new Intl.NumberFormat('vi-VN', {
                                style: 'currency',
                                currency: 'VND',
                              }).format(fee.amount)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    asChild
                    className="flex-1 bg-white hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
                  >
                    <Link href={`/home/<USER>/education/guardian/children/${child.id}`}>
                      <User className="mr-2 h-4 w-4" />
                      Chi tiết
                    </Link>
                  </Button>
                  {child.pending_fees.length > 0 && (
                    <Button 
                      size="sm" 
                      asChild
                      className="flex-1 bg-orange-600 hover:bg-orange-700"
                    >
                      <Link href={`/home/<USER>/education/guardian/fees`}>
                        <DollarSign className="mr-2 h-4 w-4" />
                        Thanh toán
                      </Link>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card className="border-0 shadow-lg bg-white">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Star className="h-5 w-5 text-purple-600" />
              </div>
              Thao tác nhanh
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                asChild
                className="h-20 flex-col gap-2 bg-white hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
              >
                <Link href={`/home/<USER>/education/guardian/attendance`}>
                  <ClipboardCheck className="h-6 w-6" />
                  <span className="text-sm font-medium">Xem điểm danh</span>
                </Link>
              </Button>
              <Button 
                variant="outline" 
                asChild
                className="h-20 flex-col gap-2 bg-white hover:bg-orange-50 hover:text-orange-600 hover:border-orange-300"
              >
                <Link href={`/home/<USER>/education/guardian/fees`}>
                  <DollarSign className="h-6 w-6" />
                  <span className="text-sm font-medium">Thanh toán học phí</span>
                </Link>
              </Button>
              <Button 
                variant="outline" 
                asChild
                className="h-20 flex-col gap-2 bg-white hover:bg-green-50 hover:text-green-600 hover:border-green-300"
              >
                <Link href={`/home/<USER>/education/guardian/events`}>
                  <Calendar className="h-6 w-6" />
                  <span className="text-sm font-medium">Sự kiện</span>
                </Link>
              </Button>
              <Button 
                variant="outline" 
                asChild
                className="h-20 flex-col gap-2 bg-white hover:bg-purple-50 hover:text-purple-600 hover:border-purple-300"
              >
                <Link href={`/home/<USER>/education/guardian/messages`}>
                  <MessageCircle className="h-6 w-6" />
                  <span className="text-sm font-medium">Tin nhắn</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
