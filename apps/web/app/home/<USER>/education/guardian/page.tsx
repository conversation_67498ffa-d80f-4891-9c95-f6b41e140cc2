import { Suspense } from 'react';
import { redirect } from 'next/navigation';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { GuardianDashboard } from './_components/guardian-dashboard';
import { GuardianDashboardSkeleton } from './_components/guardian-dashboard-skeleton';
import { ParentPortal } from './_components/parent-portal';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

async function GuardianPage({ params }: Props) {
  const { account: accountSlug } = await params;
  const i18n = await createI18nServerInstance();
  const { user, account } = await loadTeamWorkspace(accountSlug);

  // Check user role to determine which interface to show
  const userRole = user?.role || 'member';
  const isParent = userRole === 'guardian' || userRole === 'customer';

  // If user is a parent/guardian, show dedicated parent portal
  if (isParent) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-rose-50 to-orange-50">
        <Suspense fallback={<GuardianDashboardSkeleton />}>
          <ParentPortal account={accountSlug} />
        </Suspense>
      </div>
    );
  }

  // For admin/teachers, show regular dashboard with breadcrumbs
  return (
    <div className="flex flex-col space-y-6 p-4 md:p-6">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:guardian.title')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:breadcrumbs.guardian')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-7xl">
        <Suspense fallback={<GuardianDashboardSkeleton />}>
          <GuardianDashboard account={accountSlug} />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(GuardianPage);

export const metadata = {
  title: 'Guardian Dashboard',
  description: 'Theo dõi con em của bạn',
};
