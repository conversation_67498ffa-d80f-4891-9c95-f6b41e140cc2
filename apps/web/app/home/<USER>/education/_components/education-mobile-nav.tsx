'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  Users,
  BookOpen,
  GraduationCap,
  ClipboardCheck,
  DollarSign,
  Calendar,
  MessageSquare,
  FileBarChart,
  Menu,
  X,
  Heart,
  Bus,
  UtensilsCrossed,
  BookOpenCheck,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@kit/ui/sheet';
import { Badge } from '@kit/ui/badge';
import { useTranslation } from 'react-i18next';
import { cn } from '@kit/ui/utils';

interface Props {
  account: string;
  metrics: {
    totalLearners: number;
    totalPrograms: number;
    attendanceRate: number;
    upcomingEvents: number;
    pendingFees: number;
  };
}

export function EducationMobileNav({ account, metrics }: Props) {
  const { t } = useTranslation('education');
  const [isOpen, setIsOpen] = useState(false);

  const navigationItems = [
    {
      label: t('dashboard.navigation.learners.title'),
      path: `/home/<USER>/education/learners`,
      icon: Users,
      description: t('dashboard.navigation.learners.description'),
      count: metrics.totalLearners,
      color: 'bg-blue-500',
    },
    {
      label: t('dashboard.navigation.programs.title'),
      path: `/home/<USER>/education/programs`,
      icon: BookOpen,
      description: t('dashboard.navigation.programs.description'),
      count: metrics.totalPrograms,
      color: 'bg-green-500',
    },
    {
      label: t('dashboard.navigation.instructors.title'),
      path: `/home/<USER>/education/instructors`,
      icon: GraduationCap,
      description: t('dashboard.navigation.instructors.description'),
      count: 8,
      color: 'bg-purple-500',
    },
    {
      label: t('dashboard.navigation.attendance.title'),
      path: `/home/<USER>/education/attendance`,
      icon: ClipboardCheck,
      description: t('dashboard.navigation.attendance.description'),
      count: `${metrics.attendanceRate}%`,
      color: 'bg-orange-500',
    },
    {
      label: t('dashboard.navigation.fees.title'),
      path: `/home/<USER>/education/fees`,
      icon: DollarSign,
      description: t('dashboard.navigation.fees.description'),
      count: metrics.pendingFees,
      color: 'bg-red-500',
    },
    {
      label: t('dashboard.navigation.events.title'),
      path: `/home/<USER>/education/events`,
      icon: Calendar,
      description: t('dashboard.navigation.events.description'),
      count: metrics.upcomingEvents,
      color: 'bg-indigo-500',
    },
    {
      label: t('dashboard.navigation.messages.title'),
      path: `/home/<USER>/education/messages`,
      icon: MessageSquare,
      description: t('dashboard.navigation.messages.description'),
      count: t('dashboard.metrics.newRegistrations'),
      color: 'bg-pink-500',
    },
    {
      label: t('dashboard.navigation.reports.title'),
      path: `/home/<USER>/education/reports`,
      icon: FileBarChart,
      description: t('dashboard.navigation.reports.description'),
      count: '',
      color: 'bg-gray-500',
    },
    {
      label: t('dashboard.navigation.curriculum.title'),
      path: `/home/<USER>/education/curriculum`,
      icon: BookOpenCheck,
      description: t('dashboard.navigation.curriculum.description'),
      count: '',
      color: 'bg-emerald-500',
    },
    {
      label: t('dashboard.navigation.health.title'),
      path: `/home/<USER>/education/health`,
      icon: Heart,
      description: t('dashboard.navigation.health.description'),
      count: '',
      color: 'bg-red-500',
    },
    {
      label: t('dashboard.navigation.transportation.title'),
      path: `/home/<USER>/education/transportation`,
      icon: Bus,
      description: t('dashboard.navigation.transportation.description'),
      count: '',
      color: 'bg-yellow-500',
    },
    {
      label: t('dashboard.navigation.meals.title'),
      path: `/home/<USER>/education/meals`,
      icon: UtensilsCrossed,
      description: t('dashboard.navigation.meals.description'),
      count: '',
      color: 'bg-orange-500',
    },
  ];

  return (
    <div className="lg:hidden">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" size="sm" className="fixed bottom-4 right-4 z-50 shadow-lg">
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="h-[80vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Education Features
            </h2>
            <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link 
                  key={item.path} 
                  href={item.path}
                  onClick={() => setIsOpen(false)}
                  className="block"
                >
                  <div className="p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <div className={cn(
                        "p-2 rounded-lg",
                        item.color,
                        "text-white"
                      )}>
                        <Icon className="h-5 w-5" />
                      </div>
                      {item.count && (
                        <Badge variant="secondary" className="text-xs">
                          {item.count}
                        </Badge>
                      )}
                    </div>
                    
                    <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {item.label}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                      {item.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>
          
          {/* Quick Actions for Mobile */}
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
              {t('dashboard.quickActions.title')}
            </h3>
            <div className="space-y-3">
              <Button asChild className="w-full justify-start bg-blue-600 hover:bg-blue-700">
                <Link href={`/home/<USER>/education/learners/new`} onClick={() => setIsOpen(false)}>
                  <Users className="h-4 w-4 mr-2" />
                  {t('dashboard.quickActions.addLearner')}
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full justify-start">
                <Link href={`/home/<USER>/education/attendance/qr`} onClick={() => setIsOpen(false)}>
                  <ClipboardCheck className="h-4 w-4 mr-2" />
                  {t('dashboard.quickActions.qrAttendance')}
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full justify-start">
                <Link href={`/home/<USER>/education/events/new`} onClick={() => setIsOpen(false)}>
                  <Calendar className="h-4 w-4 mr-2" />
                  {t('dashboard.quickActions.createEvent')}
                </Link>
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
