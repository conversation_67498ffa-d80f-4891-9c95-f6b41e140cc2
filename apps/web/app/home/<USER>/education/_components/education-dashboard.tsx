'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import {
  BarChart3,
  BookOpen,
  Calendar,
  ClipboardCheck,
  DollarSign,
  FileBarChart,
  Folder,
  Gamepad2,
  GraduationCap,
  MessageSquare,
  Minus,
  TrendingDown,
  TrendingUp,
  Users,
  Heart,
  Bus,
  UtensilsCrossed,
  BookOpenCheck,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Separator } from '@kit/ui/separator';
import { cn } from '@kit/ui/utils';

import { KINDERGARTEN_MOCKUP } from '~/config/education.mockup.config';

import { EducationLoadingPulse } from './education-loading';
import { EducationMobileNav } from './education-mobile-nav';
import { EducationStatsWidget } from './education-stats-widget';

interface Props {
  account: string;
}

interface DashboardMetrics {
  totalLearners: number;
  activeLearners: number;
  totalPrograms: number;
  activePrograms: number;
  attendanceRate: number;
  monthlyRevenue: number;
  pendingFees: number;
  upcomingEvents: number;
  newEnrollments: number;
  growth: {
    learners: number;
    revenue: number;
    attendance: number;
  };
}

export function EducationDashboard({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get current organization
  const currentAccount = accounts?.find((acc) => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;

    loadMetrics();
  }, [currentAccount?.id, supabase]);

  const loadMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setMetrics({
          totalLearners: 0,
          activeLearners: 0,
          totalPrograms: 0,
          activePrograms: 0,
          attendanceRate: 0,
          monthlyRevenue: 0,
          pendingFees: 0,
          upcomingEvents: 0,
          newEnrollments: 0,
          growth: { learners: 0, revenue: 0, attendance: 0 },
        });
        return;
      }

      // Load all metrics in parallel
      const [
        learnersResult,
        programsResult,
        attendanceResult,
        feesResult,
        eventsResult,
      ] = await Promise.all([
        // Learners
        supabase
          .from('learners')
          .select('id, status, created_at')
          .eq('account_id', accountId),

        // Programs
        supabase
          .from('programs')
          .select('id, status, created_at')
          .eq('account_id', accountId),

        // Attendance (last 30 days) - need to join with learners
        supabase
          .from('attendance')
          .select(`
            id,
            status,
            session_date,
            learners!inner (
              account_id
            )
          `)
          .eq('learners.account_id', accountId)
          .gte(
            'session_date',
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
              .toISOString()
              .split('T')[0],
          ),

        // Fees
        supabase
          .from('fees')
          .select('id, status, amount, created_at, payment_date')
          .eq('account_id', accountId),

        // Events
        supabase
          .from('events')
          .select('id, start_datetime, created_at')
          .eq('account_id', accountId)
          .gte('start_datetime', new Date().toISOString()),
      ]);

      // Process results
      const learners = learnersResult.data || [];
      const programs = programsResult.data || [];
      const attendance = attendanceResult.data || [];
      const fees = feesResult.data || [];
      const events = eventsResult.data || [];

      // Calculate metrics
      const totalLearners = learners.length;
      const activeLearners = learners.filter(
        (l) => l.status === 'active',
      ).length;
      const totalPrograms = programs.length;
      const activePrograms = programs.filter(
        (p) => p.status === 'active',
      ).length;

      const attendanceRate =
        attendance.length > 0
          ? Math.round(
              (attendance.filter((a) => a.status === 'present').length /
                attendance.length) *
                100,
            )
          : 0;

      const monthlyRevenue = fees
        .filter(
          (f) =>
            f.status === 'paid' &&
            f.payment_date &&
            new Date(f.payment_date).getMonth() === new Date().getMonth(),
        )
        .reduce((sum, f) => sum + (f.amount || 0), 0);

      const pendingFees = fees.filter((f) => f.status === 'pending').length;
      const upcomingEvents = events.length;

      const newEnrollments = learners.filter(
        (l) => new Date(l.created_at).getMonth() === new Date().getMonth(),
      ).length;

      // Calculate growth (simplified - you might want to implement proper growth calculation)
      const growth = {
        learners: newEnrollments > 0 ? 8.5 : 0, // Mock growth for now
        revenue: monthlyRevenue > 0 ? 12.3 : 0,
        attendance: attendanceRate > 90 ? 2.1 : -2.1,
      };

      setMetrics({
        totalLearners,
        activeLearners,
        totalPrograms,
        activePrograms,
        attendanceRate,
        monthlyRevenue,
        pendingFees,
        upcomingEvents,
        newEnrollments,
        growth,
      });
    } catch (err: any) {
      console.error('Error loading metrics:', err);
      setError(err.message || 'Failed to load metrics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <EducationLoadingPulse />;
  }

  if (!metrics) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <TrendingDown className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
            {t('common.error', 'Unable to load data')}
          </h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            There was an error loading the education dashboard. Please try
            refreshing the page.
          </p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Refresh Page
          </Button>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatGrowth = (growth: number) => {
    const isPositive = growth > 0;
    const isNegative = growth < 0;

    return (
      <div
        className={`flex items-center space-x-1 text-sm ${
          isPositive
            ? 'text-green-600'
            : isNegative
              ? 'text-red-600'
              : 'text-gray-500'
        }`}
      >
        {isPositive && <TrendingUp className="h-4 w-4" />}
        {isNegative && <TrendingDown className="h-4 w-4" />}
        {!isPositive && !isNegative && <Minus className="h-4 w-4" />}
        <span>{Math.abs(growth)}%</span>
      </div>
    );
  };

  const navigationItems = [
    {
      label: t('dashboard.navigation.learners.title'),
      path: `/home/<USER>/education/learners`,
      icon: Users,
      description: t('dashboard.navigation.learners.description'),
      count: metrics.totalLearners,
      color: 'bg-blue-500',
    },
    {
      label: t('dashboard.navigation.programs.title'),
      path: `/home/<USER>/education/programs`,
      icon: BookOpen,
      description: t('dashboard.navigation.programs.description'),
      count: metrics.totalPrograms,
      color: 'bg-green-500',
    },
    {
      label: t('dashboard.navigation.instructors.title'),
      path: `/home/<USER>/education/instructors`,
      icon: GraduationCap,
      description: t('dashboard.navigation.instructors.description'),
      count: 8,
      color: 'bg-purple-500',
    },
    {
      label: t('dashboard.navigation.attendance.title'),
      path: `/home/<USER>/education/attendance`,
      icon: ClipboardCheck,
      description: t('dashboard.navigation.attendance.description'),
      count: `${metrics.attendanceRate}%`,
      color: 'bg-orange-500',
    },
    {
      label: t('dashboard.navigation.fees.title'),
      path: `/home/<USER>/education/fees`,
      icon: DollarSign,
      description: t('dashboard.navigation.fees.description'),
      count: metrics.pendingFees,
      color: 'bg-red-500',
    },
    {
      label: t('dashboard.navigation.events.title'),
      path: `/home/<USER>/education/events`,
      icon: Calendar,
      description: t('dashboard.navigation.events.description'),
      count: metrics.upcomingEvents,
      color: 'bg-indigo-500',
    },
    {
      label: t('dashboard.navigation.messages.title'),
      path: `/home/<USER>/education/messages`,
      icon: MessageSquare,
      description: t('dashboard.navigation.messages.description'),
      count: t('dashboard.metrics.newRegistrations'),
      color: 'bg-pink-500',
    },
    {
      label: t('dashboard.navigation.reports.title'),
      path: `/home/<USER>/education/reports`,
      icon: FileBarChart,
      description: t('dashboard.navigation.reports.description'),
      count: '',
      color: 'bg-gray-500',
    },
    {
      label: t('dashboard.navigation.guardian.title'),
      path: `/home/<USER>/education/guardian`,
      icon: Users,
      description: t('dashboard.navigation.guardian.description'),
      count: '',
      color: 'bg-pink-500',
    },
    {
      label: t('dashboard.navigation.curriculum.title'),
      path: `/home/<USER>/education/curriculum`,
      icon: BookOpenCheck,
      description: t('dashboard.navigation.curriculum.description'),
      count: '',
      color: 'bg-emerald-500',
    },
    {
      label: t('dashboard.navigation.health.title'),
      path: `/home/<USER>/education/health`,
      icon: Heart,
      description: t('dashboard.navigation.health.description'),
      count: '',
      color: 'bg-red-500',
    },
    {
      label: t('dashboard.navigation.transportation.title'),
      path: `/home/<USER>/education/transportation`,
      icon: Bus,
      description: t('dashboard.navigation.transportation.description'),
      count: '',
      color: 'bg-yellow-500',
    },
    {
      label: t('dashboard.navigation.meals.title'),
      path: `/home/<USER>/education/meals`,
      icon: UtensilsCrossed,
      description: t('dashboard.navigation.meals.description'),
      count: '',
      color: 'bg-orange-500',
    },
    {
      label: t('dashboard.navigation.library.title'),
      path: `/home/<USER>/education/library`,
      icon: Folder,
      description: t('dashboard.navigation.library.description'),
      count: '',
      color: 'bg-teal-500',
    },
    {
      label: 'Games & Activities',
      path: `/home/<USER>/education/games`,
      icon: Gamepad2,
      description: 'Educational games and interactive learning',
      count: '',
      color: 'bg-purple-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800 dark:from-blue-950/20 dark:to-indigo-950/20">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/50">
                <BarChart3 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {t('dashboard.welcome', {
                    organizationName: KINDERGARTEN_MOCKUP.organizationName,
                  })}
                </CardTitle>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  {t('dashboard.welcomeDescription', {
                    organizationType: t(
                      `organizationType.${KINDERGARTEN_MOCKUP.organizationType}`,
                    ),
                  })}
                </p>
              </div>
            </div>
            <Badge
              variant="secondary"
              className="bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300"
            >
              {t('dashboard.title')}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics */}
      <EducationStatsWidget data={metrics} />

      {/* Navigation Grid */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            {t('dashboard.navigation.title', 'Education Features')}
          </h2>
          <Separator className="ml-4 flex-1" />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link key={item.path} href={item.path}>
                <Card className="group cursor-pointer overflow-hidden border-0 bg-white shadow-md transition-all duration-300 hover:scale-105 hover:shadow-xl dark:bg-gray-800">
                  <div className="relative">
                    <div
                      className={cn(
                        'absolute inset-0 opacity-10 transition-opacity group-hover:opacity-20',
                        item.color
                          .replace('bg-', 'from- bg-gradient-to-br')
                          .replace('-500', '-400 to-')
                          .replace('500', '600'),
                      )}
                    />

                    <CardHeader className="relative pb-3">
                      <div className="flex items-center justify-between">
                        <div
                          className={cn(
                            'rounded-xl p-3 shadow-lg transition-transform duration-300 group-hover:scale-110',
                            item.color,
                            'text-white',
                          )}
                        >
                          <Icon className="h-6 w-6" />
                        </div>
                        {item.count && (
                          <Badge
                            variant="secondary"
                            className="bg-gray-100 font-semibold text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                          >
                            {item.count}
                          </Badge>
                        )}
                      </div>

                      <div className="mt-4">
                        <CardTitle className="text-lg font-semibold text-gray-900 transition-colors group-hover:text-blue-600 dark:text-gray-100 dark:group-hover:text-blue-400">
                          {item.label}
                        </CardTitle>
                        <p className="mt-1 line-clamp-2 text-sm text-gray-600 dark:text-gray-400">
                          {item.description}
                        </p>
                      </div>
                    </CardHeader>

                    <CardContent className="relative pt-0">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-between transition-colors group-hover:bg-blue-50 group-hover:text-blue-600 dark:group-hover:bg-blue-900/20 dark:group-hover:text-blue-400"
                      >
                        <span>{t('common.viewDetails')}</span>
                        <TrendingUp className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </CardContent>
                  </div>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 dark:border-gray-700 dark:from-gray-800 dark:to-gray-900">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/50">
              <TrendingUp className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {t('dashboard.quickActions.title')}
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Button
              asChild
              size="lg"
              className="h-14 bg-blue-600 text-white shadow-lg transition-all duration-200 hover:bg-blue-700 hover:shadow-xl"
            >
              <Link
                href={`/home/<USER>/education/learners/new`}
                className="flex items-center space-x-2"
              >
                <Users className="h-5 w-5" />
                <span>{t('dashboard.quickActions.addLearner')}</span>
              </Link>
            </Button>
            <Button
              variant="outline"
              asChild
              size="lg"
              className="h-14 border-2 border-green-200 transition-all duration-200 hover:border-green-300 hover:bg-green-50 dark:border-green-800 dark:hover:border-green-700 dark:hover:bg-green-900/20"
            >
              <Link
                href={`/home/<USER>/education/attendance/qr`}
                className="flex items-center space-x-2"
              >
                <ClipboardCheck className="h-5 w-5" />
                <span>{t('dashboard.quickActions.qrAttendance')}</span>
              </Link>
            </Button>
            <Button
              variant="outline"
              asChild
              size="lg"
              className="h-14 border-2 border-purple-200 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50 dark:border-purple-800 dark:hover:border-purple-700 dark:hover:bg-purple-900/20"
            >
              <Link
                href={`/home/<USER>/education/events/new`}
                className="flex items-center space-x-2"
              >
                <Calendar className="h-5 w-5" />
                <span>{t('dashboard.quickActions.createEvent')}</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="border-gray-200 dark:border-gray-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="rounded-lg bg-gray-100 p-2 dark:bg-gray-800">
                <ClipboardCheck className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              </div>
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {t('dashboard.recentActivity.title')}
              </CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-start space-x-4 rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20">
              <div className="flex-shrink-0">
                <div className="mt-1 h-3 w-3 rounded-full bg-green-500"></div>
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {t('dashboard.recentActivity.learnerCheckedIn', {
                    learnerName: 'Bé Nguyễn Minh An',
                  })}
                </p>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {t('dashboard.recentActivity.minutesAgo', { minutes: 5 })}
                </p>
              </div>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300"
              >
                New
              </Badge>
            </div>

            <div className="flex items-start space-x-4 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-900/20">
              <div className="flex-shrink-0">
                <div className="mt-1 h-3 w-3 rounded-full bg-blue-500"></div>
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {t('dashboard.recentActivity.feePayment', {
                    guardianName: 'Phụ huynh Trần Thị Mai',
                  })}
                </p>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {t('dashboard.recentActivity.hourAgo', { hours: 1 })}
                </p>
              </div>
              <Badge
                variant="secondary"
                className="bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300"
              >
                Payment
              </Badge>
            </div>

            <div className="flex items-start space-x-4 rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-900/20">
              <div className="flex-shrink-0">
                <div className="mt-1 h-3 w-3 rounded-full bg-orange-500"></div>
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {t('dashboard.recentActivity.eventRegistrations', {
                    eventName: 'Họp phụ huynh',
                    count: 5,
                  })}
                </p>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {t('dashboard.recentActivity.hoursAgo', { hours: 2 })}
                </p>
              </div>
              <Badge
                variant="secondary"
                className="bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300"
              >
                Event
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mobile Navigation */}
      <EducationMobileNav account={account} metrics={metrics} />
    </div>
  );
}
