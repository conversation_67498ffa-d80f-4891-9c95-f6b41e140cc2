import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '~/lib/database.types';

export type AttendanceRecord = {
  id: string;
  learner_id: string;
  program_id: string;
  session_date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  check_in_time?: string;
  check_out_time?: string;
  notes?: string;
  attendance_method: string;
  metadata?: Record<string, any>;
  confidence_score?: number;
  learner: {
    id: string;
    full_name: string;
    nickname?: string;
    learner_code?: string;
  };
};

export type AttendanceSession = {
  id: string;
  account_id: string;
  program_id: string;
  session_date: string;
  session_time?: string;
  session_type: string;
  instructor_id?: string;
  total_students: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  status: 'draft' | 'in_progress' | 'completed' | 'cancelled';
  notes?: string;
  metadata?: Record<string, any>;
  program?: {
    id: string;
    name: string;
    program_type: string;
  };
  instructor?: {
    id: string;
    full_name: string;
    employee_code?: string;
  };
};

export type AttendanceStats = {
  total_sessions: number;
  total_students: number;
  average_attendance_rate: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  trends: {
    date: string;
    attendance_rate: number;
  }[];
};

export type EnrolledStudent = {
  id: string;
  full_name: string;
  nickname?: string;
  learner_code?: string;
  enrollment_date: string;
};

export async function loadAttendanceBySession(
  client: SupabaseClient<Database>,
  accountId: string,
  programId: string,
  sessionDate: string
): Promise<AttendanceRecord[]> {
  if (!accountId || !programId || !sessionDate) {
    throw new Error('Account ID, Program ID, and Session Date are required');
  }

  try {
    const { data, error } = await client
      .from('attendance')
      .select(`
        *,
        learners!learner_id (
          id,
          full_name,
          nickname,
          learner_code
        )
      `)
      .eq('account_id', accountId)
      .eq('program_id', programId)
      .eq('session_date', sessionDate)
      .order('learners(full_name)');

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'attendance.select',
        accountId,
        programId,
        sessionDate,
      });
      throw error;
    }

    return (data || []).map((record) => ({
      id: record.id,
      learner_id: record.learner_id,
      program_id: record.program_id,
      session_date: record.session_date,
      status: record.status as 'present' | 'absent' | 'late' | 'excused',
      check_in_time: record.check_in_time,
      check_out_time: record.check_out_time,
      notes: record.notes,
      attendance_method: record.attendance_method,
      metadata: record.metadata,
      confidence_score: record.confidence_score,
      learner: {
        id: record.learners.id,
        full_name: record.learners.full_name,
        nickname: record.learners.nickname,
        learner_code: record.learners.learner_code,
      },
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load attendance records:', {
      error: message,
      accountId,
      programId,
      sessionDate,
      context: 'attendance-page.loader',
    });
    throw new Error(`Failed to load attendance records: ${message}`);
  }
}

export async function loadAttendanceSessions(
  client: SupabaseClient<Database>,
  accountId: string,
  programId?: string,
  limit = 20
): Promise<AttendanceSession[]> {
  if (!accountId) {
    throw new Error('Account ID is required');
  }

  try {
    let query = client
      .from('attendance_sessions')
      .select(`
        *,
        programs!program_id (
          id,
          name,
          program_type
        ),
        instructors!instructor_id (
          id,
          full_name,
          employee_code
        )
      `)
      .eq('account_id', accountId)
      .order('session_date', { ascending: false })
      .limit(limit);

    if (programId) {
      query = query.eq('program_id', programId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'attendance_sessions.select',
        accountId,
        programId,
      });
      throw error;
    }

    return (data || []).map((session) => ({
      id: session.id,
      account_id: session.account_id,
      program_id: session.program_id,
      session_date: session.session_date,
      session_time: session.session_time,
      session_type: session.session_type,
      instructor_id: session.instructor_id,
      total_students: session.total_students,
      present_count: session.present_count,
      absent_count: session.absent_count,
      late_count: session.late_count,
      status: session.status as 'draft' | 'in_progress' | 'completed' | 'cancelled',
      notes: session.notes,
      metadata: session.metadata,
      program: session.programs ? {
        id: session.programs.id,
        name: session.programs.name,
        program_type: session.programs.program_type,
      } : undefined,
      instructor: session.instructors ? {
        id: session.instructors.id,
        full_name: session.instructors.full_name,
        employee_code: session.instructors.employee_code,
      } : undefined,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load attendance sessions:', {
      error: message,
      accountId,
      programId,
      context: 'attendance-page.loader',
    });
    throw new Error(`Failed to load attendance sessions: ${message}`);
  }
}

export async function loadEnrolledStudents(
  client: SupabaseClient<Database>,
  accountId: string,
  programId: string
): Promise<EnrolledStudent[]> {
  if (!accountId || !programId) {
    throw new Error('Account ID and Program ID are required');
  }

  try {
    const { data, error } = await client
      .from('enrollments')
      .select(`
        enrollment_date,
        learners!learner_id (
          id,
          full_name,
          nickname,
          learner_code
        )
      `)
      .eq('account_id', accountId)
      .eq('program_id', programId)
      .eq('status', 'active')
      .order('learners(full_name)');

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'enrollments.select',
        accountId,
        programId,
      });
      throw error;
    }

    return (data || []).map((enrollment) => ({
      id: enrollment.learners.id,
      full_name: enrollment.learners.full_name,
      nickname: enrollment.learners.nickname,
      learner_code: enrollment.learners.learner_code,
      enrollment_date: enrollment.enrollment_date,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load enrolled students:', {
      error: message,
      accountId,
      programId,
      context: 'attendance-page.loader',
    });
    throw new Error(`Failed to load enrolled students: ${message}`);
  }
}
