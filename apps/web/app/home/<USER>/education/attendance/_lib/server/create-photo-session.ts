'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export interface CreatePhotoSessionParams {
  accountId: string;
  programId: string;
  sessionDate: string;
  sessionTime?: string;
  instructorId?: string;
  batchPhotoUrl?: string;
}

export interface CreatePhotoSessionResult {
  sessionId: string;
  enrolledCount: number;
  message: string;
}

export async function createPhotoAttendanceSession(
  params: CreatePhotoSessionParams
): Promise<CreatePhotoSessionResult> {
  const client = getSupabaseServerClient();

  try {
    const { data, error } = await client.rpc('create_photo_attendance_session', {
      p_account_id: params.accountId,
      p_program_id: params.programId,
      p_session_date: params.sessionDate,
      p_session_time: params.sessionTime || null,
      p_instructor_id: params.instructorId || null,
      p_batch_photo_url: params.batchPhotoUrl || null,
    });

    if (error) {
      console.error('Supabase RPC error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        function: 'create_photo_attendance_session',
        params,
      });
      throw error;
    }

    if (!data.success) {
      throw new Error(data.error || 'Failed to create photo attendance session');
    }

    return {
      sessionId: data.session_id,
      enrolledCount: data.enrolled_count,
      message: data.message,
    };
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to create photo attendance session:', {
      error: message,
      params,
      context: 'create-photo-session',
    });
    throw new Error(`Failed to create photo attendance session: ${message}`);
  }
}

export async function updatePhotoSessionStatus(
  sessionId: string,
  status: 'pending' | 'processing' | 'completed' | 'failed',
  totalFaces?: number,
  totalRecognized?: number,
  metadata?: Record<string, any>
): Promise<void> {
  const client = getSupabaseServerClient();

  try {
    const { data, error } = await client.rpc('update_photo_session_status', {
      p_session_id: sessionId,
      p_status: status,
      p_total_faces: totalFaces || null,
      p_total_recognized: totalRecognized || null,
      p_metadata: metadata || null,
    });

    if (error) {
      console.error('Supabase RPC error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        function: 'update_photo_session_status',
        sessionId,
        status,
      });
      throw error;
    }

    if (!data.success) {
      throw new Error(data.message || 'Failed to update photo session status');
    }
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to update photo session status:', {
      error: message,
      sessionId,
      status,
      context: 'create-photo-session',
    });
    throw new Error(`Failed to update photo session status: ${message}`);
  }
}
