// Types
export type {
  AttendanceRecord,
  AttendanceSession,
  AttendanceStats,
  EnrolledStudent,
} from './attendance-page.loader';

export type {
  LearnerPhoto,
  PhotoAttendanceSession,
  PhotoRecognitionResult,
} from './photo-attendance.loader';

export type {
  QRCheckinSession,
  SelfCheckinRecord,
  NFCTag,
  GuardianCheckinPermission,
} from './qr-checkin.loader';

export type {
  AttendanceAnalytics,
  AttendanceReport,
  AutomationRule,
  PredictiveInsight,
} from './analytics.loader';

export type {
  CreateAttendanceSessionParams,
  CreateAttendanceSessionResult,
} from './create-attendance-session';

export type {
  CreatePhotoSessionParams,
  CreatePhotoSessionResult,
} from './create-photo-session';

export type {
  UploadLearnerPhotoParams,
} from './upload-learner-photo';

export type {
  CreateQRSessionParams,
  CreateQRSessionResult,
} from './create-qr-session';

export type {
  ProcessQRCheckinParams,
  ProcessQRCheckinResult,
} from './process-qr-checkin';

export type {
  GenerateAnalyticsParams,
  AnalyticsResult,
  GenerateReportParams,
  ReportResult,
} from './generate-analytics';

export type {
  BulkAttendanceUpdate,
  BulkUpdateResult,
} from './update-attendance';

// Loaders
export {
  loadAttendanceBySession,
  loadAttendanceSessions,
  loadEnrolledStudents,
} from './attendance-page.loader';

export {
  loadLearnerPhotos,
  loadPhotoAttendanceSessions,
  loadPhotoRecognitionResults,
} from './photo-attendance.loader';

export {
  loadQRCheckinSessions,
  loadSelfCheckinRecords,
  loadNFCTags,
} from './qr-checkin.loader';

export {
  loadAttendanceAnalytics,
  loadAttendanceReports,
  loadPredictiveInsights,
} from './analytics.loader';

// Server Actions
export { getAttendanceRecords } from './get-attendance-records';
export { getAttendanceSessions } from './get-attendance-sessions';
export { getEnrolledStudents } from './get-enrolled-students';
export { getAttendanceStats } from './get-attendance-stats';
export { getOptimalAttendanceMethod } from './get-optimal-method';
export { createAttendanceSession } from './create-attendance-session';
export { bulkUpdateAttendance, updateSingleAttendance } from './update-attendance';

// Photo Attendance Actions
export { createPhotoAttendanceSession, updatePhotoSessionStatus } from './create-photo-session';
export { uploadLearnerPhoto, updateLearnerPhoto, deleteLearnerPhoto } from './upload-learner-photo';

// QR Check-in Actions
export { createQRCheckinSession, updateQRSessionStatus } from './create-qr-session';
export { processQRCheckin, verifyCheckinRecord } from './process-qr-checkin';

// Analytics Actions
export {
  generateAttendanceAnalytics,
  generateAttendanceReport,
  createAutomationRule,
  createPredictiveInsight
} from './generate-analytics';
