'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export interface UploadLearnerPhotoParams {
  learnerId: string;
  accountId: string;
  photoUrl: string;
  photoType?: 'profile' | 'verification' | 'attendance';
  faceEncoding?: Record<string, any>;
  qualityScore?: number;
  isPrimary?: boolean;
}

export async function uploadLearnerPhoto(
  params: UploadLearnerPhotoParams
): Promise<{ photoId: string }> {
  const client = getSupabaseServerClient();

  try {
    // If this is a primary photo, unset other primary photos for this learner
    if (params.isPrimary) {
      await client
        .from('learner_photos')
        .update({ is_primary: false })
        .eq('learner_id', params.learnerId)
        .eq('account_id', params.accountId);
    }

    const { data, error } = await client
      .from('learner_photos')
      .insert({
        learner_id: params.learnerId,
        account_id: params.accountId,
        photo_url: params.photoUrl,
        photo_type: params.photoType || 'profile',
        face_encoding: params.faceEncoding,
        quality_score: params.qualityScore,
        is_primary: params.isPrimary || false,
      })
      .select('id')
      .single();

    if (error) {
      console.error('Supabase insert error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        table: 'learner_photos',
        params,
      });
      throw error;
    }

    return { photoId: data.id };
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to upload learner photo:', {
      error: message,
      params,
      context: 'upload-learner-photo',
    });
    throw new Error(`Failed to upload learner photo: ${message}`);
  }
}

export async function updateLearnerPhoto(
  photoId: string,
  updates: {
    photoUrl?: string;
    faceEncoding?: Record<string, any>;
    qualityScore?: number;
    isPrimary?: boolean;
  }
): Promise<void> {
  const client = getSupabaseServerClient();

  try {
    // If setting as primary, unset other primary photos for this learner
    if (updates.isPrimary) {
      const { data: photo } = await client
        .from('learner_photos')
        .select('learner_id, account_id')
        .eq('id', photoId)
        .single();

      if (photo) {
        await client
          .from('learner_photos')
          .update({ is_primary: false })
          .eq('learner_id', photo.learner_id)
          .eq('account_id', photo.account_id)
          .neq('id', photoId);
      }
    }

    const { error } = await client
      .from('learner_photos')
      .update({
        photo_url: updates.photoUrl,
        face_encoding: updates.faceEncoding,
        quality_score: updates.qualityScore,
        is_primary: updates.isPrimary,
        updated_at: new Date().toISOString(),
      })
      .eq('id', photoId);

    if (error) {
      console.error('Supabase update error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        table: 'learner_photos',
        photoId,
        updates,
      });
      throw error;
    }
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to update learner photo:', {
      error: message,
      photoId,
      updates,
      context: 'upload-learner-photo',
    });
    throw new Error(`Failed to update learner photo: ${message}`);
  }
}

export async function deleteLearnerPhoto(photoId: string): Promise<void> {
  const client = getSupabaseServerClient();

  try {
    const { error } = await client
      .from('learner_photos')
      .delete()
      .eq('id', photoId);

    if (error) {
      console.error('Supabase delete error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        table: 'learner_photos',
        photoId,
      });
      throw error;
    }
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to delete learner photo:', {
      error: message,
      photoId,
      context: 'upload-learner-photo',
    });
    throw new Error(`Failed to delete learner photo: ${message}`);
  }
}
