'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { loadAttendanceSessions, type AttendanceSession } from './attendance-page.loader';

export async function getAttendanceSessions(
  accountId: string,
  programId?: string,
  limit = 20
): Promise<AttendanceSession[]> {
  const client = getSupabaseServerClient();
  return loadAttendanceSessions(client, accountId, programId, limit);
}
