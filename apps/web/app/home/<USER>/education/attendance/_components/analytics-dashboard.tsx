'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Download,
  RefreshCw,
  Calendar,
  Target
} from 'lucide-react';
import { toast } from 'sonner';

import { <PERSON><PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';

import {
  generateAttendanceAnalytics,
  generateAttendanceReport,
  type GenerateAnalyticsParams,
} from '../_lib/server/generate-analytics';

interface AnalyticsDashboardProps {
  accountId: string;
  programId?: string;
  programName?: string;
}

export function AnalyticsDashboard({
  accountId,
  programId,
  programName,
}: AnalyticsDashboardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'7days' | '30days' | '90days'>('30days');
  const [selectedAnalysisType, setSelectedAnalysisType] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  const loadAnalytics = useCallback(async () => {
    try {
      setIsLoading(true);

      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - (
        selectedPeriod === '7days' ? 7 * 24 * 60 * 60 * 1000 :
        selectedPeriod === '30days' ? 30 * 24 * 60 * 60 * 1000 :
        90 * 24 * 60 * 60 * 1000
      )).toISOString().split('T')[0];

      const result = await generateAttendanceAnalytics({
        accountId,
        programId,
        startDate,
        endDate,
        analysisType: selectedAnalysisType,
      });

      if (result.success) {
        setAnalyticsData(result.data);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Analytics loading error:', error);
      toast.error('Không thể tải dữ liệu phân tích');
    } finally {
      setIsLoading(false);
    }
  }, [accountId, programId, selectedPeriod, selectedAnalysisType]);

  const handleGenerateReport = useCallback(async () => {
    try {
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const result = await generateAttendanceReport({
        accountId,
        reportName: `Báo cáo điểm danh ${programName || 'Tổng quan'} - ${new Date().toLocaleDateString('vi-VN')}`,
        reportScope: programId ? 'program' : 'account',
        scopeId: programId,
        startDate,
        endDate,
      });

      if (result.success) {
        toast.success('Báo cáo đã được tạo thành công!');
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Report generation error:', error);
      toast.error('Không thể tạo báo cáo');
    }
  }, [accountId, programId, programName]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const getRiskLevelColor = (riskScore: number) => {
    if (riskScore <= 0.3) return 'text-green-600 bg-green-50';
    if (riskScore <= 0.6) return 'text-yellow-600 bg-yellow-50';
    if (riskScore <= 0.8) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  const getRiskLevelText = (riskScore: number) => {
    if (riskScore <= 0.3) return 'Thấp';
    if (riskScore <= 0.6) return 'Trung bình';
    if (riskScore <= 0.8) return 'Cao';
    return 'Rất cao';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Phân tích điểm danh</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {programName ? (
                <p>📚 Chương trình: <span className="font-medium">{programName}</span></p>
              ) : (
                <p>📊 Tổng quan tất cả chương trình</p>
              )}
            </div>

            <div className="flex gap-2">
              <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">7 ngày</SelectItem>
                  <SelectItem value="30days">30 ngày</SelectItem>
                  <SelectItem value="90days">90 ngày</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedAnalysisType} onValueChange={(value: any) => setSelectedAnalysisType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Hàng ngày</SelectItem>
                  <SelectItem value="weekly">Hàng tuần</SelectItem>
                  <SelectItem value="monthly">Hàng tháng</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" onClick={loadAnalytics} disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Làm mới
              </Button>

              <Button onClick={handleGenerateReport}>
                <Download className="h-4 w-4 mr-2" />
                Xuất báo cáo
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Đang phân tích dữ liệu...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analytics Results */}
      {analyticsData && !isLoading && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-blue-900">
                  {analyticsData.statistics.total_sessions}
                </div>
                <div className="text-sm text-blue-700">Tổng buổi học</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-green-900">
                  {analyticsData.statistics.attendance_rate}%
                </div>
                <div className="text-sm text-green-700">Tỷ lệ tham gia</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-orange-900">
                  {analyticsData.statistics.late_sessions}
                </div>
                <div className="text-sm text-orange-700">Đi muộn</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-red-900">
                  {analyticsData.statistics.absent_sessions}
                </div>
                <div className="text-sm text-red-700">Vắng mặt</div>
              </CardContent>
            </Card>
          </div>

          {/* Attendance Rate Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Tỷ lệ điểm danh</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Tỷ lệ hiện tại</span>
                  <span className="text-2xl font-bold">{analyticsData.statistics.attendance_rate}%</span>
                </div>
                <Progress value={analyticsData.statistics.attendance_rate} className="w-full" />
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-medium text-green-600">{analyticsData.statistics.present_sessions}</div>
                    <div className="text-muted-foreground">Có mặt</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-orange-600">{analyticsData.statistics.late_sessions}</div>
                    <div className="text-muted-foreground">Đi muộn</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-red-600">{analyticsData.statistics.absent_sessions}</div>
                    <div className="text-muted-foreground">Vắng mặt</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Risk Assessment */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5" />
                <span>Đánh giá rủi ro</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Mức độ rủi ro</span>
                  <Badge className={getRiskLevelColor(analyticsData.risk_score)}>
                    {getRiskLevelText(analyticsData.risk_score)}
                  </Badge>
                </div>
                <Progress value={analyticsData.risk_score * 100} className="w-full" />
                <div className="text-sm text-muted-foreground">
                  Điểm rủi ro: {(analyticsData.risk_score * 100).toFixed(1)}/100
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Patterns & Insights */}
          {analyticsData.patterns && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Xu hướng & Mẫu hình</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">Xu hướng tham gia</h4>
                      <p className="text-sm text-blue-800 capitalize">
                        {analyticsData.patterns.attendance_trend}
                      </p>
                    </div>
                    {analyticsData.patterns.most_absent_day && (
                      <div className="p-4 bg-orange-50 rounded-lg">
                        <h4 className="font-medium text-orange-900 mb-2">Ngày vắng nhiều nhất</h4>
                        <p className="text-sm text-orange-800">
                          Thứ {analyticsData.patterns.most_absent_day + 1}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {analyticsData.recommendations && analyticsData.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5" />
                  <span>Khuyến nghị</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analyticsData.recommendations.map((recommendation: string, index: number) => (
                    <div key={index} className="flex items-start space-x-2 p-3 bg-green-50 rounded-lg">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-green-800">{recommendation}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* No Data State */}
      {!analyticsData && !isLoading && (
        <Card>
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có dữ liệu phân tích</h3>
            <p className="text-gray-600 mb-4">
              Cần có dữ liệu điểm danh để tạo báo cáo phân tích
            </p>
            <Button onClick={loadAnalytics}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Thử lại
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
