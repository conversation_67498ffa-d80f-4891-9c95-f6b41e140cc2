'use client';

import { useState, useCallback, useEffect } from 'react';
import { QrCode, Users, Clock, CheckCircle, AlertCircle, Copy, RefreshCw, MapPin } from 'lucide-react';
import { toast } from 'sonner';
import QRCodeLib from 'qrcode';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Switch } from '@kit/ui/switch';

import {
  createQRCheckinSession,
  updateQRSessionStatus,
  type CreateQRSessionParams,
} from '../_lib/server/create-qr-session';

interface QRCheckinProps {
  accountId: string;
  programId: string;
  programName: string;
  sessionDate: string;
  onComplete?: (results: { checkins: number; qrCode: string }) => void;
}

export function QRCheckin({
  accountId,
  programId,
  programName,
  sessionDate,
  onComplete,
}: QRCheckinProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [qrSession, setQrSession] = useState<{
    sessionId: string;
    qrCode: string;
    qrCodeImage: string;
    expiresAt: string;
    enrolledCount: number;
    currentCheckins: number;
  } | null>(null);

  // QR Session Settings
  const [expiresInMinutes, setExpiresInMinutes] = useState(60);
  const [maxCheckins, setMaxCheckins] = useState(50);
  const [checkinWindowMinutes, setCheckinWindowMinutes] = useState(30);
  const [locationRequired, setLocationRequired] = useState(false);

  const generateQRCodeImage = useCallback(async (qrCode: string) => {
    try {
      const qrCodeImage = await QRCodeLib.toDataURL(qrCode, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      });
      return qrCodeImage;
    } catch (error) {
      console.error('Failed to generate QR code image:', error);
      return '';
    }
  }, []);

  const handleCreateQRSession = useCallback(async () => {
    try {
      setIsCreating(true);

      const sessionResult = await createQRCheckinSession({
        accountId,
        programId,
        sessionDate,
        sessionTime: new Date().toTimeString().split(' ')[0],
        expiresInMinutes,
        maxCheckins,
        checkinWindowMinutes,
      });

      const qrCodeImage = await generateQRCodeImage(sessionResult.qrCode);

      setQrSession({
        sessionId: sessionResult.sessionId,
        qrCode: sessionResult.qrCode,
        qrCodeImage,
        expiresAt: sessionResult.expiresAt,
        enrolledCount: sessionResult.enrolledCount,
        currentCheckins: 0,
      });

      toast.success('Tạo phiên điểm danh QR thành công!');

      onComplete?.({
        checkins: 0,
        qrCode: sessionResult.qrCode,
      });

    } catch (error) {
      console.error('QR session creation error:', error);
      toast.error('Không thể tạo phiên điểm danh QR');
    } finally {
      setIsCreating(false);
    }
  }, [
    accountId,
    programId,
    sessionDate,
    expiresInMinutes,
    maxCheckins,
    checkinWindowMinutes,
    generateQRCodeImage,
    onComplete,
  ]);

  const handleCopyQRCode = useCallback(() => {
    if (qrSession?.qrCode) {
      navigator.clipboard.writeText(qrSession.qrCode);
      toast.success('Đã copy mã QR!');
    }
  }, [qrSession?.qrCode]);

  const handleStopSession = useCallback(async () => {
    if (qrSession?.sessionId) {
      try {
        await updateQRSessionStatus(qrSession.sessionId, 'completed');
        setQrSession(null);
        toast.success('Đã dừng phiên điểm danh');
      } catch (error) {
        console.error('Failed to stop session:', error);
        toast.error('Không thể dừng phiên điểm danh');
      }
    }
  }, [qrSession?.sessionId]);

  // Auto-refresh session status (simulate real-time updates)
  useEffect(() => {
    if (!qrSession) return;

    const interval = setInterval(() => {
      // In real app, fetch current checkins from server
      const randomCheckins = Math.floor(Math.random() * 3);
      if (randomCheckins > 0) {
        setQrSession(prev => prev ? {
          ...prev,
          currentCheckins: Math.min(prev.currentCheckins + randomCheckins, prev.enrolledCount)
        } : null);
      }
    }, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, [qrSession]);

  const timeRemaining = qrSession ? 
    Math.max(0, Math.floor((new Date(qrSession.expiresAt).getTime() - Date.now()) / 1000 / 60)) : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <QrCode className="h-5 w-5" />
            <span>Điểm danh QR Code</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>📚 Chương trình: <span className="font-medium">{programName}</span></p>
            <p>📅 Ngày học: <span className="font-medium">{new Date(sessionDate).toLocaleDateString('vi-VN')}</span></p>
            <p>🎯 Phương thức: <Badge variant="secondary">QR Code Self Check-in</Badge></p>
          </div>
        </CardContent>
      </Card>

      {/* QR Session Settings */}
      {!qrSession && (
        <Card>
          <CardHeader>
            <CardTitle>Cài đặt phiên điểm danh</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="expires">Thời gian hết hạn (phút)</Label>
                <Input
                  id="expires"
                  type="number"
                  value={expiresInMinutes}
                  onChange={(e) => setExpiresInMinutes(parseInt(e.target.value) || 60)}
                  min={5}
                  max={480}
                />
              </div>
              <div>
                <Label htmlFor="maxCheckins">Số lượng tối đa</Label>
                <Input
                  id="maxCheckins"
                  type="number"
                  value={maxCheckins}
                  onChange={(e) => setMaxCheckins(parseInt(e.target.value) || 50)}
                  min={1}
                  max={200}
                />
              </div>
              <div>
                <Label htmlFor="window">Cửa sổ thời gian (phút)</Label>
                <Input
                  id="window"
                  type="number"
                  value={checkinWindowMinutes}
                  onChange={(e) => setCheckinWindowMinutes(parseInt(e.target.value) || 30)}
                  min={5}
                  max={120}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="location"
                checked={locationRequired}
                onCheckedChange={setLocationRequired}
              />
              <Label htmlFor="location">Yêu cầu vị trí GPS</Label>
            </div>

            <Button 
              onClick={handleCreateQRSession}
              disabled={isCreating}
              className="w-full"
            >
              {isCreating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Đang tạo...
                </>
              ) : (
                <>
                  <QrCode className="h-4 w-4 mr-2" />
                  Tạo phiên điểm danh QR
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Active QR Session */}
      {qrSession && (
        <div className="space-y-6">
          {/* QR Code Display */}
          <Card>
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  {qrSession.qrCodeImage ? (
                    <img 
                      src={qrSession.qrCodeImage} 
                      alt="QR Code" 
                      className="border rounded-lg shadow-lg"
                    />
                  ) : (
                    <div className="w-[300px] h-[300px] bg-gray-100 border rounded-lg flex items-center justify-center">
                      <QrCode className="h-16 w-16 text-gray-400" />
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Quét mã QR để điểm danh</h3>
                  <p className="text-sm text-muted-foreground">
                    Học sinh hoặc phụ huynh có thể quét mã này để điểm danh
                  </p>
                </div>

                <div className="flex space-x-2 justify-center">
                  <Button variant="outline" onClick={handleCopyQRCode}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy mã
                  </Button>
                  <Button variant="destructive" onClick={handleStopSession}>
                    Dừng phiên
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Session Status */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-blue-900">
                  {qrSession.currentCheckins}/{qrSession.enrolledCount}
                </div>
                <div className="text-sm text-blue-700">Đã điểm danh</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-orange-900">
                  {timeRemaining}
                </div>
                <div className="text-sm text-orange-700">Phút còn lại</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-green-900">
                  {Math.round((qrSession.currentCheckins / qrSession.enrolledCount) * 100) || 0}%
                </div>
                <div className="text-sm text-green-700">Tỷ lệ tham gia</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                {timeRemaining > 5 ? (
                  <>
                    <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-sm font-medium text-green-900">Đang hoạt động</div>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                    <div className="text-sm font-medium text-red-900">Sắp hết hạn</div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Session Info */}
          <Card>
            <CardHeader>
              <CardTitle>Thông tin phiên</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Mã phiên:</span> {qrSession.sessionId.substring(0, 8)}...
                </div>
                <div>
                  <span className="font-medium">Hết hạn lúc:</span> {new Date(qrSession.expiresAt).toLocaleString('vi-VN')}
                </div>
                <div>
                  <span className="font-medium">Số lượng tối đa:</span> {maxCheckins} học sinh
                </div>
                <div>
                  <span className="font-medium">Cửa sổ thời gian:</span> ±{checkinWindowMinutes} phút
                </div>
                {locationRequired && (
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span className="font-medium">Yêu cầu vị trí GPS</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
