'use client';

import { useState, useCallback, useRef } from 'react';
import { Camera, Upload, Users, CheckCircle, AlertCircle, Clock, Image } from 'lucide-react';
import { toast } from 'sonner';

import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';

import {
  createPhotoAttendanceSession,
  updatePhotoSessionStatus,
  type CreatePhotoSessionParams,
} from '../_lib/server/create-photo-session';

interface PhotoAttendanceProps {
  accountId: string;
  programId: string;
  programName: string;
  sessionDate: string;
  onComplete?: (results: { recognized: number; total: number }) => void;
}

export function PhotoAttendance({
  accountId,
  programId,
  programName,
  sessionDate,
  onComplete,
}: PhotoAttendanceProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingStep, setProcessingStep] = useState<string>('');
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [recognitionResults, setRecognitionResults] = useState<{
    totalFaces: number;
    recognizedStudents: number;
    confidence: number;
  } | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  const handlePhotoUpload = useCallback(async (file: File) => {
    try {
      setIsProcessing(true);
      setUploadProgress(0);
      setProcessingStep('Tạo phiên điểm danh...');

      // Step 1: Create photo attendance session
      const sessionResult = await createPhotoAttendanceSession({
        accountId,
        programId,
        sessionDate,
        sessionTime: new Date().toTimeString().split(' ')[0],
      });

      setSessionId(sessionResult.sessionId);
      setUploadProgress(20);
      setProcessingStep('Đang tải ảnh lên...');

      // Step 2: Upload photo to storage (simulate)
      await new Promise(resolve => setTimeout(resolve, 1000));
      const photoUrl = URL.createObjectURL(file); // In real app, upload to Supabase Storage
      setUploadProgress(40);

      // Step 3: Update session with photo URL
      await updatePhotoSessionStatus(
        sessionResult.sessionId,
        'processing',
        undefined,
        undefined,
        { batch_photo_url: photoUrl }
      );
      setUploadProgress(60);
      setProcessingStep('Đang phân tích khuôn mặt...');

      // Step 4: Simulate face detection and recognition
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate recognition results
      const mockResults = {
        totalFaces: Math.floor(Math.random() * 5) + 3, // 3-7 faces
        recognizedStudents: Math.floor(Math.random() * 3) + 2, // 2-4 recognized
        confidence: 0.85 + Math.random() * 0.1, // 85-95% confidence
      };

      setUploadProgress(80);
      setProcessingStep('Hoàn tất nhận diện...');

      // Step 5: Update session with results
      await updatePhotoSessionStatus(
        sessionResult.sessionId,
        'completed',
        mockResults.totalFaces,
        mockResults.recognizedStudents,
        {
          batch_photo_url: photoUrl,
          processing_completed_at: new Date().toISOString(),
          confidence_average: mockResults.confidence,
        }
      );

      setUploadProgress(100);
      setRecognitionResults(mockResults);
      setProcessingStep('Hoàn thành!');

      toast.success(
        `Nhận diện thành công ${mockResults.recognizedStudents}/${mockResults.totalFaces} học sinh`
      );

      onComplete?.({
        recognized: mockResults.recognizedStudents,
        total: mockResults.totalFaces,
      });

    } catch (error) {
      console.error('Photo attendance error:', error);
      toast.error('Không thể xử lý ảnh điểm danh');
      
      if (sessionId) {
        await updatePhotoSessionStatus(sessionId, 'failed');
      }
    } finally {
      setIsProcessing(false);
    }
  }, [accountId, programId, sessionDate, sessionId, onComplete]);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        handlePhotoUpload(file);
      } else {
        toast.error('Vui lòng chọn file ảnh');
      }
    }
  }, [handlePhotoUpload]);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const openCamera = useCallback(() => {
    cameraInputRef.current?.click();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Camera className="h-5 w-5" />
            <span>Điểm danh bằng ảnh</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>📚 Chương trình: <span className="font-medium">{programName}</span></p>
            <p>📅 Ngày học: <span className="font-medium">{new Date(sessionDate).toLocaleDateString('vi-VN')}</span></p>
            <p>🎯 Phương thức: <Badge variant="secondary">AI Face Recognition</Badge></p>
          </div>
        </CardContent>
      </Card>

      {/* Upload Options */}
      {!isProcessing && !recognitionResults && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={openCamera}>
            <CardContent className="p-6 text-center">
              <Camera className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Chụp ảnh ngay</h3>
              <p className="text-sm text-muted-foreground">
                Sử dụng camera để chụp ảnh lớp học
              </p>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={openFileDialog}>
            <CardContent className="p-6 text-center">
              <Upload className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Tải ảnh lên</h3>
              <p className="text-sm text-muted-foreground">
                Chọn ảnh từ thiết bị của bạn
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Processing Status */}
      {isProcessing && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-blue-600 animate-spin" />
                <span className="font-medium">Đang xử lý ảnh điểm danh...</span>
              </div>
              
              <Progress value={uploadProgress} className="w-full" />
              
              <div className="text-sm text-muted-foreground">
                {processingStep}
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Quy trình xử lý:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>1. Tạo phiên điểm danh</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>2. Tải ảnh lên hệ thống</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 animate-spin" />
                    <span>3. Phân tích và nhận diện khuôn mặt</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-gray-400" />
                    <span>4. Tạo kết quả điểm danh</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {recognitionResults && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-900">Điểm danh hoàn thành!</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-900">
                    {recognitionResults.totalFaces}
                  </div>
                  <div className="text-sm text-blue-700">Khuôn mặt phát hiện</div>
                </div>

                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-900">
                    {recognitionResults.recognizedStudents}
                  </div>
                  <div className="text-sm text-green-700">Học sinh nhận diện</div>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <Image className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-900">
                    {Math.round(recognitionResults.confidence * 100)}%
                  </div>
                  <div className="text-sm text-purple-700">Độ tin cậy</div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button 
                  onClick={() => {
                    setRecognitionResults(null);
                    setUploadProgress(0);
                    setProcessingStep('');
                  }}
                  variant="outline"
                >
                  Điểm danh lại
                </Button>
                <Button>
                  Xem chi tiết
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleFileSelect}
        className="hidden"
      />
    </div>
  );
}
