'use client';

import { useEffect, useState } from 'react';

import {
  BarChart3,
  Calendar,
  Camera,
  Plus,
  QrC<PERSON>,
  TrendingUp,
  UserCheck,
  UserX,
  Users,
} from 'lucide-react';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

import {
  type AttendanceSession,
  type AttendanceStats,
  getAttendanceSessions,
  getAttendanceStats,
} from '../_lib/server';
import { AnalyticsDashboard } from './analytics-dashboard';
import { PhotoAttendance } from './photo-attendance';
import { QRCheckin } from './qr-checkin';
import { QuickAttendance } from './quick-attendance';

interface AttendanceManagementProps {
  account: string;
}

export function AttendanceManagement({ account }: AttendanceManagementProps) {
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [programs, setPrograms] = useState<
    Array<{ id: string; name: string; program_type: string }>
  >([]);
  const [selectedProgramId, setSelectedProgramId] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0],
  );
  const [attendanceSessions, setAttendanceSessions] = useState<
    AttendanceSession[]
  >([]);
  const [attendanceStats, setAttendanceStats] =
    useState<AttendanceStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('quick');

  const currentAccount = accounts?.find((acc) => acc.slug === account);

  // Load programs
  useEffect(() => {
    if (currentAccount?.id) {
      loadPrograms();
    }
  }, [currentAccount?.id]);

  // Load data when program changes
  useEffect(() => {
    if (selectedProgramId && currentAccount?.id) {
      loadAttendanceData();
    }
  }, [selectedProgramId, selectedDate, currentAccount?.id]);

  const loadPrograms = async () => {
    try {
      const { data, error } = await supabase
        .from('programs')
        .select('id, name, program_type')
        .eq('account_id', currentAccount!.id)
        .eq('status', 'active')
        .order('name');

      if (error) throw error;

      setPrograms(data || []);
      if (data && data.length > 0 && !selectedProgramId) {
        setSelectedProgramId(data[0].id);
      }
    } catch (error) {
      console.error('Failed to load programs:', error);
      toast.error('Không thể tải danh sách chương trình');
    }
  };

  const loadAttendanceData = async () => {
    if (!selectedProgramId || !currentAccount?.id) return;

    try {
      setIsLoading(true);

      // Load attendance sessions
      const sessions = await getAttendanceSessions(
        currentAccount.id,
        selectedProgramId,
        10,
      );
      setAttendanceSessions(sessions);

      // Load attendance stats
      const stats = await getAttendanceStats(
        currentAccount.id,
        selectedProgramId,
        new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split('T')[0], // Last 30 days
        new Date().toISOString().split('T')[0],
      );
      setAttendanceStats(stats);
    } catch (error) {
      console.error('Failed to load attendance data:', error);
      toast.error('Không thể tải dữ liệu điểm danh');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAttendanceSaved = (stats: {
    presentCount: number;
    absentCount: number;
    lateCount: number;
  }) => {
    toast.success(
      `Đã lưu điểm danh: ${stats.presentCount} có mặt, ${stats.absentCount} vắng, ${stats.lateCount} muộn`,
    );
    loadAttendanceData(); // Reload data to update stats
  };

  const selectedProgram = programs.find((p) => p.id === selectedProgramId);

  if (programs.length === 0 && !isLoading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Users className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
          <h3 className="mb-2 text-lg font-semibold">
            Chưa có chương trình học
          </h3>
          <p className="text-muted-foreground mb-4">
            Bạn cần tạo chương trình học trước khi có thể điểm danh
          </p>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Tạo chương trình học
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <UserCheck className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Quản lý điểm danh</h1>
                  <p className="text-white/90 text-lg">Hệ thống điểm danh thông minh và hiện đại</p>
                </div>
              </div>

              {/* Quick Stats */}
              {attendanceStats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                  <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5" />
                      <div>
                        <div className="text-2xl font-bold">{attendanceStats.total_sessions}</div>
                        <div className="text-white/80 text-sm">Tổng buổi học</div>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center gap-3">
                      <UserCheck className="h-5 w-5" />
                      <div>
                        <div className="text-2xl font-bold">{attendanceStats.present_count}</div>
                        <div className="text-white/80 text-sm">Có mặt</div>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center gap-3">
                      <UserX className="h-5 w-5" />
                      <div>
                        <div className="text-2xl font-bold">{attendanceStats.absent_count}</div>
                        <div className="text-white/80 text-sm">Vắng mặt</div>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center gap-3">
                      <TrendingUp className="h-5 w-5" />
                      <div>
                        <div className="text-2xl font-bold">{attendanceStats.average_attendance_rate.toFixed(1)}%</div>
                        <div className="text-white/80 text-sm">Tỷ lệ có mặt</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Enhanced Program Selection */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
            </div>
            Chọn chương trình và ngày điểm danh
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                Chương trình học
              </label>
              <Select
                value={selectedProgramId}
                onValueChange={setSelectedProgramId}
              >
                <SelectTrigger className="h-12 bg-white border-2 border-gray-200 hover:border-blue-300 transition-colors">
                  <SelectValue placeholder="Chọn chương trình học" />
                </SelectTrigger>
                <SelectContent>
                  {programs.map((program) => (
                    <SelectItem key={program.id} value={program.id}>
                      <div className="flex items-center gap-2">
                        <span className="text-blue-600">📚</span>
                        <span>{program.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {program.program_type}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                Ngày học
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="h-12 bg-white border-2 border-gray-200 hover:border-blue-300 transition-colors flex w-full rounded-md px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Main Content */}
      {selectedProgramId && currentAccount && (
        <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-green-100 rounded-lg">
                <UserCheck className="h-5 w-5 text-green-600" />
              </div>
              Phương thức điểm danh
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4 h-14 bg-gray-100 p-1 rounded-xl">
                <TabsTrigger
                  value="quick"
                  className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                >
                  <UserCheck className="h-4 w-4" />
                  <span className="hidden sm:inline">Điểm danh nhanh</span>
                  <span className="sm:hidden">Nhanh</span>
                </TabsTrigger>
                <TabsTrigger
                  value="photo"
                  className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                >
                  <Camera className="h-4 w-4" />
                  <span className="hidden sm:inline">Điểm danh ảnh</span>
                  <span className="sm:hidden">Ảnh</span>
                </TabsTrigger>
                <TabsTrigger
                  value="qr"
                  className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                >
                  <QrCode className="h-4 w-4" />
                  <span className="hidden sm:inline">QR Check-in</span>
                  <span className="sm:hidden">QR</span>
                </TabsTrigger>
                <TabsTrigger
                  value="analytics"
                  className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                >
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Phân tích</span>
                  <span className="sm:hidden">Phân tích</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="quick" className="mt-8">
                <div className="p-6 bg-blue-50 rounded-xl border border-blue-100">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <UserCheck className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-blue-900 font-semibold text-lg">Điểm danh nhanh</h3>
                      <p className="text-blue-700 text-sm">Điểm danh từng học viên một cách nhanh chóng</p>
                    </div>
                  </div>
                  <QuickAttendance
                    accountId={currentAccount.id}
                    programId={selectedProgramId}
                    programName={selectedProgram?.name || ''}
                    sessionDate={selectedDate}
                    onSave={handleAttendanceSaved}
                  />
                </div>
              </TabsContent>

              <TabsContent value="photo" className="mt-8">
                <div className="p-6 bg-purple-50 rounded-xl border border-purple-100">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Camera className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="text-purple-900 font-semibold text-lg">Điểm danh bằng ảnh</h3>
                      <p className="text-purple-700 text-sm">Sử dụng AI để nhận diện khuôn mặt và điểm danh tự động</p>
                    </div>
                  </div>
                  <PhotoAttendance
                    accountId={currentAccount.id}
                    programId={selectedProgramId}
                    programName={selectedProgram?.name || ''}
                    sessionDate={selectedDate}
                    onComplete={(results) => {
                      toast.success(
                        `Điểm danh ảnh: ${results.recognized}/${results.total} học sinh`,
                      );
                      loadAttendanceData(); // Reload data
                    }}
                  />
                </div>
              </TabsContent>

              <TabsContent value="qr" className="mt-8">
                <div className="p-6 bg-green-50 rounded-xl border border-green-100">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <QrCode className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-green-900 font-semibold text-lg">QR Check-in</h3>
                      <p className="text-green-700 text-sm">Học viên tự điểm danh bằng mã QR</p>
                    </div>
                  </div>
                  <QRCheckin
                    accountId={currentAccount.id}
                    programId={selectedProgramId}
                    programName={selectedProgram?.name || ''}
                    sessionDate={selectedDate}
                    onComplete={(results) => {
                      toast.success(
                        `QR Check-in: ${results.checkins} học sinh đã điểm danh`,
                      );
                      loadAttendanceData(); // Reload data
                    }}
                  />
                </div>
              </TabsContent>

              <TabsContent value="analytics" className="mt-8">
                <div className="p-6 bg-orange-50 rounded-xl border border-orange-100">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="text-orange-900 font-semibold text-lg">Phân tích điểm danh</h3>
                      <p className="text-orange-700 text-sm">Báo cáo và thống kê chi tiết về điểm danh</p>
                    </div>
                  </div>
                  <AnalyticsDashboard
                    accountId={currentAccount.id}
                    programId={selectedProgramId}
                    programName={selectedProgram?.name}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
