import { redirect } from 'next/navigation';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import { TeamAccountLayoutPageHeader } from '../../../../_components/team-account-layout-page-header';
import EditProgramForm from './_components/edit-program-form';

interface Props {
  params: Promise<{
    account: string;
    programId: string;
  }>;
}

export const metadata = {
  title: 'Chỉnh S<PERSON>a <PERSON> Trình',
};

async function EditProgramPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-36">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:programs.edit.title')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/programs`}
            >
              {i18n.t('education:breadcrumbs.programs')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/programs/${resolvedParams.programId}`}
            >
              {i18n.t('education:programs.details.title')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:programs.edit.title')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-4xl">
        <EditProgramForm
          account={resolvedParams.account}
          programId={resolvedParams.programId}
        />
      </div>
    </div>
  );
}

export default EditProgramPage;
