import { redirect } from 'next/navigation';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import { TeamAccountLayoutPageHeader } from '../../../_components/team-account-layout-page-header';
import ProgramDetails from './_components/program-details';

interface Props {
  params: Promise<{
    account: string;
    programId: string;
  }>;
}

export const metadata = {
  title: 'Chi Tiết Chương Trình',
};

async function ProgramDetailsPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-8">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:programs.details.title')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/programs`}
            >
              {i18n.t('education:breadcrumbs.programs')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:programs.details.title')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-6xl px-4">
        <ProgramDetails
          account={resolvedParams.account}
          programId={resolvedParams.programId}
        />
      </div>
    </div>
  );
}

export default ProgramDetailsPage;
