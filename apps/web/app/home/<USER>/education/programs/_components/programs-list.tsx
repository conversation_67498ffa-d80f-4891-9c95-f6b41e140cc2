'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import {
  Clock,
  DollarSign,
  Eye,
  Search,
  Edit,
  Plus,
  Users,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';

interface Props {
  account: string;
}

interface Program {
  id: string;
  name: string;
  program_type: string;
  capacity: number;
  description: string;
  location: string;
  status: string;
  tuition_fee: number;
  schedule: {
    days: string[];
    start_time: string;
    end_time: string;
  };
  created_at: string;
  updated_at: string;
  account_id: string;
  _count?: {
    enrollments: number;
  };
}

export function ProgramsList({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [programs, setPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Get current organization
  const currentAccount = accounts?.find((acc) => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;

    loadPrograms();
  }, [currentAccount?.id, supabase]);

  const loadPrograms = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setPrograms([]);
        return;
      }

      // Get programs with enrollment count
      const { data: programsData, error: programsError } = await supabase
        .from('programs')
        .select(
          `
          *,
          enrollments(id)
        `,
        )
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (programsError) throw programsError;

      // Process the data to include enrollment count
      const processedPrograms =
        programsData?.map((program) => ({
          ...program,
          _count: {
            enrollments: program.enrollments?.length || 0,
          },
        })) || [];

      setPrograms(processedPrograms);
    } catch (err: any) {
      console.error('Error loading programs:', err);
      setError(err.message || 'Failed to load programs');
    } finally {
      setLoading(false);
    }
  };

  const filteredPrograms = programs.filter((program) => {
    const matchesSearch =
      program.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      program.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || program.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: {
        label: t('programs.status.active', 'Active'),
        variant: 'default' as const,
      },
      inactive: {
        label: t('programs.status.inactive', 'Inactive'),
        variant: 'secondary' as const,
      },
      archived: {
        label: t('programs.status.archived', 'Archived'),
        variant: 'outline' as const,
      },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || {
      label: status,
      variant: 'secondary' as const,
    };

    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const getProgramTypeText = (type: string) => {
    return t(`programType.${type}`, type);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatSchedule = (schedule: any) => {
    if (!schedule) return 'Chưa có lịch học';

    // Handle string format
    if (typeof schedule === 'string') {
      return schedule;
    }

    // Handle legacy format: {"schedule": "Thứ 2 - Thứ 6, 7:30 - 16:30"}
    if (schedule.schedule && typeof schedule.schedule === 'string') {
      return schedule.schedule;
    }

    // Handle new format: {"days": [...], "start_time": "07:30", "end_time": "16:30"}
    if (schedule.days && Array.isArray(schedule.days)) {
      const days = schedule.days
        .map((day: string) => {
          const dayMap: Record<string, string> = {
            monday: 'T2',
            tuesday: 'T3',
            wednesday: 'T4',
            thursday: 'T5',
            friday: 'T6',
            saturday: 'T7',
            sunday: 'CN',
          };
          return dayMap[day] || day;
        })
        .join(', ');

      // Check for time information
      if (schedule.start_time && schedule.end_time) {
        return `${days} (${schedule.start_time} - ${schedule.end_time})`;
      } else if (schedule.time) {
        return `${days} (${schedule.time})`;
      } else {
        return `${days} (Chưa xác định giờ)`;
      }
    }

    // Handle simple time format
    if (schedule.start_time && schedule.end_time) {
      return `${schedule.start_time} - ${schedule.end_time}`;
    }

    return 'Chưa có lịch học';
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">{t('common.loading')}</div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="mb-4 text-red-600">{error}</p>
        <Button onClick={loadPrograms} variant="outline">
          {t('common.refresh')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            <Input
              placeholder={t(
                'programs.searchPlaceholder',
                'Search programs...',
              )}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 pl-10"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="rounded-md border border-gray-300 px-3 py-2 text-sm"
          >
            <option value="all">
              {t('programs.filters.allStatuses', 'All Statuses')}
            </option>
            <option value="active">
              {t('programs.filters.active', 'Active')}
            </option>
            <option value="inactive">
              {t('programs.filters.inactive', 'Inactive')}
            </option>
            <option value="archived">
              {t('programs.filters.archived', 'Archived')}
            </option>
          </select>
        </div>

        <Button asChild>
          <Link href={`/home/<USER>/education/programs/new`}>
            <Plus className="mr-2 h-4 w-4" />
            {t('programs.addProgram', 'Add Program')}
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('programs.stats.total', 'Total Programs')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{programs.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('programs.stats.active', 'Active')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {programs.filter((p) => p.status === 'active').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('programs.stats.totalEnrollments', 'Total Enrollments')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {programs.reduce(
                (sum, p) => sum + (p._count?.enrollments || 0),
                0,
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {t('programs.stats.avgCapacity', 'Avg Capacity')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {programs.length > 0
                ? Math.round(
                    programs.reduce((sum, p) => sum + p.capacity, 0) /
                      programs.length,
                  )
                : 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Programs Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
        {filteredPrograms.map((program) => (
          <Card key={program.id} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-md bg-gradient-to-br from-white to-gray-50/50 overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>

            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                    {program.name}
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge
                      variant="outline"
                      className="text-xs font-medium bg-blue-50 text-blue-700 border-blue-200"
                    >
                      {program.program_type === 'nursery' && '🍼 Nhà trẻ'}
                      {program.program_type === 'preschool' && '🎨 Mẫu giáo'}
                      {program.program_type === 'kindergarten' && '🌟 Mầm non'}
                      {program.program_type === 'primary' && '📚 Tiểu học'}
                      {program.program_type === 'secondary' && '🎓 Trung học'}
                    </Badge>
                    <Badge
                      variant={program.status === 'active' ? 'default' : 'secondary'}
                      className={program.status === 'active'
                        ? 'bg-green-100 text-green-800 border-green-200'
                        : 'bg-gray-100 text-gray-600 border-gray-200'
                      }
                    >
                      {program.status === 'active' ? '✅ Hoạt động' : '⏸️ Tạm dừng'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600 line-clamp-3 leading-relaxed">
                {program.description || 'Chưa có mô tả chương trình'}
              </p>

              <div className="space-y-3">
                {/* Enrollment Info */}
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="flex items-center text-sm text-blue-700">
                    <Users className="mr-2 h-4 w-4" />
                    <span className="font-medium">Học viên</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-900">
                      {program._count?.enrollments || 0}/{program.capacity}
                    </div>
                    <div className="text-xs text-blue-600">đã đăng ký</div>
                  </div>
                </div>

                {/* Schedule Info */}
                <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg border border-purple-100">
                  <Clock className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-purple-900 mb-1">Lịch học</div>
                    <div className="text-sm text-purple-700 leading-relaxed">
                      {formatSchedule(program.schedule)}
                    </div>
                  </div>
                </div>

                {/* Fee Info */}
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100">
                  <div className="flex items-center text-sm text-green-700">
                    <DollarSign className="mr-2 h-4 w-4" />
                    <span className="font-medium">Học phí</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-900">
                      {program.fee_structure?.tuition_fee
                        ? formatCurrency(program.fee_structure.tuition_fee)
                        : program.tuition_fee
                        ? formatCurrency(program.tuition_fee)
                        : 'Liên hệ'
                      }
                    </div>
                    <div className="text-xs text-green-600">/ tháng</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex-1 bg-white hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 transition-all duration-200"
                >
                  <Link href={`/home/<USER>/education/programs/${program.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    Chi tiết
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex-1 bg-white hover:bg-purple-50 hover:text-purple-600 hover:border-purple-300 transition-all duration-200"
                >
                  <Link href={`/home/<USER>/education/programs/${program.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Sửa
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPrograms.length === 0 && (
        <div className="py-12 text-center">
          <div className="mb-4 text-gray-500">
            {searchTerm || statusFilter !== 'all'
              ? t(
                  'programs.emptyState.noResults',
                  'No programs found matching the filters.',
                )
              : t(
                  'programs.emptyState.noPrograms',
                  'No programs yet. Add your first program!',
                )}
          </div>
          {!searchTerm && statusFilter === 'all' && (
            <Button asChild>
              <Link href={`/home/<USER>/education/programs/new`}>
                <Plus className="mr-2 h-4 w-4" />
                {t('programs.addProgram', 'Add Program')}
              </Link>
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
