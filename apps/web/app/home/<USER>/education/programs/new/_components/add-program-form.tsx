'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Checkbox } from '@kit/ui/checkbox';
import { toast } from '@kit/ui/sonner';
import { Save, ArrowLeft } from 'lucide-react';

interface Props {
  account: string;
}

const programSchema = z.object({
  name: z.string().min(1, 'Program name is required'),
  program_type: z.enum(['early_childhood', 'preschool', 'kindergarten', 'extracurricular', 'special_needs']),
  description: z.string().min(1, 'Description is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  location: z.string().min(1, 'Location is required'),
  tuition_fee: z.number().min(0, 'Tuition fee must be positive'),
  start_time: z.string().min(1, 'Start time is required'),
  end_time: z.string().min(1, 'End time is required'),
  days: z.array(z.string()).min(1, 'At least one day must be selected'),
  age_group: z.string().min(1, 'Age group is required'),
});

type ProgramFormData = z.infer<typeof programSchema>;

const DAYS_OF_WEEK = [
  { value: 'monday', label: 'Monday' },
  { value: 'tuesday', label: 'Tuesday' },
  { value: 'wednesday', label: 'Wednesday' },
  { value: 'thursday', label: 'Thursday' },
  { value: 'friday', label: 'Friday' },
  { value: 'saturday', label: 'Saturday' },
  { value: 'sunday', label: 'Sunday' },
];

export function AddProgramForm({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [loading, setLoading] = useState(false);

  const currentAccount = accounts?.find(acc => acc.slug === account);

  const form = useForm<ProgramFormData>({
    resolver: zodResolver(programSchema),
    defaultValues: {
      name: '',
      program_type: 'early_childhood',
      description: '',
      capacity: 15,
      location: '',
      tuition_fee: 0,
      start_time: '08:00',
      end_time: '16:00',
      days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      age_group: '2-3',
    },
  });

  const onSubmit = async (data: ProgramFormData) => {
    if (!currentAccount?.id) {
      toast.error('Account not found');
      return;
    }

    try {
      setLoading(true);

      // Use account directly
      const accountId = currentAccount.id;

      // Create program
      const { error: programError } = await supabase
        .from('programs')
        .insert({
          name: data.name,
          program_type: data.program_type,
          description: data.description,
          capacity: data.capacity,
          age_group: data.age_group, // Use age_group column
          status: 'active',
          account_id: accountId,
          schedule: {
            days: data.days,
            start_time: data.start_time,
            end_time: data.end_time,
            location: data.location, // Store location in schedule JSONB
            tuition_fee: data.tuition_fee, // Store tuition_fee in schedule JSONB
          },
        });

      if (programError) throw programError;

      toast.success(t('programs.addSuccess', 'Program added successfully'));
      router.push(`/home/<USER>/education/programs`);
    } catch (error: any) {
      console.error('Error adding program:', error);
      toast.error(error.message || 'Failed to add program');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back')}</span>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('programs.form.basicInfo', 'Basic Information')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.name', 'Program Name')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Lớp Mầm (2-3 tuổi)" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="program_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.type', 'Program Type')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select program type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="early_childhood">{t('programType.early_childhood')}</SelectItem>
                          <SelectItem value="preschool">{t('programType.preschool')}</SelectItem>
                          <SelectItem value="kindergarten">{t('programType.kindergarten')}</SelectItem>
                          <SelectItem value="extracurricular">{t('programType.extracurricular')}</SelectItem>
                          <SelectItem value="special_needs">{t('programType.special_needs')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="capacity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.capacity', 'Capacity')}</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="15" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.location', 'Location')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Phòng A1" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tuition_fee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.tuitionFee', 'Tuition Fee (VND)')}</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="3500000" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="age_group"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.ageGroup', 'Age Group')}</FormLabel>
                      <FormControl>
                        <Input placeholder="2-3" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('programs.form.description', 'Description')}</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Chương trình giáo dục cho trẻ 2-3 tuổi với phương pháp Montessori" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Schedule Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('programs.form.schedule', 'Schedule')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="start_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.startTime', 'Start Time')}</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="end_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('programs.form.endTime', 'End Time')}</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="days"
                render={() => (
                  <FormItem>
                    <FormLabel>{t('programs.form.days', 'Days of Week')}</FormLabel>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {DAYS_OF_WEEK.map((day) => (
                        <FormField
                          key={day.value}
                          control={form.control}
                          name="days"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={day.value}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(day.value)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, day.value])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== day.value
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {t(`programs.days.${day.value}`, day.label)}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              {t('common.cancel')}
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? t('common.saving', 'Saving...') : t('common.save')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
