import { Suspense } from 'react';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { EditVehicleForm } from './_components/edit-vehicle-form';

interface Props {
  params: Promise<{
    account: string;
    vehicleId: string;
  }>;
}

async function EditVehiclePage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-8">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:transportation.vehicle.edit.title', 'Edit Vehicle')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/transportation`}
            >
              {i18n.t('education:breadcrumbs.transportation')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/transportation/vehicles`}
            >
              {i18n.t('education:transportation.vehicles.title')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/transportation/vehicles/${resolvedParams.vehicleId}`}
            >
              {i18n.t('education:transportation.vehicle.details.title')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:transportation.vehicle.edit.title')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-6xl px-4">
        <Suspense fallback={<div>Loading...</div>}>
          <EditVehicleForm 
            account={resolvedParams.account} 
            vehicleId={resolvedParams.vehicleId} 
          />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(EditVehiclePage);

export const metadata = {
  title: 'Edit Vehicle',
  description: 'Edit vehicle details and information',
};
