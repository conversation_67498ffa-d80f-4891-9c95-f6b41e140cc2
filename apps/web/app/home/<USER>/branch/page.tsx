import React, { Suspense } from 'react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { CardContent, CardHeader } from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { checkCanCreateResource } from '~/home/<USER>/_lib/server/resource-access';
import { BranchPageProps } from '~/home/<USER>/branch/_lib/branch.types';

import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';
import { BranchList } from './_components/branch-list';
import CreateBranchModal from './_components/create-branch-modal';
import { SearchAndFilterBar } from './_components/search-filter-bar';
import { loadBranches } from './_lib/server/branch.loader';

export default async function BranchPage({
  params,
  searchParams = {},
}: BranchPageProps) {
  const client = getSupabaseServerClient();

  const resolvedParams = await Promise.resolve(params);
  const resolvedSearchParams = await Promise.resolve(searchParams);

  const accountSlug = resolvedParams.account;
  const search = resolvedSearchParams.search;
  const filter = resolvedSearchParams.filter;

  const api = createTeamAccountsApi(client);

  try {
    const teamAccount = await api.getTeamAccount(accountSlug);

    if (!teamAccount) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const [branches, { account }] = await Promise.all([
      loadBranches(teamAccount.id, search, filter),
      loadTeamWorkspace(accountSlug),
    ]);

    if (!account) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    // Kiểm tra quyền đã được thực hiện ở sidebar menu
    // Nếu người dùng có thể truy cập trang này, họ đã có quyền
    const canManageBranches = true;

    // Kiểm tra giới hạn tạo chi nhánh
    const {
      canCreate,
      reason,
      current: resourceCurrent,
      limit: resourceLimit,
    } = await checkCanCreateResource(teamAccount.id, 'branches');

    return (
      <Suspense fallback={null}>
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="branch:title">Chi nhánh</Trans>}
          description={<AppBreadcrumbs />}
        />
        <PageBody data-testid="branches-page">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <SearchAndFilterBar
              initialSearch={search}
              initialFilter={filter}
              accountSlug={accountSlug}
            />

            <If condition={canManageBranches}>
              <CreateBranchModal
                accountId={teamAccount.id}
                accountSlug={accountSlug}
                canCreate={canCreate}
                reason={reason}
                resourceCurrent={resourceCurrent}
                resourceLimit={resourceLimit}
              />
            </If>
          </CardHeader>
          <CardContent>
            <BranchList
              branches={branches}
              accountId={teamAccount.id}
              accountSlug={accountSlug}
            />
          </CardContent>
        </PageBody>
      </Suspense>
    );
  } catch (error: any) {
    console.error('Error loading branches page:', {
      error: error.message,
      params,
      context: 'branches.page',
    });

    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:loadingFailed">
              Failed to load data. Please try again later.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}
