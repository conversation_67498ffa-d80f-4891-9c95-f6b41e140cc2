'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

interface DeleteCustomerProfileParams {
  profileId: string;
  accountId: string;
  accountSlug: string;
}

export async function deleteCustomerProfile({
  profileId,
  accountId,
  accountSlug,
}: DeleteCustomerProfileParams) {
  const client = getSupabaseServerClient();

  try {
    // Delete customer profile using the dedicated function
    const { data: deleted, error: deleteError } = await client.rpc(
      'delete_customer_profile_v2',
      {
        p_customer_id: profileId,
        p_team_account_id: accountId,
      },
    );

    if (deleteError) {
      throw deleteError;
    }

    if (!deleted) {
      throw new Error('Customer profile not found or access denied');
    }

    // Revalidate the CDP profiles page
    revalidatePath(`/home/<USER>/cdp/profiles`);
    revalidatePath(`/home/<USER>/cdp`);

    return { success: true };
  } catch (error) {
    console.error('Error deleting customer profile:', error);
    throw new Error(
      `Failed to delete customer profile: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
