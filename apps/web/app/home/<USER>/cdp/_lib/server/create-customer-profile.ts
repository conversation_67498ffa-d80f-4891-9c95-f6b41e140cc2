'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

interface CreateCustomerProfileParams {
  accountId: string;
  accountSlug: string;
  profileData: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    avatar_url?: string;
    metadata?: Record<string, any>;
  };
}

export async function createCustomerProfile({
  accountId,
  accountSlug,
  profileData,
}: CreateCustomerProfileParams) {
  const client = getSupabaseServerClient();

  try {
    // Check if email already exists in customer_profiles table
    const { data: existingCustomer } = await client
      .from('customer_profiles')
      .select('id')
      .eq('email', profileData.email)
      .eq('team_account_id', accountId)
      .single();

    if (existingCustomer) {
      throw new Error('Customer already exists in this team');
    }

    // Create new customer profile using the dedicated function
    const { data: newCustomerId, error: createError } = await client.rpc(
      'create_customer_profile_v2',
      {
        p_team_account_id: accountId,
        p_email: profileData.email,
        p_first_name: profileData.first_name,
        p_last_name: profileData.last_name,
        p_phone: profileData.phone,
        p_avatar_url: profileData.avatar_url,
        p_metadata: {
          source: 'manual_entry',
          ...profileData.metadata,
        },
      },
    );

    if (createError || !newCustomerId) {
      throw new Error(
        `Failed to create customer profile: ${createError?.message}`,
      );
    }

    revalidatePath(`/home/<USER>/cdp/profiles`);

    return {
      success: true,
      profileId: newCustomerId,
      message: 'Customer profile created successfully',
      type: 'new',
    };
  } catch (error) {
    console.error('Error creating customer profile:', error);
    throw new Error(
      `Failed to create customer profile: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}
