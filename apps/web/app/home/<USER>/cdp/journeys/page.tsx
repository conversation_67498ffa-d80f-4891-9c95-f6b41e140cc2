import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';

import { JourneysDashboard } from './_components/journeys-dashboard';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('cdp:journeys.title');

  return {
    title,
  };
};

interface JourneysPageProps {
  params: Promise<{
    account: string;
  }>;
}

async function JourneysPage({ params }: JourneysPageProps) {
  const { account: accountSlug } = await params;

  // Load team workspace data to get the actual account ID
  const { user, account } = await loadTeamWorkspace(accountSlug);

  return (
    <div className="flex flex-1 flex-col">
      <TeamAccountLayoutPageHeader
        account={accountSlug}
        title={<Trans i18nKey="cdp:journeys.title" />}
        description={
          <AppBreadcrumbs
            items={[
              {
                title: <Trans i18nKey="common:routes.home" />,
                url: `/home/<USER>
              },
              {
                title: <Trans i18nKey="cdp:title" />,
                url: `/home/<USER>/cdp`,
              },
              {
                title: <Trans i18nKey="cdp:journeys.title" />,
                url: `/home/<USER>/cdp/journeys`,
              },
            ]}
          />
        }
      />

      <div className="flex flex-1 flex-col space-y-6 p-6">
        <JourneysDashboard
          accountId={account.id}
          accountSlug={accountSlug}
          activeTab="all"
          statusFilter="all"
          user={user}
          account={account}
        />
      </div>
    </div>
  );
}

export default withI18n(JourneysPage);
