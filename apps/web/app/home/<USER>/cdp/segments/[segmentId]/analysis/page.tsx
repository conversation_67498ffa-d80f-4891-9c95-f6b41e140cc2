import { notFound } from 'next/navigation';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { SegmentAnalysisPage } from './_components/segment-analysis-page';

interface PageProps {
  params: Promise<{
    account: string;
    segmentId: string;
  }>;
}

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('cdp:analysis.title');

  return {
    title,
  };
};

async function SegmentAnalysisRoute({ params }: PageProps) {
  const client = getSupabaseServerClient();

  // Await params first
  const { account: accountSlug, segmentId } = await params;

  // Load team workspace data
  const { user, account } = await loadTeamWorkspace(accountSlug);

  // Get segment data
  const { data: segment, error: segmentError } = await client
    .from('customer_segments')
    .select('*')
    .eq('id', segmentId)
    .eq('account_id', account.id)
    .single();

  if (segmentError || !segment) {
    notFound();
  }

  // Get segment customers (from customer_profiles based on segment criteria)
  const { data: customers, error: customersError } = await client
    .from('customer_profiles')
    .select('*')
    .eq('team_account_id', account.id)
    .limit(100); // For now, get all customers - in real app, filter by segment criteria

  const segmentCustomers = customers || [];

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={accountSlug}
        title={
          <Trans
            i18nKey="cdp:analysis.title"
            values={{ segmentName: segment.name }}
          >
            {segment.name} - Analysis
          </Trans>
        }
        description={
          <AppBreadcrumbs
            items={[
              {
                title: <Trans i18nKey="common:routes.home" />,
                url: `/home/<USER>
              },
              {
                title: <Trans i18nKey="cdp:title" />,
                url: `/home/<USER>/cdp`,
              },
              {
                title: <Trans i18nKey="cdp:segments.title" />,
                url: `/home/<USER>/cdp/segments`,
              },
              {
                title: segment.name,
                url: '#',
              },
              {
                title: <Trans i18nKey="cdp:analysis.title" />,
              },
            ]}
          />
        }
      />

      <SegmentAnalysisPage
        account={account}
        accountSlug={accountSlug}
        segment={segment}
        customers={segmentCustomers}
      />
    </>
  );
}

export default withI18n(SegmentAnalysisRoute);
