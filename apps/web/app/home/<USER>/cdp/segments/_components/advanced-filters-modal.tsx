'use client';

import { useState } from 'react';

import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Switch } from '@kit/ui/switch';

interface AdvancedFiltersModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: AdvancedFilters) => void;
  currentFilters?: AdvancedFilters;
}

export interface AdvancedFilters {
  customerCountRange: [number, number];
  growthRateRange: [number, number];
  engagementRange: [number, number];
  valueRange: [number, number];
  isActive?: boolean;
  isAutoUpdating?: boolean;
  createdDateRange?: {
    from: string;
    to: string;
  };
  types: string[];
}

export function AdvancedFiltersModal({
  isOpen,
  onClose,
  onApply,
  currentFilters,
}: AdvancedFiltersModalProps) {
  const { t } = useTranslation(['cdp', 'common']);

  const [filters, setFilters] = useState<AdvancedFilters>(
    currentFilters || {
      customerCountRange: [0, 10000],
      growthRateRange: [-50, 50],
      engagementRange: [0, 100],
      valueRange: [0, 10000000],
      types: [],
    },
  );

  const handleApply = () => {
    onApply(filters);
    onClose();
  };

  const handleReset = () => {
    setFilters({
      customerCountRange: [0, 10000],
      growthRateRange: [-50, 50],
      engagementRange: [0, 100],
      valueRange: [0, 10000000],
      types: [],
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t('cdp:segments.filters.advanced')}</DialogTitle>
          <DialogDescription>
            {t('cdp:segments.filters.advancedDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Customer Count Range */}
          <div className="space-y-2">
            <Label>{t('cdp:segments.filters.customerCount')}</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-muted-foreground text-xs">Min</Label>
                <Input
                  type="number"
                  min={0}
                  max={10000}
                  step={100}
                  value={filters.customerCountRange[0]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      customerCountRange: [
                        parseInt(e.target.value) || 0,
                        filters.customerCountRange[1],
                      ],
                    })
                  }
                />
              </div>
              <div>
                <Label className="text-muted-foreground text-xs">Max</Label>
                <Input
                  type="number"
                  min={0}
                  max={10000}
                  step={100}
                  value={filters.customerCountRange[1]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      customerCountRange: [
                        filters.customerCountRange[0],
                        parseInt(e.target.value) || 10000,
                      ],
                    })
                  }
                />
              </div>
            </div>
          </div>

          {/* Growth Rate Range */}
          <div className="space-y-2">
            <Label>{t('cdp:segments.filters.growthRate')}</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-muted-foreground text-xs">Min (%)</Label>
                <Input
                  type="number"
                  min={-50}
                  max={50}
                  step={1}
                  value={filters.growthRateRange[0]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      growthRateRange: [
                        parseInt(e.target.value) || -50,
                        filters.growthRateRange[1],
                      ],
                    })
                  }
                />
              </div>
              <div>
                <Label className="text-muted-foreground text-xs">Max (%)</Label>
                <Input
                  type="number"
                  min={-50}
                  max={50}
                  step={1}
                  value={filters.growthRateRange[1]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      growthRateRange: [
                        filters.growthRateRange[0],
                        parseInt(e.target.value) || 50,
                      ],
                    })
                  }
                />
              </div>
            </div>
          </div>

          {/* Engagement Range */}
          <div className="space-y-2">
            <Label>{t('cdp:segments.filters.engagement')}</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-muted-foreground text-xs">Min (%)</Label>
                <Input
                  type="number"
                  min={0}
                  max={100}
                  step={5}
                  value={filters.engagementRange[0]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      engagementRange: [
                        parseInt(e.target.value) || 0,
                        filters.engagementRange[1],
                      ],
                    })
                  }
                />
              </div>
              <div>
                <Label className="text-muted-foreground text-xs">Max (%)</Label>
                <Input
                  type="number"
                  min={0}
                  max={100}
                  step={5}
                  value={filters.engagementRange[1]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      engagementRange: [
                        filters.engagementRange[0],
                        parseInt(e.target.value) || 100,
                      ],
                    })
                  }
                />
              </div>
            </div>
          </div>

          {/* Value Range */}
          <div className="space-y-2">
            <Label>{t('cdp:segments.filters.avgValue')}</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-muted-foreground text-xs">
                  Min (VND)
                </Label>
                <Input
                  type="number"
                  min={0}
                  max={10000000}
                  step={100000}
                  value={filters.valueRange[0]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      valueRange: [
                        parseInt(e.target.value) || 0,
                        filters.valueRange[1],
                      ],
                    })
                  }
                />
              </div>
              <div>
                <Label className="text-muted-foreground text-xs">
                  Max (VND)
                </Label>
                <Input
                  type="number"
                  min={0}
                  max={10000000}
                  step={100000}
                  value={filters.valueRange[1]}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      valueRange: [
                        filters.valueRange[0],
                        parseInt(e.target.value) || 10000000,
                      ],
                    })
                  }
                />
              </div>
            </div>
          </div>

          {/* Status Filters */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={filters.isActive}
                onCheckedChange={(checked) =>
                  setFilters({ ...filters, isActive: checked })
                }
              />
              <Label htmlFor="active">
                {t('cdp:segments.filters.activeOnly')}
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="autoUpdating"
                checked={filters.isAutoUpdating}
                onCheckedChange={(checked) =>
                  setFilters({ ...filters, isAutoUpdating: checked })
                }
              />
              <Label htmlFor="autoUpdating">
                {t('cdp:segments.filters.autoUpdating')}
              </Label>
            </div>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>{t('cdp:segments.filters.createdFrom')}</Label>
              <Input
                type="date"
                value={filters.createdDateRange?.from || ''}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    createdDateRange: {
                      ...filters.createdDateRange,
                      from: e.target.value,
                      to: filters.createdDateRange?.to || '',
                    },
                  })
                }
              />
            </div>

            <div className="space-y-2">
              <Label>{t('cdp:segments.filters.createdTo')}</Label>
              <Input
                type="date"
                value={filters.createdDateRange?.to || ''}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    createdDateRange: {
                      ...filters.createdDateRange,
                      from: filters.createdDateRange?.from || '',
                      to: e.target.value,
                    },
                  })
                }
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleReset}>
            {t('common:reset')}
          </Button>
          <Button variant="outline" onClick={onClose}>
            {t('common:cancel')}
          </Button>
          <Button onClick={handleApply}>{t('common:apply')}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
