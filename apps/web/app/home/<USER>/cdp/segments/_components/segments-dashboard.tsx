'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { toast } from 'sonner';
import { Plus, Users, Target, TrendingUp, Activity, MoreHorizontal, Edit, Trash2, Play, Pause } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@kit/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';

import { SegmentBuilder } from './segment-builder';

interface CustomerSegment {
  id: string;
  name: string;
  description?: string;
  color: string;
  customer_count: number;
  is_active: boolean;
  is_computing: boolean;
  type: 'static' | 'dynamic';
  created_at: string;
  updated_at: string;
  last_computed_at?: string;
}

interface SegmentsDashboardProps {
  accountId: string;
}

export function SegmentsDashboard({ accountId }: SegmentsDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const supabase = useSupabase();
  const [segments, setSegments] = useState<CustomerSegment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingSegment, setEditingSegment] = useState<CustomerSegment | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [segmentToDelete, setSegmentToDelete] = useState<string | null>(null);

  // Load segments
  const loadSegments = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('customer_segments')
        .select('*')
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setSegments(data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load segments';
      setError(errorMessage);
      toast.error(t('cdp:segments.loadError', errorMessage));
    } finally {
      setLoading(false);
    }
  };

  // Create segment
  const createSegment = async (segmentData: any) => {
    try {
      const { error } = await supabase
        .from('customer_segments')
        .insert({
          account_id: accountId,
          ...segmentData
        });

      if (error) {
        throw error;
      }

      toast.success(t('cdp:success.created', 'Segment created successfully'));
      setShowBuilder(false);
      await loadSegments();
    } catch (error) {
      console.error('Failed to create segment:', error);
      toast.error(t('cdp:segments.createError', 'Failed to create segment'));
      throw error;
    }
  };

  // Update segment
  const updateSegment = async (segmentId: string, updates: any) => {
    try {
      const { error } = await supabase
        .from('customer_segments')
        .update(updates)
        .eq('id', segmentId)
        .eq('account_id', accountId);

      if (error) {
        throw error;
      }

      toast.success(t('cdp:success.updated', 'Segment updated successfully'));
      setEditingSegment(null);
      await loadSegments();
    } catch (error) {
      console.error('Failed to update segment:', error);
      toast.error(t('cdp:segments.updateError', 'Failed to update segment'));
      throw error;
    }
  };

  // Delete segment
  const handleDeleteSegment = (segmentId: string) => {
    setSegmentToDelete(segmentId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteSegment = async () => {
    if (!segmentToDelete) return;

    try {
      const { error } = await supabase
        .from('customer_segments')
        .delete()
        .eq('id', segmentToDelete)
        .eq('account_id', accountId);

      if (error) {
        throw error;
      }

      toast.success(t('cdp:success.deleted', 'Segment deleted successfully'));
      await loadSegments();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete segment';
      setError(errorMessage);
      toast.error(t('cdp:segments.deleteError', errorMessage));
    } finally {
      setDeleteDialogOpen(false);
      setSegmentToDelete(null);
    }
  };

  // Toggle segment active status
  const toggleSegmentStatus = async (segment: CustomerSegment) => {
    await updateSegment(segment.id, { is_active: !segment.is_active });
  };

  useEffect(() => {
    loadSegments();
  }, []);

  // Calculate dashboard stats
  const stats = {
    totalSegments: segments.length,
    activeSegments: segments.filter(s => s.is_active).length,
    totalCustomers: segments.reduce((sum, s) => sum + s.customer_count, 0),
    computingSegments: segments.filter(s => s.is_computing).length,
  };

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Segments</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSegments}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeSegments} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Segmented Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCustomers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Across all segments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Segments</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeSegments}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Computing</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.computingSegments}</div>
            <p className="text-xs text-muted-foreground">
              Being processed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Segments</h2>
          <p className="text-muted-foreground">
            Create and manage dynamic customer segments based on behavior and attributes
          </p>
        </div>
        <Dialog open={showBuilder} onOpenChange={setShowBuilder}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Segment
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Segment</DialogTitle>
            </DialogHeader>
            <SegmentBuilder
              onSave={createSegment}
              onCancel={() => setShowBuilder(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Segments List */}
      <Card>
        <CardHeader>
          <CardTitle>All Segments</CardTitle>
          <CardDescription>
            Manage your customer segments and their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : segments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No segments found. Create your first segment to get started.
            </div>
          ) : (
            <div className="space-y-4">
              {segments.map((segment) => (
                <div
                  key={segment.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-center gap-4">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: segment.color }}
                    />
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{segment.name}</h3>
                        <Badge variant={segment.is_active ? 'default' : 'secondary'}>
                          {segment.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {segment.is_computing && (
                          <Badge variant="outline">Computing...</Badge>
                        )}
                        <Badge variant="outline">{segment.type}</Badge>
                      </div>
                      {segment.description && (
                        <p className="text-sm text-muted-foreground">
                          {segment.description}
                        </p>
                      )}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{segment.customer_count.toLocaleString()} customers</span>
                        <span>Updated {new Date(segment.updated_at).toLocaleDateString()}</span>
                        {segment.last_computed_at && (
                          <span>Computed {new Date(segment.last_computed_at).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setEditingSegment(segment)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => toggleSegmentStatus(segment)}>
                        {segment.is_active ? (
                          <>
                            <Pause className="mr-2 h-4 w-4" />
                            Deactivate
                          </>
                        ) : (
                          <>
                            <Play className="mr-2 h-4 w-4" />
                            Activate
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteSegment(segment.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Segment Dialog */}
      <Dialog open={!!editingSegment} onOpenChange={() => setEditingSegment(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Segment</DialogTitle>
          </DialogHeader>
          {editingSegment && (
            <SegmentBuilder
              initialData={editingSegment}
              onSave={(data) => updateSegment(editingSegment.id, data)}
              onCancel={() => setEditingSegment(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('cdp:segments.confirmDelete', 'Delete Segment')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('cdp:segments.confirmDeleteDescription', 'Are you sure you want to delete this segment? This action cannot be undone and will remove all associated data.')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common:cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteSegment}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t('common:delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
