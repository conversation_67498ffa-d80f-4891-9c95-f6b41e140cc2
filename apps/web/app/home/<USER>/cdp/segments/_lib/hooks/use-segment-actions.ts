'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import {
  createSegment,
  updateSegment,
  deleteSegment,
  refreshSegmentData,
  exportSegments,
} from '../server/segment-actions';

interface UseSegmentActionsProps {
  accountId: string;
  accountSlug: string;
}

export function useSegmentActions({ accountId, accountSlug }: UseSegmentActionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleCreateSegment = async (segmentData: any) => {
    setIsLoading(true);
    try {
      const result = await createSegment({
        accountId,
        accountSlug,
        segmentData,
      });

      toast.success(result.message);
      router.refresh();
      return result;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to create segment');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateSegment = async (segmentId: string, segmentData: any) => {
    setIsLoading(true);
    try {
      const result = await updateSegment({
        segmentId,
        accountId,
        accountSlug,
        segmentData,
      });

      toast.success(result.message);
      router.refresh();
      return result;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to update segment');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSegment = async (segmentId: string) => {
    setIsLoading(true);
    try {
      const result = await deleteSegment({
        segmentId,
        accountId,
        accountSlug,
      });

      toast.success(result.message);
      router.refresh();
      return result;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete segment');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshSegment = async (segmentId: string) => {
    setIsLoading(true);
    try {
      const result = await refreshSegmentData({
        segmentId,
        accountId,
        accountSlug,
      });

      toast.success(result.message);
      router.refresh();
      return result;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to refresh segment');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportSegments = async () => {
    setIsLoading(true);
    try {
      const result = await exportSegments({
        accountId,
        accountSlug,
      });

      // Create and download CSV file
      const blob = new Blob([result.csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(result.message);
      return result;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to export segments');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewSegmentDetails = (segmentId: string) => {
    router.push(`/home/<USER>/cdp/segments/${segmentId}`);
  };

  const handleCreateCampaign = (segmentId: string) => {
    // Navigate to campaign creation with segment pre-selected
    router.push(`/home/<USER>/marketing/campaigns/create?segment=${segmentId}`);
  };

  const handleAnalyzeSegment = (segmentId: string) => {
    // Navigate to segment analysis page
    router.push(`/home/<USER>/cdp/segments/${segmentId}/analysis`);
  };

  return {
    isLoading,
    handleCreateSegment,
    handleUpdateSegment,
    handleDeleteSegment,
    handleRefreshSegment,
    handleExportSegments,
    handleViewSegmentDetails,
    handleCreateCampaign,
    handleAnalyzeSegment,
  };
}
