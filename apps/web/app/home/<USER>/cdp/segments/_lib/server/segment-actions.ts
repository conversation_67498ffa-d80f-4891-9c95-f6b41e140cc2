'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

interface CreateSegmentParams {
  accountId: string;
  accountSlug: string;
  segmentData: {
    name: string;
    description: string;
    type: 'behavioral' | 'demographic' | 'value_based' | 'predictive';
    criteria: Record<string, any>;
    is_auto_updating?: boolean;
  };
}

interface UpdateSegmentParams {
  segmentId: string;
  accountId: string;
  accountSlug: string;
  segmentData: Partial<CreateSegmentParams['segmentData']>;
}

interface DeleteSegmentParams {
  segmentId: string;
  accountId: string;
  accountSlug: string;
}

export async function createSegment({
  accountId,
  accountSlug,
  segmentData,
}: CreateSegmentParams) {
  const client = getSupabaseServerClient();

  try {
    const { data: newSegment, error } = await client
      .from('customer_segments')
      .insert({
        account_id: accountId,
        name: segmentData.name,
        description: segmentData.description,
        type: segmentData.type,
        criteria: segmentData.criteria,
        is_auto_updating: segmentData.is_auto_updating || false,
        is_active: true,
        created_by: (await client.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    revalidatePath(`/home/<USER>/cdp/segments`);

    return {
      success: true,
      segment: newSegment,
      message: 'Segment created successfully',
    };
  } catch (error) {
    console.error('Error creating segment:', error);
    throw new Error(
      `Failed to create segment: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateSegment({
  segmentId,
  accountId,
  accountSlug,
  segmentData,
}: UpdateSegmentParams) {
  const client = getSupabaseServerClient();

  try {
    const { data: updatedSegment, error } = await client
      .from('customer_segments')
      .update({
        ...segmentData,
        updated_at: new Date().toISOString(),
        updated_by: (await client.auth.getUser()).data.user?.id,
      })
      .eq('id', segmentId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    revalidatePath(`/home/<USER>/cdp/segments`);

    return {
      success: true,
      segment: updatedSegment,
      message: 'Segment updated successfully',
    };
  } catch (error) {
    console.error('Error updating segment:', error);
    throw new Error(
      `Failed to update segment: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteSegment({
  segmentId,
  accountId,
  accountSlug,
}: DeleteSegmentParams) {
  const client = getSupabaseServerClient();

  try {
    const { error } = await client
      .from('customer_segments')
      .delete()
      .eq('id', segmentId)
      .eq('account_id', accountId);

    if (error) {
      throw error;
    }

    revalidatePath(`/home/<USER>/cdp/segments`);

    return {
      success: true,
      message: 'Segment deleted successfully',
    };
  } catch (error) {
    console.error('Error deleting segment:', error);
    throw new Error(
      `Failed to delete segment: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function refreshSegmentData({
  segmentId,
  accountId,
  accountSlug,
}: {
  segmentId: string;
  accountId: string;
  accountSlug: string;
}) {
  const client = getSupabaseServerClient();

  try {
    // Simulate segment data refresh by updating customer count
    const { data: segment, error: getError } = await client
      .from('customer_segments')
      .select('*')
      .eq('id', segmentId)
      .eq('account_id', accountId)
      .single();

    if (getError || !segment) {
      throw new Error('Segment not found');
    }

    // Simulate recalculating customer count based on criteria
    const newCustomerCount = Math.floor(Math.random() * 1000) + 100;
    const newGrowthRate = (Math.random() - 0.5) * 20; // -10% to +10%

    const { error: updateError } = await client
      .from('customer_segments')
      .update({
        customer_count: newCustomerCount,
        growth_rate: newGrowthRate,
        updated_at: new Date().toISOString(),
      })
      .eq('id', segmentId);

    if (updateError) {
      throw updateError;
    }

    revalidatePath(`/home/<USER>/cdp/segments`);

    return {
      success: true,
      message: 'Segment data refreshed successfully',
    };
  } catch (error) {
    console.error('Error refreshing segment data:', error);
    throw new Error(
      `Failed to refresh segment data: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function exportSegments({
  accountId,
  accountSlug,
}: {
  accountId: string;
  accountSlug: string;
}) {
  const client = getSupabaseServerClient();

  try {
    const { data: segments, error } = await client
      .from('customer_segments')
      .select('*')
      .eq('account_id', accountId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Convert to CSV format
    const csvHeaders = [
      'Name',
      'Description',
      'Type',
      'Customer Count',
      'Growth Rate',
      'Is Active',
      'Auto Updating',
      'Created At',
    ];

    const csvRows = segments.map((segment) => [
      segment.name,
      segment.description,
      segment.type,
      segment.customer_count,
      segment.growth_rate,
      segment.is_active ? 'Yes' : 'No',
      segment.is_auto_updating ? 'Yes' : 'No',
      new Date(segment.created_at).toLocaleDateString(),
    ]);

    const csvContent = [csvHeaders, ...csvRows]
      .map((row) => row.map((field) => `"${field}"`).join(','))
      .join('\n');

    return {
      success: true,
      csvContent,
      filename: `segments-${new Date().toISOString().split('T')[0]}.csv`,
      message: 'Segments exported successfully',
    };
  } catch (error) {
    console.error('Error exporting segments:', error);
    throw new Error(
      `Failed to export segments: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
