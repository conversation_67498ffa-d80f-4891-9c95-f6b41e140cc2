import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

import { AchievementEngine } from '~/app/home/<USER>/education/games/_lib/server/achievement-engine';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      sessionId,
      accountId,
      studentId,
      gameId,
      score,
      maxScore,
      accuracy,
      duration,
      gameType,
    } = body;

    // Validate required fields
    if (!sessionId || !accountId || !studentId || !gameId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create mock session object for achievement engine
    const gameSession = {
      id: sessionId,
      game_id: gameId,
      student_id: studentId,
      score: score || 0,
      max_score: maxScore || 100,
      accuracy_percentage: accuracy || 0,
      duration_seconds: duration || 0,
      completed_at: new Date().toISOString(),
      game: {
        game_type: gameType || 'quiz',
      },
    };

    // Initialize achievement engine and check achievements
    const achievementEngine = new AchievementEngine();
    const newAchievements = await achievementEngine.checkAchievements(
      gameSession,
      accountId
    );

    // Get achievement details for response
    const achievementDetails = [];
    if (newAchievements.length > 0) {
      const { data: achievements } = await supabase
        .from('achievements')
        .select('id, title, description, icon, badge_color, points_reward')
        .in('id', newAchievements.map(a => a.achievement_id));

      achievementDetails.push(...(achievements || []));
    }

    return NextResponse.json(achievementDetails);
  } catch (error) {
    console.error('Error in check-achievements API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
