/**
 * API Route: /api/miniapp/theme/save-config
 * Saves miniapp theme config and generates CSS using TailwindCSS CLI
 * Implements <700ms response time with MD5 caching
 */

import { NextRequest, NextResponse } from 'next/server';
import { createHash } from 'crypto';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';

// Import TailwindCSS service for server-side CSS generation
import { tailwindCSSService } from '../../lib/tailwind-css-service';

interface SaveConfigRequest {
  config: any; // Puck data config
  accountId: string;
  themeId?: string;
  editThemeId?: string;
  tempThemeId?: string;
}

interface SaveConfigResponse {
  success: boolean;
  data?: {
    configId: string;
    cssUrl: string;
    hash: string;
    size: number;
  };
  error?: string;
}

export const POST = enhanceRouteHandler(
  async ({ request }: { request: NextRequest }) => {
    const logger = await getLogger();
    const startTime = Date.now();

    try {
      // Parse request body
      const body: SaveConfigRequest = await request.json();
      const { config, accountId, themeId, editThemeId, tempThemeId } = body;

      if (!config || !accountId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields: config, accountId',
          } as SaveConfigResponse,
          { status: 400 }
        );
      }

      logger.info({ accountId, themeId, editThemeId, tempThemeId }, 'Saving miniapp theme config');

      // Generate MD5 hash of config for caching
      const configHash = createHash('md5')
        .update(JSON.stringify(config))
        .digest('hex');

      const supabase = getSupabaseServerClient();

      // Check if we already have CSS for this config hash
      let existingCSS: string | null = null;
      
      // First check temp_themes table
      if (tempThemeId) {
        const { data: tempTheme } = await supabase
          .from('temp_themes')
          .select('config')
          .eq('id', tempThemeId)
          .maybeSingle();

        if (tempTheme?.config?.cssText && tempTheme?.config?.configHash === configHash) {
          existingCSS = tempTheme.config.cssText;
          logger.info({ configHash }, 'Cache hit: Using existing CSS from temp_themes');
        }
      }

      // If not found in temp_themes, check account_themes
      if (!existingCSS && (themeId || editThemeId)) {
        const targetThemeId = editThemeId || themeId;
        const { data: accountTheme } = await supabase
          .from('account_themes')
          .select('config')
          .eq('id', targetThemeId)
          .maybeSingle();

        if (accountTheme?.config?.cssText && accountTheme?.config?.configHash === configHash) {
          existingCSS = accountTheme.config.cssText;
          logger.info({ configHash }, 'Cache hit: Using existing CSS from account_themes');
        }
      }

      let cssText = existingCSS;
      let cssSize = 0;

      // Generate CSS if not cached
      if (!cssText) {
        logger.info({ configHash }, 'Cache miss: Generating new CSS');
        
        try {
          const cssResult = await tailwindCSSService.generateCSSFromPuckData(config);
          cssText = cssResult.css;
          cssSize = cssResult.size;
          
          logger.info(
            { 
              configHash, 
              classCount: cssResult.classes.length, 
              cssSize: cssSize,
              generationTime: Date.now() - startTime 
            }, 
            'CSS generated successfully'
          );
        } catch (error) {
          logger.error({ error: error.message }, 'CSS generation failed');
          return NextResponse.json(
            {
              success: false,
              error: 'Failed to generate CSS',
            } as SaveConfigResponse,
            { status: 500 }
          );
        }
      } else {
        cssSize = Buffer.byteLength(cssText, 'utf8');
      }

      // Prepare config object with CSS data
      const configWithCSS = {
        ...config,
        cssText,
        configHash,
        generatedAt: new Date().toISOString(),
      };

      // Save to database
      let savedConfigId: string;

      if (tempThemeId) {
        // Update existing temp theme
        const { data: updatedTempTheme, error: updateError } = await supabase
          .from('temp_themes')
          .update({
            config: configWithCSS,
            updated_at: new Date().toISOString(),
          })
          .eq('id', tempThemeId)
          .eq('account_id', accountId)
          .select('id')
          .single();

        if (updateError) {
          logger.error({ error: updateError }, 'Failed to update temp theme');
          return NextResponse.json(
            {
              success: false,
              error: 'Failed to save theme config',
            } as SaveConfigResponse,
            { status: 500 }
          );
        }

        savedConfigId = updatedTempTheme.id;
      } else {
        // Create new temp theme
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours expiry

        const { data: newTempTheme, error: insertError } = await supabase
          .from('temp_themes')
          .insert({
            account_id: accountId,
            account_theme_id: editThemeId || null,
            theme_id: themeId || null,
            config: configWithCSS,
            preview_token: `miniapp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            expires_at: expiresAt.toISOString(),
          })
          .select('id')
          .single();

        if (insertError) {
          logger.error({ error: insertError }, 'Failed to create temp theme');
          return NextResponse.json(
            {
              success: false,
              error: 'Failed to save theme config',
            } as SaveConfigResponse,
            { status: 500 }
          );
        }

        savedConfigId = newTempTheme.id;
      }

      // Generate CSS URL (static file path)
      const cssUrl = `/api/miniapp/theme/css/${savedConfigId}.css`;

      const totalTime = Date.now() - startTime;
      logger.info(
        { 
          configId: savedConfigId, 
          configHash, 
          cssSize, 
          totalTime,
          cached: !!existingCSS 
        }, 
        'Miniapp theme config saved successfully'
      );

      return NextResponse.json({
        success: true,
        data: {
          configId: savedConfigId,
          cssUrl,
          hash: configHash,
          size: cssSize,
        },
      } as SaveConfigResponse);

    } catch (error: any) {
      const totalTime = Date.now() - startTime;
      logger.error(
        { error: error.message, totalTime }, 
        'Save miniapp config API error'
      );

      return NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
        } as SaveConfigResponse,
        { status: 500 }
      );
    }
  }
);

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
