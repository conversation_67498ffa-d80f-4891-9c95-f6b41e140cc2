import { enhanceRouteHand<PERSON> } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { createCorsResponse } from '~/lib/cors';
import { z } from 'zod';

const CheckPaymentStatusSchema = z.object({
  accountId: z.string().uuid()
});

/**
 * API endpoint to check payment status for Mini App
 * This checks if a temporary account has been converted to a real business
 */
export const POST = enhanceRouteHandler(
  async ({ request, body }) => {
    const logger = await getLogger();
    const adminClient = getSupabaseServerAdminClient();

    try {
      const { accountId } = body;

      logger.info(
        { accountId },
        'Checking payment status for Mini App account'
      );

      // Check account status
      const { data: account, error: accountError } = await adminClient
        .from('accounts')
        .select('id, name, slug, public_data')
        .eq('id', accountId)
        .single();

      if (accountError || !account) {
        logger.error({ error: accountError }, 'Account not found');
        throw new Error('Account not found');
      }

      // Check if there's a subscription for this account
      const { data: subscription, error: subError } = await adminClient
        .from('subscriptions')
        .select('*')
        .eq('account_id', accountId)
        .single();

      if (subError && subError.code !== 'PGRST116') { // PGRST116 = no rows found
        logger.error({ error: subError }, 'Error checking subscription');
        throw new Error('Failed to check subscription status');
      }

      // Check account status from public_data
      const accountStatus = account.public_data?.status || 'pending_payment';

      if (!subscription) {
        // No subscription found - check if it's a free plan or payment pending
        if (accountStatus === 'active') {
          // Free plan - already active
          return createCorsResponse(
            request,
            {
              success: true,
              data: {
                status: 'completed',
                businessData: {
                  id: account.id,
                  name: account.name,
                  slug: account.slug,
                  ...account.public_data
                }
              }
            },
            200
          );
        } else {
          // Payment still pending
          return createCorsResponse(
            request,
            {
              success: true,
              data: {
                status: 'pending',
                message: 'Payment is still being processed'
              }
            },
            200
          );
        }
      }

      // Check if the account has been updated to a real account ID
      if (!sessionId.startsWith('temp_')) {
        // This is already a real account ID, get business info
        const { data: account, error: accountError } = await adminClient
          .from('accounts')
          .select('id, name, slug, public_data')
          .eq('id', sessionId)
          .single();

        if (accountError) {
          logger.error({ error: accountError }, 'Error fetching account');
          throw new Error('Failed to fetch business info');
        }

        return createCorsResponse(
          request,
          {
            success: true,
            data: {
              status: 'completed',
              businessData: {
                id: account.id,
                name: account.name,
                slug: account.slug,
                ...account.public_data
              },
              subscription: {
                id: subscription.id,
                status: subscription.status,
                planId: subscription.plan_id
              }
            }
          },
          200
        );
      }

      // Check if subscription has been updated with real account ID
      if (subscription.account_id !== sessionId) {
        // Subscription has been updated with real account ID
        const { data: account, error: accountError } = await adminClient
          .from('accounts')
          .select('id, name, slug, public_data')
          .eq('id', subscription.account_id)
          .single();

        if (accountError) {
          logger.error({ error: accountError }, 'Error fetching updated account');
          throw new Error('Failed to fetch business info');
        }

        return createCorsResponse(
          request,
          {
            success: true,
            data: {
              status: 'completed',
              businessData: {
                id: account.id,
                name: account.name,
                slug: account.slug,
                ...account.public_data
              },
              subscription: {
                id: subscription.id,
                status: subscription.status,
                planId: subscription.plan_id
              }
            }
          },
          200
        );
      }

      // Subscription exists but still has temp account ID - payment successful but business not created yet
      return createCorsResponse(
        request,
        {
          success: true,
          data: {
            status: 'processing',
            message: 'Payment successful, creating business account...'
          }
        },
        200
      );

    } catch (error: any) {
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          sessionId: body?.sessionId
        },
        'Failed to check payment status'
      );

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Failed to check payment status',
          details: error.message
        },
        500
      );
    }
  },
  { 
    schema: CheckPaymentStatusSchema,
    auth: false 
  }
);
