import { NextResponse } from 'next/server';

import { requireUser } from '@kit/supabase/require-user';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

export async function POST(request: Request) {
  try {
    const supabase = getSupabaseServerAdminClient();

    // 1. <PERSON><PERSON><PERSON> thực user
    const auth = await requireUser(supabase);
    if (auth.error) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const user = auth.data;

    // 2. Lấy phone_token từ body request
    const { token } = await request.json();
    if (!token) {
      return NextResponse.json(
        { error: 'Missing phone_token' },
        { status: 400 },
      );
    }

    // 3. Lấy số điện thoại từ Zalo API
    const phoneResponse = await fetch('https://graph.zalo.me/v2.0/me/info', {
      method: 'GET',
      headers: {
        access_token: process.env.ZALO_APP_ID!, // Token từ server-side của <PERSON>
        code: token, // phone_token từ client
        secret_key: process.env.ZALO_SECRET_KEY!, // Secret key của Zalo app
      },
    });

    if (!phoneResponse.ok) {
      throw new Error(`Zalo Phone API failed: ${await phoneResponse.text()}`);
    }

    const phoneData = await phoneResponse.json();
    const phone = phoneData.data?.number;
    if (phoneData.error !== 0 || !phone) {
      throw new Error('Phone number not available');
    }

    // 4. Cập nhật phone trong user_metadata
    const { error: updateError } = await supabase.auth.admin.updateUserById(
      user.id,
      {
        user_metadata: { ...user.user_metadata, phone },
      },
    );

    if (updateError) {
      throw new Error(`Failed to update user: ${updateError.message}`);
    }

    // 5. Trả về thông tin user đã cập nhật
    return NextResponse.json({
      user: {
        id: user.id,
        name: user.user_metadata.name,
        phone,
        picture: user.user_metadata.picture,
      },
    });
  } catch (error) {
    console.error('Error in phone update:', error);
    return NextResponse.json({ error: error.message }, { status: 400 });
  }
}
