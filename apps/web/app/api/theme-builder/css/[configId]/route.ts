/**
 * API Route: /api/theme-builder/css/[configId]
 * Serves static CSS files for theme configs
 * Implements caching headers for optimal performance
 */

import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';

interface CSSRouteParams {
  configId: string;
}

export const GET = enhanceRouteHandler(
  async ({ request, params }: { request: NextRequest; params: CSSRouteParams }) => {
    const logger = await getLogger();
    const startTime = Date.now();

    try {
      const configId = params.configId.replace('.css', ''); // Remove .css extension
      
      if (!configId) {
        return new NextResponse('Config ID is required', { status: 400 });
      }

      logger.info({ configId }, 'Serving CSS file');

      const supabase = getSupabaseServerClient();
      let cssText: string | null = null;
      let configHash: string | null = null;

      // Try to load CSS from temp_themes first
      const { data: tempTheme } = await supabase
        .from('temp_themes')
        .select('css_text, config_hash')
        .eq('id', configId)
        .maybeSingle();

      if (tempTheme?.css_text) {
        cssText = tempTheme.css_text;
        configHash = tempTheme.config_hash;
        logger.info({ configId, source: 'temp_themes' }, 'CSS loaded from temp_themes');
      } else {
        // Try to load from account_themes
        const { data: accountTheme } = await supabase
          .from('account_themes')
          .select('css_text, config_hash')
          .eq('id', configId)
          .maybeSingle();

        if (accountTheme?.css_text) {
          cssText = accountTheme.css_text;
          configHash = accountTheme.config_hash;
          logger.info({ configId, source: 'account_themes' }, 'CSS loaded from account_themes');
        }
      }

      if (!cssText) {
        logger.warn({ configId }, 'CSS not found');
        return new NextResponse('CSS not found', { status: 404 });
      }

      const totalTime = Date.now() - startTime;
      logger.info(
        { 
          configId, 
          configHash, 
          cssSize: Buffer.byteLength(cssText, 'utf8'),
          totalTime 
        }, 
        'CSS served successfully'
      );

      // Return CSS with appropriate headers
      return new NextResponse(cssText, {
        status: 200,
        headers: {
          'Content-Type': 'text/css',
          'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
          'ETag': `"${configHash}"`,
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      });

    } catch (error: any) {
      const totalTime = Date.now() - startTime;
      logger.error(
        { error: error.message, totalTime }, 
        'CSS serve API error'
      );

      return new NextResponse('Internal server error', { status: 500 });
    }
  }
);

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
