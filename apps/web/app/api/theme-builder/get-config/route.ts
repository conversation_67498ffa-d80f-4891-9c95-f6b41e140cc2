/**
 * API Route: /api/theme-builder/get-config
 * Loads theme config and CSS with MD5 caching
 * Implements <300ms response time (cache hit ~50ms)
 */

import { NextRequest, NextResponse } from 'next/server';
import { createHash } from 'crypto';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';

// Import TailwindCSS service for server-side CSS generation
import { tailwindCSSService } from '../../../lib/tailwind-css-service';

interface GetConfigResponse {
  success: boolean;
  data?: {
    config: any;
    cssText: string;
    hash: string;
    size: number;
    cached: boolean;
  };
  error?: string;
}

export const GET = enhanceRouteHandler(
  async ({ request }: { request: NextRequest }) => {
    const logger = await getLogger();
    const startTime = Date.now();

    try {
      const url = new URL(request.url);
      const configId = url.searchParams.get('configId');
      const accountId = url.searchParams.get('accountId');

      if (!configId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required parameter: configId',
          } as GetConfigResponse,
          { status: 400 }
        );
      }

      logger.info({ configId, accountId }, 'Loading theme config');

      const supabase = getSupabaseServerClient();
      let config: any = null;
      let cssText: string | null = null;
      let configHash: string | null = null;
      let cached = false;
      let sourceTable: string = '';

      // Try to load from temp_themes first
      const { data: tempTheme } = await supabase
        .from('temp_themes')
        .select('config, account_id')
        .eq('id', configId)
        .maybeSingle();

      if (tempTheme) {
        // Verify account access if accountId provided
        if (accountId && tempTheme.account_id !== accountId) {
          return NextResponse.json(
            {
              success: false,
              error: 'Access denied',
            } as GetConfigResponse,
            { status: 403 }
          );
        }

        config = tempTheme.config;
        cssText = tempTheme.config?.cssText;
        configHash = tempTheme.config?.configHash;
        sourceTable = 'temp_themes';

        logger.info({ configId, source: 'temp_themes' }, 'Config loaded from temp_themes');
      } else {
        // Try to load from account_themes
        const { data: accountTheme } = await supabase
          .from('account_themes')
          .select('config, account_id')
          .eq('id', configId)
          .maybeSingle();

        if (accountTheme) {
          // Verify account access if accountId provided
          if (accountId && accountTheme.account_id !== accountId) {
            return NextResponse.json(
              {
                success: false,
                error: 'Access denied',
              } as GetConfigResponse,
              { status: 403 }
            );
          }

          config = accountTheme.config;
          cssText = accountTheme.config?.cssText;
          configHash = accountTheme.config?.configHash;
          sourceTable = 'account_themes';

          logger.info({ configId, source: 'account_themes' }, 'Config loaded from account_themes');
        }
      }

      if (!config) {
        return NextResponse.json(
          {
            success: false,
            error: 'Config not found',
          } as GetConfigResponse,
          { status: 404 }
        );
      }

      // Generate current config hash (excluding CSS data for comparison)
      const configForHashing = { ...config };
      delete configForHashing.cssText;
      delete configForHashing.configHash;
      delete configForHashing.generatedAt;

      const currentConfigHash = createHash('md5')
        .update(JSON.stringify(configForHashing))
        .digest('hex');

      // Check if CSS is valid and up-to-date
      if (cssText && configHash === currentConfigHash) {
        // Cache hit - CSS is up-to-date
        cached = true;
        logger.info({ configId, configHash }, 'Cache hit: Using existing CSS');
      } else {
        // Cache miss - need to regenerate CSS
        cached = false;
        logger.info({ configId, configHash, currentConfigHash }, 'Cache miss: Regenerating CSS');

        try {
          const cssResult = await tailwindCSSService.generateCSSFromPuckData(configForHashing);
          cssText = cssResult.css;
          configHash = currentConfigHash;

          // Update config with new CSS data
          const updatedConfig = {
            ...config,
            cssText,
            configHash,
            generatedAt: new Date().toISOString(),
          };

          // Update database with new CSS
          if (sourceTable === 'temp_themes') {
            await supabase
              .from('temp_themes')
              .update({
                config: updatedConfig,
                updated_at: new Date().toISOString(),
              })
              .eq('id', configId);
          } else {
            await supabase
              .from('account_themes')
              .update({
                config: updatedConfig,
                updated_at: new Date().toISOString(),
              })
              .eq('id', configId);
          }

          logger.info(
            {
              configId,
              configHash,
              classCount: cssResult.classes.length,
              cssSize: cssResult.size,
              generationTime: Date.now() - startTime
            },
            'CSS regenerated and saved'
          );
        } catch (error) {
          logger.error({ error: error.message }, 'CSS regeneration failed');
          return NextResponse.json(
            {
              success: false,
              error: 'Failed to generate CSS',
            } as GetConfigResponse,
            { status: 500 }
          );
        }
      }

      const cssSize = Buffer.byteLength(cssText!, 'utf8');
      const totalTime = Date.now() - startTime;

      logger.info(
        { 
          configId, 
          configHash, 
          cssSize, 
          totalTime,
          cached 
        }, 
        'Config loaded successfully'
      );

      return NextResponse.json({
        success: true,
        data: {
          config,
          cssText: cssText!,
          hash: configHash!,
          size: cssSize,
          cached,
        },
      } as GetConfigResponse);

    } catch (error: any) {
      const totalTime = Date.now() - startTime;
      logger.error(
        { error: error.message, totalTime }, 
        'Get config API error'
      );

      return NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
        } as GetConfigResponse,
        { status: 500 }
      );
    }
  }
);

// CSS endpoint to serve static CSS files
export const dynamic = 'force-dynamic';

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
