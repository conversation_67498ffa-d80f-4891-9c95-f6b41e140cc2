import { NextResponse } from 'next/server';

import axios from 'axios';
import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

// Schema for request body
const LocationRequestSchema = z.object({
  code: z.string(), // OAuth code from Zalo Mini App (required)
  token: z.string(), // Access token from Zalo SDK (required)
  theme_id: z.string().optional(), // Theme ID is optional
});

/**
 * Enhanced location retrieval API with optional account_id support
 *
 * OA Configuration Priority:
 * 1. Theme-specific configuration (if theme_id provided)
 * 2. System default configuration (is_system_default = true)
 * 3. Account-specific configuration (if account_id available)
 * 4. Any available configuration (fallback)
 *
 * This allows the API to work in multiple scenarios:
 * - With account context (normal authenticated users)
 * - Without account context (guest users, public access)
 * - With theme-specific branding (mini app themes)
 */

export const POST = enhanceRouteHandler(
  async ({ request, user, supabase }) => {
    const logger = await getLogger();
    const adminClient = getSupabaseServerAdminClient();

    try {
      // Parse request body manually
      const body = await request.json();

      // Parse and validate request body
      const { code, token, theme_id } = LocationRequestSchema.parse(body);

      // Log the request for debugging
      logger.info(
        {
          code: code ? 'provided' : 'missing',
          token: token ? 'provided' : 'missing',
          theme_id: theme_id || 'not_provided',
          user_id: user.id,
        },
        'Processing location request',
      );

      // Get OA configuration - simplified logic to get secret_key
      let oaConfig;

      // Try to get system default OA configuration first
      const { data: defaultOaConfig } = await adminClient
        .from('oa_configurations')
        .select('*')
        .eq('is_system_default', true)
        .single();

      if (defaultOaConfig?.secret_key) {
        oaConfig = defaultOaConfig;
      } else {
        // Fallback to any available configuration
        const { data: anyOaConfig } = await adminClient
          .from('oa_configurations')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (!anyOaConfig?.secret_key) {
          return NextResponse.json(
            {
              success: false,
              error: 'OA configuration not found',
            },
            { status: 404 },
          );
        }

        oaConfig = anyOaConfig;
      }

      // Get location using the Zalo API
      const locationResponse = await axios.get(
        'https://graph.zalo.me/v2.0/me/info',
        {
          headers: {
            access_token: token,
            code: code,
            secret_key: oaConfig.secret_key,
          },
        },
      );

      // Check Zalo API response
      if (
        locationResponse.status !== 200 ||
        locationResponse.data.error !== 0
      ) {
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to get location from Zalo',
            details: locationResponse.data,
          },
          { status: 500 },
        );
      }

      const locationData = locationResponse.data.data;

      if (!locationData || !locationData.latitude || !locationData.longitude) {
        return NextResponse.json(
          {
            success: false,
            error: 'Location data not available',
            details: locationResponse.data,
          },
          { status: 400 },
        );
      }

      // Return the location data
      return NextResponse.json({
        success: true,
        data: {
          location: {
            latitude: locationData.latitude,
            longitude: locationData.longitude,
            provider: locationData.provider,
            timestamp: locationData.timestamp,
          },
        },
      });
    } catch (error: any) {
      logger.error(
        { error: error.message },
        'Error getting location from Zalo',
      );

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 },
        );
      }

      return NextResponse.json(
        { success: false, error: 'Internal server error' },
        { status: 500 },
      );
    }
  },
  {
    auth: true,
  },
);
