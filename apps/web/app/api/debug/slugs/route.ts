import { enhanceRouteHand<PERSON> } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { createCorsResponse } from '~/lib/cors';

/**
 * Debug API to check existing slugs
 */
export const GET = enhanceRouteHandler(
  async ({ request }) => {
    const logger = await getLogger();
    const adminClient = getSupabaseServerAdminClient();

    try {
      // Get all account slugs
      const { data: accounts, error } = await adminClient
        .from('accounts')
        .select('id, name, slug, created_at')
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        throw error;
      }

      const response = {
        success: true,
        data: {
          accounts: accounts || [],
          count: accounts?.length || 0
        },
        message: 'Slugs retrieved successfully'
      };

      return createCorsResponse(request, response, 200);

    } catch (error: any) {
      logger.error(
        {
          error: error.message,
          stack: error.stack
        },
        'Failed to retrieve slugs'
      );

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Failed to retrieve slugs',
          details: error.message
        },
        500
      );
    }
  },
  { 
    auth: false 
  }
);

/**
 * Check if a specific slug exists
 */
export const POST = enhanceRouteHandler(
  async ({ request, body }) => {
    const logger = await getLogger();
    const adminClient = getSupabaseServerAdminClient();

    try {
      const { slug } = body;

      if (!slug) {
        throw new Error('Slug is required');
      }

      // Check if slug exists
      const { data: existingAccount, error } = await adminClient
        .from('accounts')
        .select('id, name, slug, created_at')
        .eq('slug', slug)
        .single();

      const response = {
        success: true,
        data: {
          slug,
          exists: !!existingAccount,
          account: existingAccount || null
        },
        message: existingAccount ? 'Slug already exists' : 'Slug is available'
      };

      return createCorsResponse(request, response, 200);

    } catch (error: any) {
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          slug: body?.slug
        },
        'Failed to check slug'
      );

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Failed to check slug',
          details: error.message
        },
        500
      );
    }
  },
  { 
    auth: false 
  }
);
