-- Add CSS storage columns for TailwindCSS integration
-- Migration: 20250624000000_add_css_storage_columns.sql

-- Add columns to temp_themes table
ALTER TABLE public.temp_themes 
ADD COLUMN IF NOT EXISTS css_text TEXT,
ADD COLUMN IF NOT EXISTS config_hash VARCHAR(32);

-- Add columns to account_themes table  
ALTER TABLE public.account_themes
ADD COLUMN IF NOT EXISTS css_text TEXT,
ADD COLUMN IF NOT EXISTS config_hash VARCHAR(32);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_temp_themes_config_hash ON public.temp_themes(config_hash);
CREATE INDEX IF NOT EXISTS idx_account_themes_config_hash ON public.account_themes(config_hash);

-- Add comments
COMMENT ON COLUMN public.temp_themes.css_text IS 'Generated CSS text from TailwindCSS CLI';
COMMENT ON COLUMN public.temp_themes.config_hash IS 'MD5 hash of config for caching';
COMMENT ON COLUMN public.account_themes.css_text IS 'Generated CSS text from TailwindCSS CLI';
COMMENT ON COLUMN public.account_themes.config_hash IS 'MD5 hash of config for caching';

-- Update RLS policies to include new columns
-- temp_themes policies
DROP POLICY IF EXISTS "Users can view temp themes for their account" ON public.temp_themes;
CREATE POLICY "Users can view temp themes for their account" ON public.temp_themes
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM public.account_members 
      WHERE user_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Users can insert temp themes for their account" ON public.temp_themes;
CREATE POLICY "Users can insert temp themes for their account" ON public.temp_themes
  FOR INSERT WITH CHECK (
    account_id IN (
      SELECT account_id FROM public.account_members 
      WHERE user_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Users can update temp themes for their account" ON public.temp_themes;
CREATE POLICY "Users can update temp themes for their account" ON public.temp_themes
  FOR UPDATE USING (
    account_id IN (
      SELECT account_id FROM public.account_members 
      WHERE user_id = auth.uid()
    )
  );

-- account_themes policies  
DROP POLICY IF EXISTS "Users can view account themes for their account" ON public.account_themes;
CREATE POLICY "Users can view account themes for their account" ON public.account_themes
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM public.account_members 
      WHERE user_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Users can update account themes for their account" ON public.account_themes;
CREATE POLICY "Users can update account themes for their account" ON public.account_themes
  FOR UPDATE USING (
    account_id IN (
      SELECT account_id FROM public.account_members 
      WHERE user_id = auth.uid()
    )
  );
